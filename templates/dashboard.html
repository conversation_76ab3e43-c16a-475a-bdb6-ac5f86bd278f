{% extends "base.html" %}

{% block title %}لوحة التحكم - نظام إدارة المبيعات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <h1 class="h3 mb-4">
            <i class="fas fa-tachometer-alt me-2"></i>لوحة التحكم
            <small class="text-muted">مرحباً {{ current_user.username }}</small>
        </h1>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-primary border-4 h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-primary text-uppercase mb-1">إجمالي المنتجات</div>
                        <div class="h5 mb-0 fw-bold text-gray-800">{{ total_products }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-warning border-4 h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-warning text-uppercase mb-1">منتجات قليلة المخزون</div>
                        <div class="h5 mb-0 fw-bold text-gray-800">{{ low_stock_count }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-success border-4 h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-success text-uppercase mb-1">مبيعات اليوم</div>
                        <div class="h5 mb-0 fw-bold text-gray-800">{{ today_sales }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-shopping-cart fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-info border-4 h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-info text-uppercase mb-1">إجمالي العملاء</div>
                        <div class="h5 mb-0 fw-bold text-gray-800">{{ total_customers }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-start border-success border-4 h-100">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-success text-uppercase mb-1">الربح اليومي</div>
                        <div class="h5 mb-0 fw-bold text-gray-800" id="dailyProfitCard">
                            <span class="currency-lyd">0.000</span> د.ل
                            <span class="libya-flag ms-1"></span>
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- الإجراءات السريعة -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>الإجراءات السريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    {% if current_user.role in ['admin', 'seller'] %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('new_sale') }}" class="btn btn-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="fas fa-plus-circle fa-2x mb-2"></i>
                            <span>فاتورة جديدة</span>
                        </a>
                    </div>
                    {% endif %}

                    {% if current_user.role in ['admin', 'warehouse_manager'] %}
                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('add_product') }}" class="btn btn-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="fas fa-box fa-2x mb-2"></i>
                            <span>إضافة منتج</span>
                        </a>
                    </div>
                    {% endif %}

                    <div class="col-md-3 mb-3">
                        <a href="{{ url_for('customers') }}" class="btn btn-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="fas fa-user-plus fa-2x mb-2"></i>
                            <span>إضافة عميل</span>
                        </a>
                    </div>

                    <div class="col-md-3 mb-3">
                        <a href="#" class="btn btn-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3">
                            <i class="fas fa-chart-bar fa-2x mb-2"></i>
                            <span>التقارير</span>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تنبيهات المخزون -->
{% if low_stock_count > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="alert alert-warning" role="alert">
            <h5 class="alert-heading">
                <i class="fas fa-exclamation-triangle me-2"></i>تنبيه مخزون!
            </h5>
            <p class="mb-0">
                يوجد {{ low_stock_count }} منتج تحت الحد الأدنى للمخزون.
                <a href="{{ url_for('products') }}?low_stock=1" class="alert-link">عرض المنتجات</a>
            </p>
        </div>
    </div>
</div>
{% endif %}

<!-- آخر العمليات -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clock me-2"></i>آخر المبيعات
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ</th>
                                <th>التاريخ</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- سيتم إضافة البيانات هنا لاحقاً -->
                            <tr>
                                <td colspan="4" class="text-center text-muted">لا توجد مبيعات حديثة</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-chart-line me-2"></i>إحصائيات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6 border-end">
                        <div class="h4 mb-0 text-primary">0</div>
                        <small class="text-muted">مبيعات الأسبوع</small>
                    </div>
                    <div class="col-6">
                        <div class="h4 mb-0 text-success">0.000 د.ل <span class="libya-flag"></span></div>
                        <small class="text-muted">إيرادات الشهر</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// تحميل الربح اليومي
document.addEventListener('DOMContentLoaded', function() {
    loadDailyProfit();
});

async function loadDailyProfit() {
    try {
        const response = await fetch('/api/profits/daily');
        const result = await response.json();

        if (result.success) {
            const profitElement = document.getElementById('dailyProfitCard');
            if (profitElement) {
                const currencyElement = profitElement.querySelector('.currency-lyd');
                if (currencyElement) {
                    currencyElement.textContent = formatCurrency(result.data.total_profit);
                }
            }
        }
    } catch (error) {
        console.error('خطأ في تحميل الربح اليومي:', error);
    }
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-LY', {
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
    }).format(amount);
}
</script>
{% endblock %}
