{% extends "base.html" %}

{% block title %}تسوية المخزون{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-balance-scale text-warning"></i> تسوية المخزون</h2>
                <a href="{{ url_for('stock_movements') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> العودة لحركات المخزون
                </a>
            </div>

            <div class="row">
                <div class="col-md-8 mx-auto">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-edit"></i> تعديل كمية المنتج</h5>
                        </div>
                        <div class="card-body">
                            <form method="POST">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="product_id" class="form-label">المنتج *</label>
                                        <select class="form-select" id="product_id" name="product_id" required onchange="updateCurrentQuantity()">
                                            <option value="">اختر المنتج</option>
                                            {% for product in products %}
                                            <option value="{{ product.id }}" data-quantity="{{ product.stock_quantity }}" data-unit="{{ product.unit }}">
                                                {{ product.name }} - {{ product.barcode or 'بدون باركود' }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الكمية الحالية</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="current_quantity" readonly placeholder="اختر المنتج أولاً">
                                            <span class="input-group-text" id="current_unit">-</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="new_quantity" class="form-label">الكمية الجديدة *</label>
                                        <input type="number" class="form-control" id="new_quantity" name="new_quantity" 
                                               min="0" step="1" required onchange="calculateDifference()">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الفرق</label>
                                        <div class="input-group">
                                            <input type="text" class="form-control" id="difference" readonly>
                                            <span class="input-group-text" id="diff_unit">-</span>
                                        </div>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="notes" class="form-label">ملاحظات التسوية</label>
                                    <textarea class="form-control" id="notes" name="notes" rows="3" 
                                              placeholder="اذكر سبب التسوية (جرد، تلف، خطأ في الإدخال، إلخ...)"></textarea>
                                </div>

                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>تنبيه:</strong> تسوية المخزون ستؤثر على كمية المنتج المتاحة وستسجل كحركة مخزون.
                                </div>

                                <div class="text-center">
                                    <button type="submit" class="btn btn-warning btn-lg me-2" id="submitBtn" disabled>
                                        <i class="fas fa-balance-scale"></i> تنفيذ التسوية
                                    </button>
                                    <a href="{{ url_for('stock_movements') }}" class="btn btn-secondary btn-lg">
                                        <i class="fas fa-times"></i> إلغاء
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- جدول المنتجات للمرجع -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-header">
                            <h5><i class="fas fa-list"></i> قائمة المنتجات</h5>
                        </div>
                        <div class="card-body">
                            <div class="table-responsive">
                                <table class="table table-striped table-hover">
                                    <thead>
                                        <tr>
                                            <th>المنتج</th>
                                            <th>الباركود</th>
                                            <th>التصنيف</th>
                                            <th>الكمية الحالية</th>
                                            <th>الحد الأدنى</th>
                                            <th>الحالة</th>
                                            <th>إجراء</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for product in products %}
                                        <tr>
                                            <td>
                                                <strong>{{ product.name }}</strong>
                                                {% if product.description %}
                                                <br><small class="text-muted">{{ product.description }}</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if product.barcode %}
                                                <code>{{ product.barcode }}</code>
                                                {% else %}
                                                <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <span class="badge bg-secondary">{{ product.category.name }}</span>
                                            </td>
                                            <td>
                                                <span class="fw-bold">{{ product.stock_quantity }}</span> {{ product.unit }}
                                            </td>
                                            <td>{{ product.min_quantity }} {{ product.unit }}</td>
                                            <td>
                                                {% if product.stock_quantity > product.min_quantity %}
                                                <span class="badge bg-success">متوفر</span>
                                                {% elif product.stock_quantity > 0 %}
                                                <span class="badge bg-warning text-dark">منخفض</span>
                                                {% else %}
                                                <span class="badge bg-danger">نفد</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-outline-warning btn-sm" 
                                                        onclick="selectProduct({{ product.id }})">
                                                    <i class="fas fa-edit"></i> تسوية
                                                </button>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateCurrentQuantity() {
    const select = document.getElementById('product_id');
    const currentQuantityInput = document.getElementById('current_quantity');
    const currentUnitSpan = document.getElementById('current_unit');
    const newQuantityInput = document.getElementById('new_quantity');
    const submitBtn = document.getElementById('submitBtn');
    
    if (select.value) {
        const selectedOption = select.options[select.selectedIndex];
        const quantity = selectedOption.getAttribute('data-quantity');
        const unit = selectedOption.getAttribute('data-unit');
        
        currentQuantityInput.value = quantity;
        currentUnitSpan.textContent = unit;
        document.getElementById('diff_unit').textContent = unit;
        
        newQuantityInput.disabled = false;
        newQuantityInput.focus();
    } else {
        currentQuantityInput.value = '';
        currentUnitSpan.textContent = '-';
        document.getElementById('diff_unit').textContent = '-';
        newQuantityInput.disabled = true;
        newQuantityInput.value = '';
        submitBtn.disabled = true;
    }
    
    calculateDifference();
}

function calculateDifference() {
    const currentQuantity = parseFloat(document.getElementById('current_quantity').value) || 0;
    const newQuantity = parseFloat(document.getElementById('new_quantity').value) || 0;
    const differenceInput = document.getElementById('difference');
    const submitBtn = document.getElementById('submitBtn');
    
    const diff = newQuantity - currentQuantity;
    
    if (diff > 0) {
        differenceInput.value = `+${diff}`;
        differenceInput.className = 'form-control text-success fw-bold';
    } else if (diff < 0) {
        differenceInput.value = diff;
        differenceInput.className = 'form-control text-danger fw-bold';
    } else {
        differenceInput.value = '0';
        differenceInput.className = 'form-control text-muted';
    }
    
    // تفعيل زر الحفظ إذا كان هناك فرق
    submitBtn.disabled = (diff === 0 || !document.getElementById('product_id').value);
}

function selectProduct(productId) {
    const select = document.getElementById('product_id');
    select.value = productId;
    updateCurrentQuantity();
    
    // التمرير إلى النموذج
    document.querySelector('.card').scrollIntoView({ behavior: 'smooth' });
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateCurrentQuantity();
});
</script>
{% endblock %}
