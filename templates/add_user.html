{% extends "base.html" %}

{% block title %}إضافة مستخدم جديد - نظام إدارة المبيعات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد
            </h1>
            <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للمستخدمين
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>بيانات المستخدم الجديد
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- اسم المستخدم -->
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-1"></i>اسم المستخدم *
                            </label>
                            <input type="text" class="form-control" id="username" name="username" required>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم المستخدم
                            </div>
                            <small class="form-text text-muted">
                                يجب أن يكون فريداً ولا يحتوي على مسافات
                            </small>
                        </div>

                        <!-- البريد الإلكتروني -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>البريد الإلكتروني *
                            </label>
                            <input type="email" class="form-control" id="email" name="email" required>
                            <div class="invalid-feedback">
                                يرجى إدخال بريد إلكتروني صحيح
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <!-- كلمة المرور -->
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-1"></i>كلمة المرور *
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" 
                                       minlength="6" required>
                                <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                كلمة المرور يجب أن تكون 6 أحرف على الأقل
                            </div>
                            <small class="form-text text-muted">
                                يُفضل استخدام أحرف وأرقام ورموز
                            </small>
                        </div>

                        <!-- تأكيد كلمة المرور -->
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-lock me-1"></i>تأكيد كلمة المرور *
                            </label>
                            <input type="password" class="form-control" id="confirm_password" 
                                   name="confirm_password" required>
                            <div class="invalid-feedback">
                                كلمات المرور غير متطابقة
                            </div>
                        </div>
                    </div>

                    <!-- الدور -->
                    <div class="mb-4">
                        <label for="role" class="form-label">
                            <i class="fas fa-user-tag me-1"></i>الدور *
                        </label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="">اختر الدور</option>
                            <option value="admin">مدير - صلاحيات كاملة</option>
                            <option value="seller">بائع - إنشاء فواتير وعرض البيانات</option>
                            <option value="warehouse_manager">مشرف مخزن - إدارة المنتجات والمخزون</option>
                        </select>
                        <div class="invalid-feedback">
                            يرجى اختيار الدور
                        </div>
                    </div>

                    <!-- معلومات الأدوار -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات الأدوار:</h6>
                        <ul class="mb-0">
                            <li><strong>مدير:</strong> جميع الصلاحيات بما في ذلك إدارة المستخدمين والإعدادات</li>
                            <li><strong>بائع:</strong> إنشاء فواتير المبيعات وعرض البيانات الأساسية</li>
                            <li><strong>مشرف مخزن:</strong> إدارة المنتجات والمخزون وحركات المخزن</li>
                        </ul>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                        <div>
                            <a href="{{ url_for('users') }}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ المستخدم
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إظهار/إخفاء كلمة المرور
function togglePassword() {
    const passwordField = document.getElementById('password');
    const toggleIcon = document.getElementById('passwordToggleIcon');
    
    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// التحقق من تطابق كلمات المرور
function validatePasswordMatch() {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const confirmField = document.getElementById('confirm_password');
    
    if (password !== confirmPassword) {
        confirmField.setCustomValidity('كلمات المرور غير متطابقة');
        confirmField.classList.add('is-invalid');
        return false;
    } else {
        confirmField.setCustomValidity('');
        confirmField.classList.remove('is-invalid');
        return true;
    }
}

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
        document.querySelector('form').reset();
        document.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
        document.querySelectorAll('.is-valid').forEach(el => el.classList.remove('is-valid'));
    }
}

// التحقق من قوة كلمة المرور
function checkPasswordStrength(password) {
    let strength = 0;
    
    if (password.length >= 8) strength++;
    if (/[a-z]/.test(password)) strength++;
    if (/[A-Z]/.test(password)) strength++;
    if (/[0-9]/.test(password)) strength++;
    if (/[^A-Za-z0-9]/.test(password)) strength++;
    
    return strength;
}

// عرض مؤشر قوة كلمة المرور
function updatePasswordStrength() {
    const password = document.getElementById('password').value;
    const strength = checkPasswordStrength(password);
    
    // يمكن إضافة مؤشر بصري لقوة كلمة المرور هنا
}

// إضافة مستمعي الأحداث
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('confirm_password');
    
    // التحقق من تطابق كلمات المرور عند الكتابة
    confirmPasswordField.addEventListener('input', validatePasswordMatch);
    passwordField.addEventListener('input', function() {
        updatePasswordStrength();
        if (confirmPasswordField.value) {
            validatePasswordMatch();
        }
    });
    
    // التحقق من النموذج قبل الإرسال
    form.addEventListener('submit', function(e) {
        if (!form.checkValidity() || !validatePasswordMatch()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });
    
    // التحقق من اسم المستخدم (بدون مسافات)
    document.getElementById('username').addEventListener('input', function(e) {
        e.target.value = e.target.value.replace(/\s/g, '');
    });
});
</script>
{% endblock %}
