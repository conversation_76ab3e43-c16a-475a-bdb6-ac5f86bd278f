{% extends "base.html" %}

{% block title %}إدارة المبيعات - نظام إدارة المبيعات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-shopping-cart me-2"></i>إدارة المبيعات
            </h1>
            {% if current_user.role in ['admin', 'seller'] %}
            <a href="{{ url_for('new_sale') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>فاتورة جديدة
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-start border-success border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-success text-uppercase mb-1">مبيعات اليوم</div>
                        <div class="h5 mb-0 fw-bold">{{ invoices.total if invoices.total else 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-day fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-start border-primary border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-primary text-uppercase mb-1">إجمالي المبيعات</div>
                        <div class="h5 mb-0 fw-bold">0.00 ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-money-bill fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-warning text-uppercase mb-1">فواتير معلقة</div>
                        <div class="h5 mb-0 fw-bold">0</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-clock fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-start border-info border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-info text-uppercase mb-1">متوسط الفاتورة</div>
                        <div class="h5 mb-0 fw-bold">0.00 ر.س</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-3">
                        <div class="search-box">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="رقم الفاتورة أو العميل..." 
                                   value="{{ request.args.get('search', '') }}">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="payment_method">
                            <option value="">جميع طرق الدفع</option>
                            <option value="cash" {% if request.args.get('payment_method') == 'cash' %}selected{% endif %}>نقدي</option>
                            <option value="card" {% if request.args.get('payment_method') == 'card' %}selected{% endif %}>بطاقة</option>
                            <option value="credit" {% if request.args.get('payment_method') == 'credit' %}selected{% endif %}>آجل</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" name="start_date" 
                               value="{{ request.args.get('start_date', '') }}">
                    </div>
                    <div class="col-md-2">
                        <input type="date" class="form-control" name="end_date" 
                               value="{{ request.args.get('end_date', '') }}">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" name="status">
                            <option value="">جميع الحالات</option>
                            <option value="completed" {% if request.args.get('status') == 'completed' %}selected{% endif %}>مكتملة</option>
                            <option value="pending" {% if request.args.get('status') == 'pending' %}selected{% endif %}>معلقة</option>
                            <option value="cancelled" {% if request.args.get('status') == 'cancelled' %}selected{% endif %}>ملغية</option>
                        </select>
                    </div>
                    <div class="col-md-1">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-filter"></i>
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول الفواتير -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>قائمة الفواتير
                        <span class="badge bg-primary ms-2">{{ invoices.total }} فاتورة</span>
                    </h5>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-success" onclick="exportSales('excel')">
                            <i class="fas fa-file-excel me-1"></i>Excel
                        </button>
                        <button class="btn btn-outline-danger" onclick="exportSales('pdf')">
                            <i class="fas fa-file-pdf me-1"></i>PDF
                        </button>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>رقم الفاتورة</th>
                                <th>العميل</th>
                                <th>المبلغ الإجمالي</th>
                                <th>طريقة الدفع</th>
                                <th>الحالة</th>
                                <th>التاريخ</th>
                                <th>البائع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for invoice in invoices.items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="icon-circle bg-success text-white me-3">
                                            <i class="fas fa-receipt"></i>
                                        </div>
                                        <div>
                                            <strong>{{ invoice.invoice_number }}</strong>
                                            {% if invoice.notes %}
                                            <br><small class="text-muted">{{ invoice.notes[:30] }}...</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if invoice.customer %}
                                    <div>
                                        <strong>{{ invoice.customer.name }}</strong>
                                        {% if invoice.customer.phone %}
                                        <br><small class="text-muted">{{ invoice.customer.phone }}</small>
                                        {% endif %}
                                    </div>
                                    {% else %}
                                    <span class="text-muted">عميل نقدي</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>
                                        <strong class="text-success">{{ "%.2f"|format(invoice.final_amount) }} ر.س</strong>
                                        {% if invoice.discount > 0 %}
                                        <br><small class="text-warning">خصم: {{ "%.2f"|format(invoice.discount) }} ر.س</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if invoice.payment_method == 'cash' %}
                                    <span class="badge bg-success">نقدي</span>
                                    {% elif invoice.payment_method == 'card' %}
                                    <span class="badge bg-primary">بطاقة</span>
                                    {% elif invoice.payment_method == 'credit' %}
                                    <span class="badge bg-warning">آجل</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if invoice.status == 'completed' %}
                                    <span class="status-badge bg-success text-white">مكتملة</span>
                                    {% elif invoice.status == 'pending' %}
                                    <span class="status-badge bg-warning text-dark">معلقة</span>
                                    {% elif invoice.status == 'cancelled' %}
                                    <span class="status-badge bg-danger text-white">ملغية</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div>
                                        <strong>{{ invoice.created_at.strftime('%Y-%m-%d') }}</strong>
                                        <br><small class="text-muted">{{ invoice.created_at.strftime('%H:%M') }}</small>
                                    </div>
                                </td>
                                <td>
                                    <span class="text-muted">{{ invoice.user.username }}</span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-primary" title="عرض التفاصيل"
                                                onclick="viewInvoice({{ invoice.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <a href="{{ url_for('print_invoice', invoice_id=invoice.id) }}" 
                                           class="btn btn-outline-success" title="طباعة" target="_blank">
                                            <i class="fas fa-print"></i>
                                        </a>
                                        {% if current_user.role == 'admin' and invoice.status == 'completed' %}
                                        <button class="btn btn-outline-warning" title="مرتجع"
                                                onclick="returnInvoice({{ invoice.id }})">
                                            <i class="fas fa-undo"></i>
                                        </button>
                                        {% endif %}
                                        {% if current_user.role == 'admin' and invoice.status != 'cancelled' %}
                                        <button class="btn btn-outline-danger" title="إلغاء"
                                                onclick="cancelInvoice({{ invoice.id }})">
                                            <i class="fas fa-times"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-shopping-cart fa-3x mb-3"></i>
                                        <p>لا توجد فواتير</p>
                                        {% if current_user.role in ['admin', 'seller'] %}
                                        <a href="{{ url_for('new_sale') }}" class="btn btn-primary">
                                            إنشاء فاتورة جديدة
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- ترقيم الصفحات -->
            {% if invoices.pages > 1 %}
            <div class="card-footer">
                <nav aria-label="ترقيم الصفحات">
                    <ul class="pagination justify-content-center mb-0">
                        {% if invoices.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('sales', page=invoices.prev_num, **request.args) }}">
                                السابق
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in invoices.iter_pages() %}
                        {% if page_num %}
                        {% if page_num != invoices.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('sales', page=page_num, **request.args) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                        {% endfor %}
                        
                        {% if invoices.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('sales', page=invoices.next_num, **request.args) }}">
                                التالي
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// عرض تفاصيل الفاتورة
function viewInvoice(invoiceId) {
    // سيتم تنفيذها لاحقاً
    console.log('View invoice:', invoiceId);
}

// إرجاع فاتورة
function returnInvoice(invoiceId) {
    if (confirm('هل أنت متأكد من إرجاع هذه الفاتورة؟')) {
        // سيتم تنفيذها لاحقاً
        console.log('Return invoice:', invoiceId);
    }
}

// إلغاء فاتورة
function cancelInvoice(invoiceId) {
    if (confirm('هل أنت متأكد من إلغاء هذه الفاتورة؟')) {
        // سيتم تنفيذها لاحقاً
        console.log('Cancel invoice:', invoiceId);
    }
}

// تصدير المبيعات
function exportSales(format) {
    if (window.app) {
        window.app.exportData('sales', format);
    }
}
</script>
{% endblock %}
