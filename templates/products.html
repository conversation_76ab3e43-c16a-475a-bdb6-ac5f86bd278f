{% extends "base.html" %}

{% block title %}إدارة المنتجات - نظام إدارة المبيعات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-box me-2"></i>إدارة المنتجات
            </h1>
            {% if current_user.role in ['admin', 'warehouse_manager'] %}
            <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>إضافة منتج جديد
            </a>
            {% endif %}
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-4">
                        <div class="search-box">
                            <input type="text" class="form-control" name="search"
                                   placeholder="البحث في المنتجات..."
                                   value="{{ request.args.get('search', '') }}">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="category">
                            <option value="">جميع التصنيفات</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}"
                                    {% if request.args.get('category') == category.id|string %}selected{% endif %}>
                                {{ category.name }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="stock_status">
                            <option value="">جميع المنتجات</option>
                            <option value="low" {% if request.args.get('stock_status') == 'low' %}selected{% endif %}>
                                مخزون منخفض
                            </option>
                            <option value="out" {% if request.args.get('stock_status') == 'out' %}selected{% endif %}>
                                نفد المخزون
                            </option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-filter me-2"></i>فلترة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول المنتجات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>قائمة المنتجات
                    <span class="badge bg-primary ms-2">{{ products.total }} منتج</span>
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>اسم المنتج</th>
                                <th>التصنيف</th>
                                <th>الباركود</th>
                                <th>سعر الشراء</th>
                                <th>سعر البيع</th>
                                <th>الكمية</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for product in products.items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="icon-circle bg-primary text-white me-3">
                                            <i class="fas fa-box"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ product.name }}</h6>
                                            <small class="text-muted">{{ product.unit }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-secondary">{{ product.category.name }}</span>
                                </td>
                                <td>
                                    <code>{{ product.barcode or 'غير محدد' }}</code>
                                </td>
                                <td>
                                    <span class="text-muted price-display">
                                        <span class="currency-lyd">{{ "%.2f"|format(product.purchase_price) }}</span>
                                        <span class="libya-flag"></span>
                                    </span>
                                </td>
                                <td>
                                    <strong class="text-success price-display">
                                        <span class="currency-lyd">{{ "%.2f"|format(product.selling_price) }}</span>
                                        <span class="libya-flag"></span>
                                    </strong>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <span class="me-2">{{ product.quantity }}</span>
                                        {% if product.quantity <= product.min_quantity %}
                                        <i class="fas fa-exclamation-triangle text-warning"
                                           title="مخزون منخفض"></i>
                                        {% endif %}
                                    </div>
                                </td>
                                <td>
                                    {% if product.quantity > product.min_quantity %}
                                    <span class="status-badge bg-success text-white">متوفر</span>
                                    {% elif product.quantity > 0 %}
                                    <span class="status-badge bg-warning text-dark">منخفض</span>
                                    {% else %}
                                    <span class="status-badge bg-danger text-white">نفد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="#" class="btn btn-outline-primary" title="عرض">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        {% if current_user.role in ['admin', 'warehouse_manager'] %}
                                        <a href="#" class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <a href="#" class="btn btn-outline-success" title="إضافة كمية">
                                            <i class="fas fa-plus"></i>
                                        </a>
                                        {% endif %}
                                        {% if current_user.role == 'admin' %}
                                        <a href="#" class="btn btn-outline-danger delete-btn"
                                           title="حذف" data-message="هل أنت متأكد من حذف هذا المنتج؟">
                                            <i class="fas fa-trash"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="8" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-box fa-3x mb-3"></i>
                                        <p>لا توجد منتجات</p>
                                        {% if current_user.role in ['admin', 'warehouse_manager'] %}
                                        <a href="{{ url_for('add_product') }}" class="btn btn-primary">
                                            إضافة منتج جديد
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- ترقيم الصفحات -->
            {% if products.pages > 1 %}
            <div class="card-footer">
                <nav aria-label="ترقيم الصفحات">
                    <ul class="pagination justify-content-center mb-0">
                        {% if products.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('products', page=products.prev_num, **request.args) }}">
                                السابق
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in products.iter_pages() %}
                        {% if page_num %}
                        {% if page_num != products.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('products', page=page_num, **request.args) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if products.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('products', page=products.next_num, **request.args) }}">
                                التالي
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}
