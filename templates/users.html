{% extends "base.html" %}

{% block title %}إدارة المستخدمين - نظام إدارة المبيعات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-users me-2"></i>إدارة المستخدمين
            </h1>
            <a href="{{ url_for('add_user') }}" class="btn btn-primary">
                <i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد
            </a>
        </div>
    </div>
</div>

<!-- إحصائيات المستخدمين -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-start border-primary border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-primary text-uppercase mb-1">إجمالي المستخدمين</div>
                        <div class="h5 mb-0 fw-bold">{{ users.total }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-start border-success border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-success text-uppercase mb-1">المديرين</div>
                        <div class="h5 mb-0 fw-bold">
                            {% set admin_count = 0 %}
                            {% for user in users.items %}
                                {% if user.role == 'admin' %}
                                    {% set admin_count = admin_count + 1 %}
                                {% endif %}
                            {% endfor %}
                            {{ admin_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-shield fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-start border-info border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-info text-uppercase mb-1">البائعين</div>
                        <div class="h5 mb-0 fw-bold">
                            {% set seller_count = 0 %}
                            {% for user in users.items %}
                                {% if user.role == 'seller' %}
                                    {% set seller_count = seller_count + 1 %}
                                {% endif %}
                            {% endfor %}
                            {{ seller_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-user-tie fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-warning text-uppercase mb-1">مشرفي المخزن</div>
                        <div class="h5 mb-0 fw-bold">
                            {% set warehouse_count = 0 %}
                            {% for user in users.items %}
                                {% if user.role == 'warehouse_manager' %}
                                    {% set warehouse_count = warehouse_count + 1 %}
                                {% endif %}
                            {% endfor %}
                            {{ warehouse_count }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-warehouse fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-6">
                        <div class="search-box">
                            <input type="text" class="form-control" name="search"
                                   placeholder="البحث في المستخدمين..."
                                   value="{{ request.args.get('search', '') }}">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="role">
                            <option value="">جميع الأدوار</option>
                            <option value="admin" {% if request.args.get('role') == 'admin' %}selected{% endif %}>مدير</option>
                            <option value="seller" {% if request.args.get('role') == 'seller' %}selected{% endif %}>بائع</option>
                            <option value="warehouse_manager" {% if request.args.get('role') == 'warehouse_manager' %}selected{% endif %}>مشرف مخزن</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-filter me-2"></i>فلترة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول المستخدمين -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>قائمة المستخدمين
                    <span class="badge bg-primary ms-2">{{ users.total }} مستخدم</span>
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>اسم المستخدم</th>
                                <th>البريد الإلكتروني</th>
                                <th>الدور</th>
                                <th>تاريخ الإنشاء</th>
                                <th>آخر دخول</th>
                                <th>الحالة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for user in users.items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="icon-circle
                                            {% if user.role == 'admin' %}bg-success
                                            {% elif user.role == 'seller' %}bg-info
                                            {% else %}bg-warning{% endif %}
                                            text-white me-3">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <strong>{{ user.username }}</strong>
                                            {% if user.id == current_user.id %}
                                            <span class="badge bg-primary ms-2">أنت</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if user.email %}
                                    <a href="mailto:{{ user.email }}" class="text-decoration-none">
                                        <i class="fas fa-envelope me-1"></i>{{ user.email }}
                                    </a>
                                    {% else %}
                                    <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if user.role == 'admin' %}
                                    <span class="badge bg-success">مدير</span>
                                    {% elif user.role == 'seller' %}
                                    <span class="badge bg-info">بائع</span>
                                    {% elif user.role == 'warehouse_manager' %}
                                    <span class="badge bg-warning">مشرف مخزن</span>
                                    {% else %}
                                    <span class="badge bg-secondary">{{ user.role }}</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ user.created_at.strftime('%Y-%m-%d') if user.created_at else 'غير محدد' }}
                                    </small>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        غير متوفر حالياً
                                    </small>
                                </td>
                                <td>
                                    <span class="status-badge bg-success text-white">نشط</span>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('edit_user', user_id=user.id) }}"
                                           class="btn btn-outline-warning" title="تعديل">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        {% if user.id != current_user.id %}
                                        <button class="btn btn-outline-danger" title="حذف"
                                                onclick="deleteUser({{ user.id }}, '{{ user.username }}')">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-users fa-3x mb-3"></i>
                                        <p>لا يوجد مستخدمين</p>
                                        <a href="{{ url_for('add_user') }}" class="btn btn-primary">
                                            إضافة مستخدم جديد
                                        </a>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- ترقيم الصفحات -->
            {% if users.pages > 1 %}
            <div class="card-footer">
                <nav aria-label="ترقيم الصفحات">
                    <ul class="pagination justify-content-center mb-0">
                        {% if users.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('users', page=users.prev_num, **request.args) }}">
                                السابق
                            </a>
                        </li>
                        {% endif %}

                        {% for page_num in users.iter_pages() %}
                        {% if page_num %}
                        {% if page_num != users.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('users', page=page_num, **request.args) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                        {% endfor %}

                        {% if users.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('users', page=users.next_num, **request.args) }}">
                                التالي
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- نموذج حذف المستخدم -->
<form id="deleteUserForm" method="POST" style="display: none;">
    <input type="hidden" name="_method" value="DELETE">
</form>
{% endblock %}

{% block extra_js %}
<script>
function deleteUser(userId, username) {
    if (confirm(`هل أنت متأكد من حذف المستخدم "${username}"؟\nهذا الإجراء لا يمكن التراجع عنه.`)) {
        const form = document.getElementById('deleteUserForm');
        form.action = `/users/${userId}/delete`;
        form.submit();
    }
}
</script>
{% endblock %}
