{% extends "base.html" %}

{% block title %}إدارة التصنيفات - نظام إدارة المبيعات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-tags me-2"></i>إدارة التصنيفات
            </h1>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                <i class="fas fa-plus me-2"></i>إضافة تصنيف جديد
            </button>
        </div>
    </div>
</div>

<!-- إحصائيات التصنيفات -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card border-start border-primary border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-primary text-uppercase mb-1">إجمالي التصنيفات</div>
                        <div class="h5 mb-0 fw-bold">{{ categories|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-tags fa-2x text-primary"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-start border-success border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-success text-uppercase mb-1">تصنيفات نشطة</div>
                        <div class="h5 mb-0 fw-bold">{{ categories|length }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-check-circle fa-2x text-success"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-start border-info border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-info text-uppercase mb-1">منتجات مصنفة</div>
                        <div class="h5 mb-0 fw-bold">
                            {% set total_products = 0 %}
                            {% for category in categories %}
                                {% set total_products = total_products + category.products|length %}
                            {% endfor %}
                            {{ total_products }}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-box fa-2x text-info"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card border-start border-warning border-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col">
                        <div class="text-xs fw-bold text-warning text-uppercase mb-1">متوسط المنتجات</div>
                        <div class="h5 mb-0 fw-bold">
                            {% if categories|length > 0 %}
                                {{ "%.1f"|format(total_products / categories|length) }}
                            {% else %}
                                0
                            {% endif %}
                        </div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-bar fa-2x text-warning"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- قائمة التصنيفات -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>قائمة التصنيفات
                </h5>
            </div>
            <div class="card-body">
                {% if categories %}
                <div class="row">
                    {% for category in categories %}
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100 border-0 shadow-sm">
                            <div class="card-body">
                                <div class="d-flex align-items-start justify-content-between mb-3">
                                    <div class="icon-circle bg-primary text-white">
                                        <i class="fas fa-tag"></i>
                                    </div>
                                    <div class="dropdown">
                                        <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="dropdown">
                                            <i class="fas fa-ellipsis-v"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <li><a class="dropdown-item" href="#" onclick="editCategory({{ category.id }})">
                                                <i class="fas fa-edit me-2"></i>تعديل
                                            </a></li>
                                            <li><a class="dropdown-item text-danger" href="#" onclick="deleteCategory({{ category.id }})">
                                                <i class="fas fa-trash me-2"></i>حذف
                                            </a></li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <h5 class="card-title">{{ category.name }}</h5>
                                
                                {% if category.description %}
                                <p class="card-text text-muted">{{ category.description }}</p>
                                {% else %}
                                <p class="card-text text-muted">لا يوجد وصف</p>
                                {% endif %}
                                
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <span class="badge bg-primary">{{ category.products|length }} منتج</span>
                                    </div>
                                    <small class="text-muted">
                                        {{ category.created_at.strftime('%Y-%m-%d') }}
                                    </small>
                                </div>
                                
                                <div class="mt-3">
                                    <a href="{{ url_for('products') }}?category={{ category.id }}" 
                                       class="btn btn-outline-primary btn-sm w-100">
                                        <i class="fas fa-eye me-2"></i>عرض المنتجات
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-tags fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">لا توجد تصنيفات</h4>
                    <p class="text-muted">ابدأ بإضافة تصنيف جديد لتنظيم منتجاتك</p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCategoryModal">
                        <i class="fas fa-plus me-2"></i>إضافة تصنيف جديد
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة تصنيف جديد -->
<div class="modal fade" id="addCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus me-2"></i>إضافة تصنيف جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCategoryForm" class="needs-validation" novalidate>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="categoryName" class="form-label">اسم التصنيف *</label>
                        <input type="text" class="form-control" id="categoryName" required>
                        <div class="invalid-feedback">يرجى إدخال اسم التصنيف</div>
                    </div>
                    <div class="mb-3">
                        <label for="categoryDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="categoryDescription" rows="3" 
                                  placeholder="وصف التصنيف (اختياري)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التصنيف</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- نافذة تعديل التصنيف -->
<div class="modal fade" id="editCategoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit me-2"></i>تعديل التصنيف
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editCategoryForm" class="needs-validation" novalidate>
                <div class="modal-body">
                    <input type="hidden" id="editCategoryId">
                    <div class="mb-3">
                        <label for="editCategoryName" class="form-label">اسم التصنيف *</label>
                        <input type="text" class="form-control" id="editCategoryName" required>
                        <div class="invalid-feedback">يرجى إدخال اسم التصنيف</div>
                    </div>
                    <div class="mb-3">
                        <label for="editCategoryDescription" class="form-label">الوصف</label>
                        <textarea class="form-control" id="editCategoryDescription" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إضافة تصنيف جديد
document.getElementById('addCategoryForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!this.checkValidity()) {
        e.stopPropagation();
        this.classList.add('was-validated');
        return;
    }
    
    const categoryData = {
        name: document.getElementById('categoryName').value,
        description: document.getElementById('categoryDescription').value
    };
    
    try {
        const response = await fetch('/categories/add', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(categoryData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            location.reload();
        } else {
            alert('حدث خطأ في إضافة التصنيف');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    }
});

// تعديل التصنيف
function editCategory(categoryId) {
    // سيتم تنفيذها لاحقاً
    console.log('Edit category:', categoryId);
}

// حذف التصنيف
function deleteCategory(categoryId) {
    if (confirm('هل أنت متأكد من حذف هذا التصنيف؟\nسيتم حذف جميع المنتجات المرتبطة به.')) {
        // سيتم تنفيذها لاحقاً
        console.log('Delete category:', categoryId);
    }
}
</script>
{% endblock %}
