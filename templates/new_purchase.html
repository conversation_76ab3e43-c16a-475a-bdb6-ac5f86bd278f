{% extends "base.html" %}

{% block title %}فاتورة شراء جديدة{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-shopping-cart text-primary"></i> فاتورة شراء جديدة</h2>
                <a href="{{ url_for('purchases') }}" class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-right"></i> العودة للمشتريات
                </a>
            </div>

            <form method="POST" id="purchaseForm">
                <div class="row">
                    <!-- معلومات الفاتورة -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-info-circle"></i> معلومات الفاتورة</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="supplier_id" class="form-label">المورد *</label>
                                        <select class="form-select" id="supplier_id" name="supplier_id" required>
                                            <option value="">اختر المورد</option>
                                            {% for supplier in suppliers %}
                                            <option value="{{ supplier.id }}">{{ supplier.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="invoice_date" class="form-label">تاريخ الفاتورة *</label>
                                        <input type="date" class="form-control" id="invoice_date" name="invoice_date" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="due_date" class="form-label">تاريخ الاستحقاق</label>
                                        <input type="date" class="form-control" id="due_date" name="due_date">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="notes" class="form-label">ملاحظات</label>
                                        <textarea class="form-control" id="notes" name="notes" rows="2"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- إضافة منتج -->
                    <div class="col-md-6">
                        <div class="card mb-4">
                            <div class="card-header">
                                <h5><i class="fas fa-plus"></i> إضافة منتج</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="product_id" class="form-label">المنتج</label>
                                        <select class="form-select" id="product_id">
                                            <option value="">اختر المنتج</option>
                                            {% for product in products %}
                                            <option value="{{ product.id }}">{{ product.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label for="quantity" class="form-label">الكمية</label>
                                        <input type="number" class="form-control" id="quantity" min="1" value="1">
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="unit_cost" class="form-label">سعر الوحدة</label>
                                        <input type="number" class="form-control" id="unit_cost" step="0.001" min="0">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button type="button" class="btn btn-primary d-block" onclick="addItem()">
                                            <i class="fas fa-plus"></i> إضافة
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- جدول العناصر -->
                <div class="card mb-4">
                    <div class="card-header">
                        <h5><i class="fas fa-list"></i> عناصر الفاتورة</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>المنتج</th>
                                        <th>الكمية</th>
                                        <th>سعر الوحدة</th>
                                        <th>الإجمالي</th>
                                        <th>إجراءات</th>
                                    </tr>
                                </thead>
                                <tbody id="itemsTable">
                                    <tr id="emptyRow">
                                        <td colspan="5" class="text-center text-muted">لا توجد عناصر</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>

                <!-- المجاميع -->
                <div class="row">
                    <div class="col-md-8"></div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="fas fa-calculator"></i> المجاميع</h5>
                            </div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-6">المجموع الفرعي:</div>
                                    <div class="col-6 text-end" id="subtotal">0.000 د.ل</div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6">
                                        <label for="discount">الخصم:</label>
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control form-control-sm"
                                               id="discount" name="discount" step="0.001" min="0" value="0"
                                               onchange="calculateTotals()">
                                    </div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6">
                                        <label for="tax_amount">الضريبة:</label>
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control form-control-sm"
                                               id="tax_amount" name="tax_amount" step="0.001" min="0" value="0"
                                               onchange="calculateTotals()">
                                    </div>
                                </div>
                                <hr>
                                <div class="row">
                                    <div class="col-6"><strong>الإجمالي النهائي:</strong></div>
                                    <div class="col-6 text-end"><strong id="finalTotal">0.000 د.ل</strong></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- أزرار الحفظ -->
                <div class="row mt-4">
                    <div class="col-12 text-center">
                        <button type="submit" class="btn btn-success btn-lg me-2">
                            <i class="fas fa-save"></i> حفظ الفاتورة
                        </button>
                        <a href="{{ url_for('purchases') }}" class="btn btn-secondary btn-lg">
                            <i class="fas fa-times"></i> إلغاء
                        </a>
                    </div>
                </div>

                <!-- حقول مخفية للعناصر -->
                <div id="hiddenItems"></div>
            </form>
        </div>
    </div>
</div>

<script>
let items = [];

function addItem() {
    const productId = document.getElementById('product_id').value;
    const quantity = parseInt(document.getElementById('quantity').value) || 1;
    const unitCost = parseFloat(document.getElementById('unit_cost').value) || 0;

    if (!productId || unitCost <= 0) {
        alert('يرجى اختيار المنتج وإدخال سعر صحيح');
        return;
    }

    const productName = document.getElementById('product_id').selectedOptions[0].text;
    const total = quantity * unitCost;

    items.push({
        product_id: productId,
        product_name: productName,
        quantity: quantity,
        unit_cost: unitCost,
        total: total
    });

    updateTable();
    calculateTotals();

    // إعادة تعيين النموذج
    document.getElementById('product_id').value = '';
    document.getElementById('quantity').value = '1';
    document.getElementById('unit_cost').value = '';
}

function removeItem(index) {
    items.splice(index, 1);
    updateTable();
    calculateTotals();
}

function updateTable() {
    const tbody = document.getElementById('itemsTable');
    const emptyRow = document.getElementById('emptyRow');

    // إزالة الصفوف الموجودة
    const existingRows = tbody.querySelectorAll('tr:not(#emptyRow)');
    existingRows.forEach(row => row.remove());

    if (items.length === 0) {
        emptyRow.style.display = '';
        return;
    }

    emptyRow.style.display = 'none';

    items.forEach((item, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${item.product_name}</td>
            <td>${item.quantity}</td>
            <td>${item.unit_cost.toFixed(3)} د.ل</td>
            <td>${item.total.toFixed(3)} د.ل</td>
            <td>
                <button type="button" class="btn btn-outline-danger btn-sm" onclick="removeItem(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });

    updateHiddenFields();
}

function updateHiddenFields() {
    const container = document.getElementById('hiddenItems');
    container.innerHTML = '';

    items.forEach((item, index) => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'items';
        input.value = `${item.product_id},${item.quantity},${item.unit_cost}`;
        container.appendChild(input);
    });
}

function calculateTotals() {
    const subtotal = items.reduce((sum, item) => sum + item.total, 0);
    const discount = parseFloat(document.getElementById('discount').value) || 0;
    const tax = parseFloat(document.getElementById('tax_amount').value) || 0;
    const finalTotal = subtotal - discount + tax;

    document.getElementById('subtotal').textContent = subtotal.toFixed(3) + ' د.ل';
    document.getElementById('finalTotal').textContent = finalTotal.toFixed(3) + ' د.ل';
}

// تعيين التاريخ الحالي
document.getElementById('invoice_date').value = new Date().toISOString().split('T')[0];
</script>
{% endblock %}
