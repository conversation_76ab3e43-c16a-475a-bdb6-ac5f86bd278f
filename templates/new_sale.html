{% extends "base.html" %}

{% block title %}فاتورة مبيعات جديدة - نظام إدارة المبيعات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-plus-circle me-2"></i>فاتورة مبيعات جديدة
            </h1>
            <a href="{{ url_for('sales') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للمبيعات
            </a>
        </div>
    </div>
</div>

<div class="row">
    <!-- معلومات الفاتورة -->
    <div class="col-lg-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-info-circle me-2"></i>معلومات الفاتورة
                </h5>
            </div>
            <div class="card-body">
                <form id="invoiceForm">
                    <!-- العميل -->
                    <div class="mb-3">
                        <label for="customer" class="form-label">العميل</label>
                        <div class="input-group">
                            <select class="form-select" id="customer" name="customer_id">
                                <option value="">عميل نقدي</option>
                                {% for customer in customers %}
                                <option value="{{ customer.id }}">{{ customer.name }}</option>
                                {% endfor %}
                            </select>
                            <button class="btn btn-outline-primary" type="button" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                                <i class="fas fa-plus"></i>
                            </button>
                        </div>
                    </div>

                    <!-- طريقة الدفع -->
                    <div class="mb-3">
                        <label for="paymentMethod" class="form-label">طريقة الدفع</label>
                        <select class="form-select" id="paymentMethod" name="payment_method">
                            <option value="cash">نقدي</option>
                            <option value="card">بطاقة</option>
                            <option value="credit">آجل</option>
                        </select>
                    </div>

                    <!-- الخصم -->
                    <div class="mb-3">
                        <label for="discount" class="form-label">الخصم</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="discount" name="discount" value="0" min="0" step="0.001">
                            <span class="input-group-text">د.ل</span>
                        </div>
                    </div>

                    <!-- الضريبة -->
                    <div class="mb-3">
                        <label for="tax" class="form-label">الضريبة (15%)</label>
                        <div class="input-group">
                            <input type="number" class="form-control" id="tax" name="tax" value="0" min="0" step="0.001" readonly>
                            <span class="input-group-text">د.ل</span>
                        </div>
                    </div>

                    <!-- ملاحظات -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">ملاحظات</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
        </div>

        <!-- ملخص الفاتورة -->
        <div class="card mt-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-calculator me-2"></i>ملخص الفاتورة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between mb-2">
                    <span>المجموع الفرعي:</span>
                    <span id="subtotal">0.000 د.ل</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>الخصم:</span>
                    <span id="discountAmount">0.000 د.ل</span>
                </div>
                <div class="d-flex justify-content-between mb-2">
                    <span>الضريبة:</span>
                    <span id="taxAmount">0.000 د.ل</span>
                </div>
                <hr>
                <div class="d-flex justify-content-between fw-bold h5">
                    <span>المجموع النهائي:</span>
                    <span id="finalTotal" class="text-success">0.000 د.ل <span class="libya-flag"></span></span>
                </div>

                <div class="d-grid gap-2 mt-3">
                    <button type="button" class="btn btn-success btn-lg" onclick="saveInvoice()">
                        <i class="fas fa-save me-2"></i>حفظ الفاتورة
                    </button>
                    <button type="button" class="btn btn-outline-primary" onclick="printInvoice()">
                        <i class="fas fa-print me-2"></i>طباعة
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- عناصر الفاتورة -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-shopping-cart me-2"></i>عناصر الفاتورة
                </h5>
            </div>
            <div class="card-body">
                <!-- إضافة منتج -->
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="search-box">
                            <input type="text" class="form-control search-input"
                                   placeholder="البحث عن منتج..."
                                   data-search-type="products"
                                   id="productSearch">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                        <div id="products-results" class="list-group mt-2" style="display: none;"></div>
                    </div>
                    <div class="col-md-3">
                        <input type="number" class="form-control" id="productQuantity"
                               placeholder="الكمية" min="1" value="1">
                    </div>
                    <div class="col-md-3">
                        <button type="button" class="btn btn-primary w-100" onclick="addProductToInvoice()">
                            <i class="fas fa-plus me-2"></i>إضافة
                        </button>
                    </div>
                </div>

                <!-- جدول العناصر -->
                <div class="table-responsive">
                    <table class="table table-hover" id="invoiceItemsTable">
                        <thead class="table-light">
                            <tr>
                                <th>المنتج</th>
                                <th>السعر</th>
                                <th>الكمية</th>
                                <th>المجموع</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody id="invoiceItems">
                            <tr id="emptyRow">
                                <td colspan="5" class="text-center text-muted py-4">
                                    <i class="fas fa-shopping-cart fa-2x mb-2"></i>
                                    <p>لا توجد عناصر في الفاتورة</p>
                                    <small>ابحث عن منتج وأضفه للفاتورة</small>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- نافذة إضافة عميل سريع -->
<div class="modal fade" id="addCustomerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة عميل سريع</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="quickCustomerForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="quickCustomerName" class="form-label">اسم العميل *</label>
                        <input type="text" class="form-control" id="quickCustomerName" required>
                    </div>
                    <div class="mb-3">
                        <label for="quickCustomerPhone" class="form-label">رقم الهاتف</label>
                        <input type="tel" class="form-control" id="quickCustomerPhone">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let invoiceItems = [];
let selectedProduct = null;

// إضافة منتج للفاتورة
function addProductToInvoice() {
    if (!selectedProduct) {
        alert('يرجى اختيار منتج أولاً');
        return;
    }

    const quantity = parseInt(document.getElementById('productQuantity').value) || 1;

    if (quantity > selectedProduct.quantity) {
        alert(`الكمية المتوفرة: ${selectedProduct.quantity} ${selectedProduct.unit}`);
        return;
    }

    // التحقق من وجود المنتج في الفاتورة
    const existingItem = invoiceItems.find(item => item.product_id === selectedProduct.id);

    if (existingItem) {
        existingItem.quantity += quantity;
        existingItem.total_price = existingItem.quantity * existingItem.unit_price;
    } else {
        invoiceItems.push({
            product_id: selectedProduct.id,
            product_name: selectedProduct.name,
            unit_price: selectedProduct.price,
            quantity: quantity,
            total_price: quantity * selectedProduct.price,
            unit: selectedProduct.unit
        });
    }

    updateInvoiceTable();
    calculateTotals();

    // إعادة تعيين البحث
    document.getElementById('productSearch').value = '';
    document.getElementById('productQuantity').value = '1';
    document.getElementById('products-results').style.display = 'none';
    selectedProduct = null;
}

// تحديث جدول الفاتورة
function updateInvoiceTable() {
    const tbody = document.getElementById('invoiceItems');
    const emptyRow = document.getElementById('emptyRow');

    if (invoiceItems.length === 0) {
        emptyRow.style.display = '';
        return;
    }

    emptyRow.style.display = 'none';

    // إزالة الصفوف الموجودة (عدا الصف الفارغ)
    const existingRows = tbody.querySelectorAll('tr:not(#emptyRow)');
    existingRows.forEach(row => row.remove());

    // إضافة الصفوف الجديدة
    invoiceItems.forEach((item, index) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>
                <div>
                    <strong>${item.product_name}</strong>
                    <br><small class="text-muted">${item.unit}</small>
                </div>
            </td>
            <td>${item.unit_price.toFixed(3)} د.ل</td>
            <td>
                <input type="number" class="form-control form-control-sm"
                       value="${item.quantity}" min="1"
                       onchange="updateItemQuantity(${index}, this.value)">
            </td>
            <td class="fw-bold">${item.total_price.toFixed(3)} د.ل</td>
            <td>
                <button class="btn btn-outline-danger btn-sm" onclick="removeItem(${index})">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// تحديث كمية العنصر
function updateItemQuantity(index, newQuantity) {
    const quantity = parseInt(newQuantity) || 1;
    invoiceItems[index].quantity = quantity;
    invoiceItems[index].total_price = quantity * invoiceItems[index].unit_price;
    updateInvoiceTable();
    calculateTotals();
}

// حذف عنصر
function removeItem(index) {
    invoiceItems.splice(index, 1);
    updateInvoiceTable();
    calculateTotals();
}

// حساب المجاميع
function calculateTotals() {
    const subtotal = invoiceItems.reduce((sum, item) => sum + item.total_price, 0);
    const discount = parseFloat(document.getElementById('discount').value) || 0;
    const taxRate = 0.15; // 15%
    const tax = (subtotal - discount) * taxRate;
    const finalTotal = subtotal - discount + tax;

    document.getElementById('subtotal').textContent = subtotal.toFixed(3) + ' د.ل';
    document.getElementById('discountAmount').textContent = discount.toFixed(3) + ' د.ل';
    document.getElementById('tax').value = tax.toFixed(3);
    document.getElementById('taxAmount').textContent = tax.toFixed(3) + ' د.ل';
    document.getElementById('finalTotal').innerHTML = finalTotal.toFixed(3) + ' د.ل <span class="libya-flag"></span>';
}

// حفظ الفاتورة
async function saveInvoice() {
    if (invoiceItems.length === 0) {
        alert('يرجى إضافة عناصر للفاتورة');
        return;
    }

    const formData = new FormData(document.getElementById('invoiceForm'));
    const invoiceData = {
        customer_id: formData.get('customer_id') || null,
        payment_method: formData.get('payment_method'),
        discount: parseFloat(formData.get('discount')) || 0,
        tax: parseFloat(formData.get('tax')) || 0,
        notes: formData.get('notes'),
        items: invoiceItems
    };

    try {
        const response = await fetch('/api/invoices', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(invoiceData)
        });

        const result = await response.json();

        if (result.success) {
            alert(`تم حفظ الفاتورة بنجاح\nرقم الفاتورة: ${result.invoice_number}`);
            window.location.href = '/sales';
        } else {
            alert('حدث خطأ: ' + result.error);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    }
}

// إعداد البحث عن المنتجات
document.getElementById('productSearch').addEventListener('input', async function(e) {
    const query = e.target.value;
    const resultsContainer = document.getElementById('products-results');

    if (query.length < 2) {
        resultsContainer.style.display = 'none';
        return;
    }

    try {
        const response = await fetch(`/api/products/search?q=${encodeURIComponent(query)}`);
        const products = await response.json();

        resultsContainer.innerHTML = '';

        products.forEach(product => {
            const item = document.createElement('div');
            item.className = 'list-group-item list-group-item-action';
            item.innerHTML = `
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${product.name}</h6>
                    <small class="text-success">${product.price} د.ل</small>
                </div>
                <p class="mb-1">الكمية: ${product.quantity} ${product.unit}</p>
                <small>الباركود: ${product.barcode || 'غير محدد'}</small>
            `;

            item.addEventListener('click', () => {
                selectedProduct = product;
                document.getElementById('productSearch').value = product.name;
                resultsContainer.style.display = 'none';
            });

            resultsContainer.appendChild(item);
        });

        resultsContainer.style.display = products.length > 0 ? 'block' : 'none';
    } catch (error) {
        console.error('Search error:', error);
    }
});

// إضافة مستمعي الأحداث
document.getElementById('discount').addEventListener('input', calculateTotals);

// إضافة عميل سريع
document.getElementById('quickCustomerForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const customerData = {
        name: document.getElementById('quickCustomerName').value,
        phone: document.getElementById('quickCustomerPhone').value
    };

    try {
        const response = await fetch('/api/customers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(customerData)
        });

        const result = await response.json();

        if (result.success) {
            // إضافة العميل للقائمة
            const customerSelect = document.getElementById('customer');
            const option = document.createElement('option');
            option.value = result.customer_id;
            option.textContent = customerData.name;
            option.selected = true;
            customerSelect.appendChild(option);

            // إغلاق النافذة
            bootstrap.Modal.getInstance(document.getElementById('addCustomerModal')).hide();

            // إعادة تعيين النموذج
            this.reset();
        } else {
            alert('حدث خطأ في إضافة العميل');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    }
});
</script>
{% endblock %}
