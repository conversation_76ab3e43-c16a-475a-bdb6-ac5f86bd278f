{% extends "base.html" %}

{% block title %}التقارير والإحصائيات - نظام إدارة المبيعات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-chart-bar me-2"></i>التقارير والإحصائيات
            </h1>
            <div class="btn-group">
                <button class="btn btn-outline-success" onclick="exportReport('excel')">
                    <i class="fas fa-file-excel me-2"></i>تصدير Excel
                </button>
                <button class="btn btn-outline-danger" onclick="exportReport('pdf')">
                    <i class="fas fa-file-pdf me-2"></i>تصدير PDF
                </button>
            </div>
        </div>
    </div>
</div>

<!-- فلاتر التقارير -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-filter me-2"></i>فلاتر التقارير
                </h5>
            </div>
            <div class="card-body">
                <form id="reportFilters" class="row g-3">
                    <div class="col-md-3">
                        <label for="reportType" class="form-label">نوع التقرير</label>
                        <select class="form-select" id="reportType">
                            <option value="sales">تقرير المبيعات</option>
                            <option value="inventory">تقرير المخزون</option>
                            <option value="customers">تقرير العملاء</option>
                            <option value="products">تقرير المنتجات</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label for="startDate" class="form-label">من تاريخ</label>
                        <input type="date" class="form-control" id="startDate">
                    </div>
                    <div class="col-md-3">
                        <label for="endDate" class="form-label">إلى تاريخ</label>
                        <input type="date" class="form-control" id="endDate">
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-search me-2"></i>إنشاء التقرير
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- إحصائيات سريعة -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card stat-card text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h4 mb-0" id="totalSales">0.000 د.ل <span class="libya-flag"></span></div>
                        <div>إجمالي المبيعات</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-money-bill fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h4 mb-0" id="totalInvoices">0</div>
                        <div>عدد الفواتير</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-receipt fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h4 mb-0" id="averageInvoice">0.000 د.ل <span class="libya-flag"></span></div>
                        <div>متوسط الفاتورة</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-line fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card stat-card text-white">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <div class="h4 mb-0" id="totalProfit">0.000 د.ل <span class="libya-flag"></span></div>
                        <div>إجمالي الأرباح</div>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-chart-pie fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- محتوى التقرير -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0" id="reportTitle">
                    <i class="fas fa-table me-2"></i>تقرير المبيعات
                </h5>
            </div>
            <div class="card-body">
                <div id="reportContent">
                    <div class="text-center py-5">
                        <i class="fas fa-chart-bar fa-4x text-muted mb-3"></i>
                        <h4 class="text-muted">اختر نوع التقرير والفترة الزمنية</h4>
                        <p class="text-muted">استخدم الفلاتر أعلاه لإنشاء التقرير المطلوب</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- تقارير سريعة -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-bolt me-2"></i>تقارير سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3"
                                onclick="generateQuickReport('today')">
                            <i class="fas fa-calendar-day fa-2x mb-2"></i>
                            <span>مبيعات اليوم</span>
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3"
                                onclick="generateQuickReport('week')">
                            <i class="fas fa-calendar-week fa-2x mb-2"></i>
                            <span>مبيعات الأسبوع</span>
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3"
                                onclick="generateQuickReport('month')">
                            <i class="fas fa-calendar-alt fa-2x mb-2"></i>
                            <span>مبيعات الشهر</span>
                        </button>
                    </div>
                    <div class="col-md-3 mb-3">
                        <button class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center p-3"
                                onclick="generateQuickReport('low_stock')">
                            <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                            <span>مخزون منخفض</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إنشاء التقرير
document.getElementById('reportFilters').addEventListener('submit', async function(e) {
    e.preventDefault();

    const reportType = document.getElementById('reportType').value;
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;

    await generateReport(reportType, startDate, endDate);
});

// إنشاء التقرير
async function generateReport(type, startDate, endDate) {
    const reportContent = document.getElementById('reportContent');
    const reportTitle = document.getElementById('reportTitle');

    // عرض مؤشر التحميل
    reportContent.innerHTML = `
        <div class="text-center py-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">جاري التحميل...</span>
            </div>
            <p class="mt-3">جاري إنشاء التقرير...</p>
        </div>
    `;

    try {
        let url = `/api/reports/${type}`;
        const params = new URLSearchParams();

        if (startDate) params.append('start_date', startDate);
        if (endDate) params.append('end_date', endDate);

        if (params.toString()) {
            url += '?' + params.toString();
        }

        const response = await fetch(url);
        const data = await response.json();

        // تحديث العنوان
        const titles = {
            'sales': 'تقرير المبيعات',
            'inventory': 'تقرير المخزون',
            'customers': 'تقرير العملاء',
            'products': 'تقرير المنتجات'
        };

        reportTitle.innerHTML = `<i class="fas fa-table me-2"></i>${titles[type]}`;

        // عرض البيانات
        if (type === 'sales') {
            displaySalesReport(data);
        } else {
            displayGenericReport(data);
        }

        // تحديث الإحصائيات
        updateStatistics(data);

    } catch (error) {
        console.error('Error:', error);
        reportContent.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-exclamation-triangle fa-3x text-danger mb-3"></i>
                <h4 class="text-danger">حدث خطأ في إنشاء التقرير</h4>
                <p class="text-muted">يرجى المحاولة مرة أخرى</p>
            </div>
        `;
    }
}

// عرض تقرير المبيعات
function displaySalesReport(data) {
    const reportContent = document.getElementById('reportContent');

    if (!data.invoices || data.invoices.length === 0) {
        reportContent.innerHTML = `
            <div class="text-center py-5">
                <i class="fas fa-chart-bar fa-3x text-muted mb-3"></i>
                <h4 class="text-muted">لا توجد بيانات</h4>
                <p class="text-muted">لا توجد مبيعات في الفترة المحددة</p>
            </div>
        `;
        return;
    }

    let tableHTML = `
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>رقم الفاتورة</th>
                        <th>العميل</th>
                        <th>المبلغ</th>
                        <th>التاريخ</th>
                    </tr>
                </thead>
                <tbody>
    `;

    data.invoices.forEach(invoice => {
        tableHTML += `
            <tr>
                <td>${invoice.invoice_number}</td>
                <td>${invoice.customer_name}</td>
                <td class="text-success fw-bold">${invoice.total_amount.toFixed(3)} د.ل <span class="libya-flag"></span></td>
                <td>${invoice.created_at}</td>
            </tr>
        `;
    });

    tableHTML += `
                </tbody>
            </table>
        </div>
    `;

    reportContent.innerHTML = tableHTML;
}

// عرض تقرير عام
function displayGenericReport(data) {
    const reportContent = document.getElementById('reportContent');
    reportContent.innerHTML = `
        <div class="text-center py-5">
            <i class="fas fa-info-circle fa-3x text-info mb-3"></i>
            <h4 class="text-info">قريباً</h4>
            <p class="text-muted">هذا التقرير قيد التطوير</p>
        </div>
    `;
}

// تحديث الإحصائيات
function updateStatistics(data) {
    if (data.total_sales !== undefined) {
        document.getElementById('totalSales').innerHTML = data.total_sales.toFixed(3) + ' د.ل <span class="libya-flag"></span>';
    }
    if (data.total_invoices !== undefined) {
        document.getElementById('totalInvoices').textContent = data.total_invoices;
    }
    if (data.total_sales && data.total_invoices) {
        const average = data.total_invoices > 0 ? data.total_sales / data.total_invoices : 0;
        document.getElementById('averageInvoice').innerHTML = average.toFixed(3) + ' د.ل <span class="libya-flag"></span>';
    }
}

// تقارير سريعة
function generateQuickReport(period) {
    const today = new Date();
    let startDate, endDate;

    switch (period) {
        case 'today':
            startDate = endDate = today.toISOString().split('T')[0];
            break;
        case 'week':
            const weekStart = new Date(today.setDate(today.getDate() - today.getDay()));
            startDate = weekStart.toISOString().split('T')[0];
            endDate = new Date().toISOString().split('T')[0];
            break;
        case 'month':
            startDate = new Date(today.getFullYear(), today.getMonth(), 1).toISOString().split('T')[0];
            endDate = new Date().toISOString().split('T')[0];
            break;
        case 'low_stock':
            // تقرير المخزون المنخفض
            generateReport('inventory', '', '');
            return;
    }

    document.getElementById('startDate').value = startDate;
    document.getElementById('endDate').value = endDate;
    generateReport('sales', startDate, endDate);
}

// تصدير التقرير
function exportReport(format) {
    if (window.app) {
        window.app.exportData('reports', format);
    } else {
        alert('ميزة التصدير قيد التطوير');
    }
}

// تحميل إحصائيات أولية
document.addEventListener('DOMContentLoaded', function() {
    generateQuickReport('today');
});
</script>
{% endblock %}
