{% extends "base.html" %}

{% block title %}حركات المخزون{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-exchange-alt"></i> حركات المخزون
        </h1>
        <a href="{{ url_for('stock_adjustment') }}" class="btn btn-primary btn-sm shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> تسوية مخزون
        </a>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                حركات اليوم
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="todayMovements">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-calendar-day fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                حركات الدخول
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="inMovements">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-down fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                حركات الخروج
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="outMovements">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-arrow-up fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-info shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                تسويات المخزون
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="adjustmentMovements">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-balance-scale fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- فلاتر البحث -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">فلاتر البحث</h6>
        </div>
        <div class="card-body">
            <form id="filterForm" class="row">
                <div class="col-md-3 mb-3">
                    <label for="product_filter" class="form-label">المنتج</label>
                    <select class="form-control" id="product_filter" name="product_id">
                        <option value="">جميع المنتجات</option>
                        <!-- سيتم تحميل المنتجات بـ JavaScript -->
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="movement_type_filter" class="form-label">نوع الحركة</label>
                    <select class="form-control" id="movement_type_filter" name="movement_type">
                        <option value="">جميع الأنواع</option>
                        <option value="in">دخول</option>
                        <option value="out">خروج</option>
                        <option value="adjustment">تسوية</option>
                        <option value="transfer">نقل</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_from" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="date_from" name="date_from">
                </div>
                <div class="col-md-3 mb-3">
                    <label for="date_to" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="date_to" name="date_to">
                </div>
            </form>
        </div>
    </div>

    <!-- جدول حركات المخزون -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">سجل حركات المخزون</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="movementsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>التاريخ</th>
                            <th>المنتج</th>
                            <th>نوع الحركة</th>
                            <th>الكمية</th>
                            <th>الرصيد قبل</th>
                            <th>الرصيد بعد</th>
                            <th>التكلفة</th>
                            <th>المرجع</th>
                            <th>المستخدم</th>
                            <th>ملاحظات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for movement in movements %}
                        <tr>
                            <td>{{ movement.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            <td>{{ movement.product.name }}</td>
                            <td>
                                {% if movement.movement_type == 'in' %}
                                <span class="badge badge-success">دخول</span>
                                {% elif movement.movement_type == 'out' %}
                                <span class="badge badge-danger">خروج</span>
                                {% elif movement.movement_type == 'adjustment' %}
                                <span class="badge badge-info">تسوية</span>
                                {% elif movement.movement_type == 'transfer' %}
                                <span class="badge badge-warning">نقل</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if movement.movement_type == 'out' %}
                                <span class="text-danger">-{{ movement.quantity }}</span>
                                {% else %}
                                <span class="text-success">+{{ movement.quantity }}</span>
                                {% endif %}
                            </td>
                            <td>{{ movement.balance_before }}</td>
                            <td>{{ movement.balance_after }}</td>
                            <td>
                                {% if movement.total_cost %}
                                <span class="text-primary">
                                    {{ "%.3f"|format(movement.total_cost) }} د.ل
                                    <span class="libya-flag"></span>
                                </span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                {% if movement.reference_type and movement.reference_id %}
                                <span class="badge badge-secondary">
                                    {{ movement.reference_type }}-{{ movement.reference_id }}
                                </span>
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>{{ movement.user.username }}</td>
                            <td>{{ movement.notes or '-' }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#movementsTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 0, "desc" ]],
        "pageLength": 25
    });
    
    // تحميل الإحصائيات
    loadMovementStats();
    
    // تحميل المنتجات في الفلتر
    loadProductsFilter();
    
    // تطبيق الفلاتر
    $('#filterForm input, #filterForm select').on('change', function() {
        applyFilters();
    });
});

function loadMovementStats() {
    var todayCount = 0;
    var inCount = 0;
    var outCount = 0;
    var adjustmentCount = 0;
    
    var today = new Date().toISOString().split('T')[0];
    
    $('#movementsTable tbody tr').each(function() {
        var dateStr = $(this).find('td:eq(0)').text().split(' ')[0];
        var movementType = $(this).find('td:eq(2) .badge').text().trim();
        
        if (dateStr === today) {
            todayCount++;
        }
        
        if (movementType === 'دخول') inCount++;
        else if (movementType === 'خروج') outCount++;
        else if (movementType === 'تسوية') adjustmentCount++;
    });
    
    $('#todayMovements').text(todayCount);
    $('#inMovements').text(inCount);
    $('#outMovements').text(outCount);
    $('#adjustmentMovements').text(adjustmentCount);
}

function loadProductsFilter() {
    // يمكن إضافة AJAX لجلب المنتجات
    // مؤقتاً سنستخدم المنتجات من الجدول
    var products = new Set();
    $('#movementsTable tbody tr').each(function() {
        var productName = $(this).find('td:eq(1)').text().trim();
        products.add(productName);
    });
    
    products.forEach(function(product) {
        $('#product_filter').append('<option value="' + product + '">' + product + '</option>');
    });
}

function applyFilters() {
    var table = $('#movementsTable').DataTable();
    
    // تطبيق فلتر المنتج
    var productFilter = $('#product_filter').val();
    table.column(1).search(productFilter);
    
    // تطبيق فلتر نوع الحركة
    var typeFilter = $('#movement_type_filter').val();
    if (typeFilter) {
        var typeText = '';
        switch(typeFilter) {
            case 'in': typeText = 'دخول'; break;
            case 'out': typeText = 'خروج'; break;
            case 'adjustment': typeText = 'تسوية'; break;
            case 'transfer': typeText = 'نقل'; break;
        }
        table.column(2).search(typeText);
    } else {
        table.column(2).search('');
    }
    
    table.draw();
}
</script>

<style>
.libya-flag {
    display: inline-block;
    width: 16px;
    height: 12px;
    background: linear-gradient(to bottom, #e4002b 33%, #000000 33%, #000000 66%, #009639 66%);
    margin-left: 5px;
    border-radius: 2px;
}

.table td {
    vertical-align: middle;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-info {
    border-left: 0.25rem solid #36b9cc !important;
}
</style>
{% endblock %}
