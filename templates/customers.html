{% extends "base.html" %}

{% block title %}إدارة العملاء - نظام إدارة المبيعات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-users me-2"></i>إدارة العملاء
            </h1>
            <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
            </button>
        </div>
    </div>
</div>

<!-- فلتر البحث -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <form method="GET" class="row g-3">
                    <div class="col-md-6">
                        <div class="search-box">
                            <input type="text" class="form-control" name="search" 
                                   placeholder="البحث في العملاء..." 
                                   value="{{ request.args.get('search', '') }}">
                            <i class="fas fa-search search-icon"></i>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" name="sort">
                            <option value="name">ترتيب حسب الاسم</option>
                            <option value="created_at" {% if request.args.get('sort') == 'created_at' %}selected{% endif %}>
                                ترتيب حسب تاريخ الإضافة
                            </option>
                            <option value="balance" {% if request.args.get('sort') == 'balance' %}selected{% endif %}>
                                ترتيب حسب الرصيد
                            </option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-outline-primary w-100">
                            <i class="fas fa-filter me-2"></i>فلترة
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- جدول العملاء -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-list me-2"></i>قائمة العملاء
                    <span class="badge bg-primary ms-2">{{ customers.total }} عميل</span>
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>اسم العميل</th>
                                <th>رقم الهاتف</th>
                                <th>البريد الإلكتروني</th>
                                <th>الرصيد الحالي</th>
                                <th>الحد الائتماني</th>
                                <th>تاريخ الإضافة</th>
                                <th>الإجراءات</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for customer in customers.items %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="icon-circle bg-info text-white me-3">
                                            <i class="fas fa-user"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-0">{{ customer.name }}</h6>
                                            {% if customer.address %}
                                            <small class="text-muted">{{ customer.address[:50] }}...</small>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    {% if customer.phone %}
                                    <a href="tel:{{ customer.phone }}" class="text-decoration-none">
                                        <i class="fas fa-phone me-1"></i>{{ customer.phone }}
                                    </a>
                                    {% else %}
                                    <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if customer.email %}
                                    <a href="mailto:{{ customer.email }}" class="text-decoration-none">
                                        <i class="fas fa-envelope me-1"></i>{{ customer.email }}
                                    </a>
                                    {% else %}
                                    <span class="text-muted">غير محدد</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if customer.current_balance > 0 %}
                                    <span class="text-danger fw-bold">
                                        {{ "%.2f"|format(customer.current_balance) }} ر.س
                                    </span>
                                    {% elif customer.current_balance < 0 %}
                                    <span class="text-success fw-bold">
                                        {{ "%.2f"|format(abs(customer.current_balance)) }} ر.س
                                    </span>
                                    {% else %}
                                    <span class="text-muted">0.00 ر.س</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="text-info">{{ "%.2f"|format(customer.credit_limit) }} ر.س</span>
                                </td>
                                <td>
                                    <small class="text-muted">
                                        {{ customer.created_at.strftime('%Y-%m-%d') }}
                                    </small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <button class="btn btn-outline-primary" title="عرض التفاصيل"
                                                onclick="viewCustomer({{ customer.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-warning" title="تعديل"
                                                onclick="editCustomer({{ customer.id }})">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <a href="{{ url_for('new_sale') }}?customer={{ customer.id }}" 
                                           class="btn btn-outline-success" title="فاتورة جديدة">
                                            <i class="fas fa-shopping-cart"></i>
                                        </a>
                                        {% if current_user.role == 'admin' %}
                                        <button class="btn btn-outline-danger delete-btn" title="حذف"
                                                onclick="deleteCustomer({{ customer.id }})">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% else %}
                            <tr>
                                <td colspan="7" class="text-center py-4">
                                    <div class="text-muted">
                                        <i class="fas fa-users fa-3x mb-3"></i>
                                        <p>لا يوجد عملاء</p>
                                        <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addCustomerModal">
                                            إضافة عميل جديد
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- ترقيم الصفحات -->
            {% if customers.pages > 1 %}
            <div class="card-footer">
                <nav aria-label="ترقيم الصفحات">
                    <ul class="pagination justify-content-center mb-0">
                        {% if customers.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customers', page=customers.prev_num, **request.args) }}">
                                السابق
                            </a>
                        </li>
                        {% endif %}
                        
                        {% for page_num in customers.iter_pages() %}
                        {% if page_num %}
                        {% if page_num != customers.page %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customers', page=page_num, **request.args) }}">
                                {{ page_num }}
                            </a>
                        </li>
                        {% else %}
                        <li class="page-item active">
                            <span class="page-link">{{ page_num }}</span>
                        </li>
                        {% endif %}
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">...</span>
                        </li>
                        {% endif %}
                        {% endfor %}
                        
                        {% if customers.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('customers', page=customers.next_num, **request.args) }}">
                                التالي
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- نافذة إضافة عميل جديد -->
<div class="modal fade" id="addCustomerModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addCustomerForm" class="needs-validation" novalidate>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customerName" class="form-label">اسم العميل *</label>
                            <input type="text" class="form-control" id="customerName" required>
                            <div class="invalid-feedback">يرجى إدخال اسم العميل</div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="customerPhone" class="form-label">رقم الهاتف</label>
                            <input type="tel" class="form-control" id="customerPhone">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="customerEmail" class="form-label">البريد الإلكتروني</label>
                            <input type="email" class="form-control" id="customerEmail">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="creditLimit" class="form-label">الحد الائتماني</label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="creditLimit" value="0" min="0" step="0.01">
                                <span class="input-group-text">ر.س</span>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="customerAddress" class="form-label">العنوان</label>
                        <textarea class="form-control" id="customerAddress" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ العميل</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إضافة عميل جديد
document.getElementById('addCustomerForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    if (!this.checkValidity()) {
        e.stopPropagation();
        this.classList.add('was-validated');
        return;
    }
    
    const formData = {
        name: document.getElementById('customerName').value,
        phone: document.getElementById('customerPhone').value,
        email: document.getElementById('customerEmail').value,
        address: document.getElementById('customerAddress').value,
        credit_limit: parseFloat(document.getElementById('creditLimit').value) || 0
    };
    
    try {
        const response = await fetch('/api/customers', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        if (response.ok) {
            location.reload();
        } else {
            alert('حدث خطأ في إضافة العميل');
        }
    } catch (error) {
        console.error('Error:', error);
        alert('حدث خطأ في الاتصال');
    }
});

// عرض تفاصيل العميل
function viewCustomer(customerId) {
    // سيتم تنفيذها لاحقاً
    console.log('View customer:', customerId);
}

// تعديل العميل
function editCustomer(customerId) {
    // سيتم تنفيذها لاحقاً
    console.log('Edit customer:', customerId);
}

// حذف العميل
function deleteCustomer(customerId) {
    if (confirm('هل أنت متأكد من حذف هذا العميل؟')) {
        // سيتم تنفيذها لاحقاً
        console.log('Delete customer:', customerId);
    }
}
</script>
{% endblock %}
