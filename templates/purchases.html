{% extends "base.html" %}

{% block title %}إدارة المشتريات{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-shopping-cart"></i> إدارة المشتريات
        </h1>
        <a href="{{ url_for('new_purchase') }}" class="btn btn-primary btn-sm shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> فاتورة شراء جديدة
        </a>
    </div>

    <!-- إحصائيات سريعة -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                إجمالي المشتريات (الشهر)
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="monthlyPurchases">
                                0.000 د.ل <span class="libya-flag"></span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-shopping-cart fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                فواتير مدفوعة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="paidInvoices">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-check-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                فواتير معلقة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="pendingInvoices">0</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">
                                مبالغ مستحقة
                            </div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800" id="outstandingAmount">
                                0.000 د.ل <span class="libya-flag"></span>
                            </div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-exclamation-triangle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- جدول فواتير الشراء -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">فواتير الشراء</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="purchasesTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>المورد</th>
                            <th>تاريخ الفاتورة</th>
                            <th>تاريخ الاستحقاق</th>
                            <th>المبلغ الإجمالي</th>
                            <th>المبلغ المدفوع</th>
                            <th>المتبقي</th>
                            <th>حالة الدفع</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for purchase in purchases %}
                        <tr>
                            <td>{{ purchase.invoice_number }}</td>
                            <td>{{ purchase.supplier.name }}</td>
                            <td>{{ purchase.invoice_date.strftime('%Y-%m-%d') }}</td>
                            <td>
                                {% if purchase.due_date %}
                                {{ purchase.due_date.strftime('%Y-%m-%d') }}
                                {% else %}
                                -
                                {% endif %}
                            </td>
                            <td>
                                <span class="text-primary fw-bold">
                                    {{ "%.3f"|format(purchase.total_amount) }} د.ل
                                    <span class="libya-flag"></span>
                                </span>
                            </td>
                            <td>
                                <span class="text-success fw-bold">
                                    {{ "%.3f"|format(purchase.paid_amount) }} د.ل
                                    <span class="libya-flag"></span>
                                </span>
                            </td>
                            <td>
                                {% set remaining = purchase.total_amount - purchase.paid_amount %}
                                {% if remaining > 0 %}
                                <span class="text-danger fw-bold">
                                    {{ "%.3f"|format(remaining) }} د.ل
                                    <span class="libya-flag"></span>
                                </span>
                                {% else %}
                                <span class="text-muted">0.000 د.ل</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if purchase.payment_status == 'paid' %}
                                <span class="badge badge-success">مدفوعة</span>
                                {% elif purchase.payment_status == 'partial' %}
                                <span class="badge badge-warning">جزئية</span>
                                {% else %}
                                <span class="badge badge-danger">غير مدفوعة</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if purchase.status == 'received' %}
                                <span class="badge badge-success">مستلمة</span>
                                {% elif purchase.status == 'pending' %}
                                <span class="badge badge-warning">معلقة</span>
                                {% else %}
                                <span class="badge badge-secondary">ملغية</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button type="button" class="btn btn-sm btn-outline-info" 
                                            onclick="viewPurchaseDetails({{ purchase.id }})" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-success" 
                                            onclick="printPurchase({{ purchase.id }})" title="طباعة">
                                        <i class="fas fa-print"></i>
                                    </button>
                                    {% if purchase.payment_status != 'paid' %}
                                    <button type="button" class="btn btn-sm btn-outline-warning" 
                                            onclick="recordPayment({{ purchase.id }})" title="تسجيل دفعة">
                                        <i class="fas fa-money-bill"></i>
                                    </button>
                                    {% endif %}
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#purchasesTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 2, "desc" ]],
        "pageLength": 25
    });
    
    // تحميل الإحصائيات
    loadPurchaseStats();
});

function loadPurchaseStats() {
    // يمكن إضافة AJAX لجلب الإحصائيات
    // مؤقتاً سنحسب من الجدول
    var totalPurchases = 0;
    var paidCount = 0;
    var pendingCount = 0;
    var outstandingTotal = 0;
    
    $('#purchasesTable tbody tr').each(function() {
        var total = parseFloat($(this).find('td:eq(4)').text().replace(/[^\d.]/g, ''));
        var paid = parseFloat($(this).find('td:eq(5)').text().replace(/[^\d.]/g, ''));
        var remaining = total - paid;
        
        totalPurchases += total;
        if (remaining <= 0) paidCount++;
        else pendingCount++;
        outstandingTotal += remaining;
    });
    
    $('#monthlyPurchases').html(totalPurchases.toFixed(3) + ' د.ل <span class="libya-flag"></span>');
    $('#paidInvoices').text(paidCount);
    $('#pendingInvoices').text(pendingCount);
    $('#outstandingAmount').html(outstandingTotal.toFixed(3) + ' د.ل <span class="libya-flag"></span>');
}

function viewPurchaseDetails(purchaseId) {
    // عرض تفاصيل فاتورة الشراء
    window.open('/purchases/' + purchaseId + '/details', '_blank');
}

function printPurchase(purchaseId) {
    // طباعة فاتورة الشراء
    window.open('/purchases/' + purchaseId + '/print', '_blank');
}

function recordPayment(purchaseId) {
    // تسجيل دفعة
    alert('سيتم إضافة نافذة تسجيل الدفعات قريباً');
}
</script>

<style>
.libya-flag {
    display: inline-block;
    width: 16px;
    height: 12px;
    background: linear-gradient(to bottom, #e4002b 33%, #000000 33%, #000000 66%, #009639 66%);
    margin-left: 5px;
    border-radius: 2px;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    margin-right: 2px;
}

.border-left-primary {
    border-left: 0.25rem solid #4e73df !important;
}

.border-left-success {
    border-left: 0.25rem solid #1cc88a !important;
}

.border-left-warning {
    border-left: 0.25rem solid #f6c23e !important;
}

.border-left-danger {
    border-left: 0.25rem solid #e74a3b !important;
}
</style>
{% endblock %}
