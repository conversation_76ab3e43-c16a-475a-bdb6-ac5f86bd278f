{% extends "base.html" %}

{% block title %}تعديل المورد - {{ supplier.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-edit"></i> تعديل المورد: {{ supplier.name }}
        </h1>
        <a href="{{ url_for('suppliers') }}" class="btn btn-secondary btn-sm shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> العودة للموردين
        </a>
    </div>

    <!-- نموذج تعديل المورد -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">بيانات المورد</h6>
                </div>
                <div class="card-body">
                    <form method="POST" id="supplierForm">
                        <div class="row">
                            <!-- الاسم -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المورد <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" 
                                       value="{{ supplier.name }}" required>
                            </div>

                            <!-- اسم الشركة -->
                            <div class="col-md-6 mb-3">
                                <label for="company_name" class="form-label">اسم الشركة</label>
                                <input type="text" class="form-control" id="company_name" name="company_name" 
                                       value="{{ supplier.company_name or '' }}">
                            </div>

                            <!-- الهاتف -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       value="{{ supplier.phone or '' }}"
                                       pattern="^(\+218|0)[0-9]{9}$" placeholder="+218xxxxxxxxx">
                                <div class="form-text">مثال: +218912345678</div>
                            </div>

                            <!-- البريد الإلكتروني -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="{{ supplier.email or '' }}">
                            </div>

                            <!-- العنوان -->
                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="3">{{ supplier.address or '' }}</textarea>
                            </div>

                            <!-- الرقم الضريبي -->
                            <div class="col-md-6 mb-3">
                                <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="tax_number" name="tax_number" 
                                       value="{{ supplier.tax_number or '' }}">
                            </div>

                            <!-- الحد الائتماني -->
                            <div class="col-md-6 mb-3">
                                <label for="credit_limit" class="form-label">الحد الائتماني</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="credit_limit" name="credit_limit" 
                                           min="0" step="0.001" value="{{ supplier.credit_limit or 0 }}">
                                    <div class="input-group-append">
                                        <span class="input-group-text">
                                            د.ل <span class="libya-flag"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>

                            <!-- الحالة -->
                            <div class="col-12 mb-3">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="is_active" name="is_active" 
                                           {% if supplier.is_active %}checked{% endif %}>
                                    <label class="form-check-label" for="is_active">
                                        المورد نشط
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ التغييرات
                                </button>
                                <a href="{{ url_for('suppliers') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معلومات المورد -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">معلومات المورد</h6>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12 mb-2">
                            <strong>الرصيد الحالي:</strong>
                            {% if supplier.current_balance > 0 %}
                            <span class="text-danger fw-bold">
                                {{ "%.3f"|format(supplier.current_balance) }} د.ل
                                <span class="libya-flag"></span> (مستحق)
                            </span>
                            {% elif supplier.current_balance < 0 %}
                            <span class="text-success fw-bold">
                                {{ "%.3f"|format(abs(supplier.current_balance)) }} د.ل
                                <span class="libya-flag"></span> (رصيد)
                            </span>
                            {% else %}
                            <span class="text-muted">
                                0.000 د.ل <span class="libya-flag"></span>
                            </span>
                            {% endif %}
                        </div>
                        <div class="col-12 mb-2">
                            <strong>تاريخ الإنشاء:</strong>
                            {{ supplier.created_at.strftime('%Y-%m-%d %H:%M') }}
                        </div>
                        <div class="col-12 mb-2">
                            <strong>عدد فواتير الشراء:</strong>
                            {{ supplier.purchase_invoices|length }}
                        </div>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">إجراءات سريعة</h6>
                </div>
                <div class="card-body">
                    <a href="{{ url_for('new_purchase') }}?supplier_id={{ supplier.id }}" 
                       class="btn btn-success btn-sm btn-block mb-2">
                        <i class="fas fa-plus"></i> فاتورة شراء جديدة
                    </a>
                    <a href="{{ url_for('purchases') }}?supplier_id={{ supplier.id }}" 
                       class="btn btn-info btn-sm btn-block mb-2">
                        <i class="fas fa-list"></i> عرض فواتير الشراء
                    </a>
                    <button class="btn btn-warning btn-sm btn-block" onclick="showPaymentModal()">
                        <i class="fas fa-money-bill"></i> تسجيل دفعة
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // التحقق من صحة النموذج
    $('#supplierForm').on('submit', function(e) {
        var name = $('#name').val().trim();
        if (!name) {
            e.preventDefault();
            alert('يرجى إدخال اسم المورد');
            $('#name').focus();
            return false;
        }
        
        var phone = $('#phone').val().trim();
        if (phone && !phone.match(/^(\+218|0)[0-9]{9}$/)) {
            e.preventDefault();
            alert('يرجى إدخال رقم هاتف ليبي صحيح');
            $('#phone').focus();
            return false;
        }
    });

    // تنسيق رقم الهاتف
    $('#phone').on('input', function() {
        var value = $(this).val().replace(/\D/g, '');
        if (value.startsWith('218')) {
            $(this).val('+' + value);
        } else if (value.startsWith('0')) {
            $(this).val(value);
        }
    });
});

function showPaymentModal() {
    // يمكن إضافة modal لتسجيل الدفعات
    alert('سيتم إضافة نافذة تسجيل الدفعات قريباً');
}
</script>

<style>
.libya-flag {
    display: inline-block;
    width: 16px;
    height: 12px;
    background: linear-gradient(to bottom, #e4002b 33%, #000000 33%, #000000 66%, #009639 66%);
    margin-left: 5px;
    border-radius: 2px;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
}

.card-header h6 {
    color: #5a5c69;
}
</style>
{% endblock %}
