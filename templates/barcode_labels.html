{% extends "base.html" %}

{% block title %}طباعة ملصقات الباركود{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-barcode text-primary me-2"></i>
                طباعة ملصقات الباركود
            </h2>
            <p class="text-muted mb-0">إنشاء وطباعة ملصقات باركود للمنتجات</p>
        </div>
        <div>
            <button class="btn btn-primary" onclick="generateLabels()" id="generateBtn" disabled>
                <i class="fas fa-print me-2"></i>إنشاء الملصقات
            </button>
        </div>
    </div>

    <!-- إعدادات الطباعة -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-cog me-2"></i>إعدادات الطباعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label for="labelsPerRow" class="form-label">عدد الملصقات في الصف</label>
                        <select class="form-select" id="labelsPerRow">
                            <option value="2">2 ملصقات</option>
                            <option value="3" selected>3 ملصقات</option>
                            <option value="4">4 ملصقات</option>
                            <option value="5">5 ملصقات</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">المنتجات المحددة</label>
                        <div class="alert alert-info">
                            <span id="selectedCount">0</span> منتج محدد
                        </div>
                    </div>

                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" onclick="selectAll()">
                            <i class="fas fa-check-square me-2"></i>تحديد الكل
                        </button>
                        <button class="btn btn-outline-secondary" onclick="clearSelection()">
                            <i class="fas fa-square me-2"></i>إلغاء التحديد
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <!-- معاينة الملصقات -->
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-eye me-2"></i>معاينة الملصقات
                    </h5>
                </div>
                <div class="card-body">
                    <div id="labelsPreview" class="text-center text-muted">
                        <i class="fas fa-barcode fa-3x mb-3"></i>
                        <p>حدد المنتجات واضغط "إنشاء الملصقات" لعرض المعاينة</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- قائمة المنتجات -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-list me-2"></i>المنتجات المتاحة ({{ products|length }})
            </h5>
        </div>
        <div class="card-body p-0">
            {% if products %}
            <div class="table-responsive">
                <table class="table table-hover mb-0">
                    <thead class="table-light">
                        <tr>
                            <th width="50">
                                <input type="checkbox" id="selectAllCheckbox" onchange="toggleSelectAll()">
                            </th>
                            <th>المنتج</th>
                            <th>الباركود</th>
                            <th>التصنيف</th>
                            <th>الكمية</th>
                            <th>السعر</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for product in products %}
                        <tr>
                            <td>
                                <input type="checkbox" class="product-checkbox"
                                       value="{{ product.id }}" onchange="updateSelection()">
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <div class="icon-circle bg-primary text-white me-3">
                                        <i class="fas fa-box"></i>
                                    </div>
                                    <div>
                                        <h6 class="mb-0">{{ product.name }}</h6>
                                        <small class="text-muted">{{ product.unit }}</small>
                                    </div>
                                </div>
                            </td>
                            <td>
                                <code class="text-primary">{{ product.barcode }}</code>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ product.category.name }}</span>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'danger' if product.stock_quantity <= product.min_quantity else 'success' }}">
                                    {{ product.stock_quantity }}
                                </span>
                            </td>
                            <td>
                                <span class="text-success price-display">
                                    <span class="currency-lyd">{{ "%.2f"|format(product.selling_price) }}</span>
                                    <span class="libya-flag"></span>
                                </span>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            {% else %}
            <div class="text-center py-5">
                <i class="fas fa-barcode fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">لا توجد منتجات بباركود</h5>
                <p class="text-muted">يجب إضافة باركود للمنتجات أولاً</p>
                <a href="{{ url_for('products') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>إدارة المنتجات
                </a>
            </div>
            {% endif %}
        </div>
    </div>
</div>

<!-- نافذة منبثقة لعرض الملصقات -->
<div class="modal fade" id="labelsModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-barcode me-2"></i>ملصقات الباركود
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="labelsContent" class="text-center">
                    <!-- سيتم إدراج الملصقات هنا -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                <button type="button" class="btn btn-primary" onclick="printLabels()">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
                <button type="button" class="btn btn-success" onclick="downloadLabels()">
                    <i class="fas fa-download me-2"></i>تحميل
                </button>
            </div>
        </div>
    </div>
</div>

<script>
let selectedProducts = [];
let currentLabelsImage = '';

// تحديث التحديد
function updateSelection() {
    const checkboxes = document.querySelectorAll('.product-checkbox:checked');
    selectedProducts = Array.from(checkboxes).map(cb => parseInt(cb.value));

    document.getElementById('selectedCount').textContent = selectedProducts.length;
    document.getElementById('generateBtn').disabled = selectedProducts.length === 0;

    // تحديث حالة "تحديد الكل"
    const allCheckboxes = document.querySelectorAll('.product-checkbox');
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');

    if (selectedProducts.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
    } else if (selectedProducts.length === allCheckboxes.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
    } else {
        selectAllCheckbox.indeterminate = true;
    }
}

// تبديل تحديد الكل
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAllCheckbox');
    const productCheckboxes = document.querySelectorAll('.product-checkbox');

    productCheckboxes.forEach(checkbox => {
        checkbox.checked = selectAllCheckbox.checked;
    });

    updateSelection();
}

// تحديد الكل
function selectAll() {
    document.querySelectorAll('.product-checkbox').forEach(checkbox => {
        checkbox.checked = true;
    });
    updateSelection();
}

// إلغاء التحديد
function clearSelection() {
    document.querySelectorAll('.product-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    updateSelection();
}

// إنشاء الملصقات
async function generateLabels() {
    if (selectedProducts.length === 0) {
        alert('يرجى تحديد منتجات أولاً');
        return;
    }

    const labelsPerRow = parseInt(document.getElementById('labelsPerRow').value);

    try {
        // إظهار مؤشر التحميل
        document.getElementById('generateBtn').innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإنشاء...';
        document.getElementById('generateBtn').disabled = true;

        const response = await fetch('/api/barcode/labels/generate', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                product_ids: selectedProducts,
                labels_per_row: labelsPerRow
            })
        });

        const result = await response.json();

        if (result.success) {
            currentLabelsImage = result.labels_image;

            // عرض المعاينة
            document.getElementById('labelsPreview').innerHTML = `
                <img src="data:image/png;base64,${result.labels_image}"
                     alt="Barcode Labels" class="img-fluid" style="max-width: 100%;">
            `;

            // عرض النافذة المنبثقة
            document.getElementById('labelsContent').innerHTML = `
                <img src="data:image/png;base64,${result.labels_image}"
                     alt="Barcode Labels" class="img-fluid" style="max-width: 100%;">
            `;

            const modal = new bootstrap.Modal(document.getElementById('labelsModal'));
            modal.show();

        } else {
            alert('خطأ: ' + result.error);
        }
    } catch (error) {
        console.error('Error:', error);
        alert('حدث خطأ في إنشاء الملصقات');
    } finally {
        // إعادة تعيين الزر
        document.getElementById('generateBtn').innerHTML = '<i class="fas fa-print me-2"></i>إنشاء الملصقات';
        document.getElementById('generateBtn').disabled = selectedProducts.length === 0;
    }
}

// طباعة الملصقات
function printLabels() {
    if (!currentLabelsImage) {
        alert('لا توجد ملصقات للطباعة');
        return;
    }

    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
            <head>
                <title>طباعة ملصقات الباركود</title>
                <style>
                    body {
                        margin: 0;
                        padding: 20px;
                        text-align: center;
                    }
                    img {
                        max-width: 100%;
                        height: auto;
                    }
                    @media print {
                        body { margin: 0; padding: 0; }
                    }
                </style>
            </head>
            <body>
                <img src="data:image/png;base64,${currentLabelsImage}" alt="Barcode Labels">
            </body>
        </html>
    `);
    printWindow.document.close();
    printWindow.print();
}

// تحميل الملصقات
function downloadLabels() {
    if (!currentLabelsImage) {
        alert('لا توجد ملصقات للتحميل');
        return;
    }

    const link = document.createElement('a');
    link.href = `data:image/png;base64,${currentLabelsImage}`;
    link.download = `barcode_labels_${new Date().toISOString().slice(0, 10)}.png`;
    link.click();
}

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateSelection();
});
</script>
{% endblock %}
