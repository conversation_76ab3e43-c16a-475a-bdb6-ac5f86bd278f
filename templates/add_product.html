{% extends "base.html" %}

{% block title %}إضافة منتج جديد - نظام إدارة المبيعات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-plus me-2"></i>إضافة منتج جديد
            </h1>
            <a href="{{ url_for('products') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للمنتجات
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-box me-2"></i>بيانات المنتج
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- اسم المنتج -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                <i class="fas fa-tag me-1"></i>اسم المنتج *
                            </label>
                            <input type="text" class="form-control" id="name" name="name" required>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم المنتج
                            </div>
                        </div>

                        <!-- الباركود -->
                        <div class="col-md-6 mb-3">
                            <label for="barcode" class="form-label">
                                <i class="fas fa-barcode me-1"></i>الباركود
                            </label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="barcode" name="barcode">
                                <button class="btn btn-outline-secondary" type="button" onclick="generateBarcode()">
                                    <i class="fas fa-random"></i>
                                </button>
                            </div>
                            <small class="form-text text-muted">اتركه فارغاً لتوليد باركود تلقائي</small>
                        </div>
                    </div>

                    <div class="row">
                        <!-- التصنيف -->
                        <div class="col-md-6 mb-3">
                            <label for="category_id" class="form-label">
                                <i class="fas fa-folder me-1"></i>التصنيف *
                            </label>
                            <select class="form-select" id="category_id" name="category_id" required>
                                <option value="">اختر التصنيف</option>
                                {% for category in categories %}
                                <option value="{{ category.id }}">{{ category.name }}</option>
                                {% endfor %}
                            </select>
                            <div class="invalid-feedback">
                                يرجى اختيار التصنيف
                            </div>
                        </div>

                        <!-- الوحدة -->
                        <div class="col-md-6 mb-3">
                            <label for="unit" class="form-label">
                                <i class="fas fa-balance-scale me-1"></i>الوحدة *
                            </label>
                            <select class="form-select" id="unit" name="unit" required>
                                <option value="قطعة">قطعة</option>
                                <option value="كيلو">كيلو</option>
                                <option value="لتر">لتر</option>
                                <option value="متر">متر</option>
                                <option value="علبة">علبة</option>
                                <option value="كرتون">كرتون</option>
                            </select>
                        </div>
                    </div>

                    <div class="row">
                        <!-- سعر الشراء -->
                        <div class="col-md-6 mb-3">
                            <label for="purchase_price" class="form-label">
                                <i class="fas fa-dollar-sign me-1"></i>سعر الشراء
                                <span class="libya-flag"></span> *
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="purchase_price"
                                       name="purchase_price" step="0.01" min="0" required>
                                <span class="input-group-text">
                                    <span class="libya-flag"></span> د.ل
                                </span>
                            </div>
                            <div class="invalid-feedback">
                                يرجى إدخال سعر الشراء
                            </div>
                        </div>

                        <!-- سعر البيع -->
                        <div class="col-md-6 mb-3">
                            <label for="selling_price" class="form-label">
                                <i class="fas fa-money-bill me-1"></i>سعر البيع
                                <span class="libya-flag"></span> *
                            </label>
                            <div class="input-group">
                                <input type="number" class="form-control" id="selling_price"
                                       name="selling_price" step="0.01" min="0" required>
                                <span class="input-group-text">
                                    <span class="libya-flag"></span> د.ل
                                </span>
                            </div>
                            <div class="invalid-feedback">
                                يرجى إدخال سعر البيع
                            </div>
                            <small class="form-text text-muted">
                                هامش الربح: <span id="profit_margin">0%</span>
                            </small>
                        </div>
                    </div>

                    <div class="row">
                        <!-- الكمية الابتدائية -->
                        <div class="col-md-6 mb-3">
                            <label for="quantity" class="form-label">
                                <i class="fas fa-cubes me-1"></i>الكمية الابتدائية *
                            </label>
                            <input type="number" class="form-control" id="quantity"
                                   name="quantity" min="0" value="0" required>
                            <div class="invalid-feedback">
                                يرجى إدخال الكمية
                            </div>
                        </div>

                        <!-- الحد الأدنى -->
                        <div class="col-md-6 mb-3">
                            <label for="min_quantity" class="form-label">
                                <i class="fas fa-exclamation-triangle me-1"></i>الحد الأدنى للتنبيه *
                            </label>
                            <input type="number" class="form-control" id="min_quantity"
                                   name="min_quantity" min="0" value="5" required>
                            <div class="invalid-feedback">
                                يرجى إدخال الحد الأدنى
                            </div>
                        </div>
                    </div>

                    <!-- الوصف -->
                    <div class="mb-3">
                        <label for="description" class="form-label">
                            <i class="fas fa-align-left me-1"></i>الوصف
                        </label>
                        <textarea class="form-control" id="description" name="description"
                                  rows="3" placeholder="وصف المنتج (اختياري)"></textarea>
                    </div>

                    <!-- أزرار الإجراءات -->
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                        <div>
                            <a href="{{ url_for('products') }}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ المنتج
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// حساب هامش الربح
function calculateProfitMargin() {
    const purchasePrice = parseFloat(document.getElementById('purchase_price').value) || 0;
    const sellingPrice = parseFloat(document.getElementById('selling_price').value) || 0;

    if (purchasePrice > 0) {
        const margin = ((sellingPrice - purchasePrice) / purchasePrice * 100).toFixed(2);
        document.getElementById('profit_margin').textContent = margin + '%';

        // تغيير لون النص حسب الهامش
        const marginElement = document.getElementById('profit_margin');
        if (margin < 0) {
            marginElement.className = 'text-danger';
        } else if (margin < 10) {
            marginElement.className = 'text-warning';
        } else {
            marginElement.className = 'text-success';
        }
    }
}

// توليد باركود عشوائي
function generateBarcode() {
    const barcode = Date.now().toString() + Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    document.getElementById('barcode').value = barcode;
}

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
        document.querySelector('form').reset();
        document.getElementById('profit_margin').textContent = '0%';
        document.getElementById('profit_margin').className = '';
    }
}

// إضافة مستمعي الأحداث
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('purchase_price').addEventListener('input', calculateProfitMargin);
    document.getElementById('selling_price').addEventListener('input', calculateProfitMargin);

    // تحديد سعر البيع تلقائياً بناءً على سعر الشراء
    document.getElementById('purchase_price').addEventListener('change', function() {
        const purchasePrice = parseFloat(this.value) || 0;
        const sellingPriceField = document.getElementById('selling_price');

        if (purchasePrice > 0 && !sellingPriceField.value) {
            // إضافة هامش ربح افتراضي 20%
            const suggestedPrice = (purchasePrice * 1.2).toFixed(2);
            sellingPriceField.value = suggestedPrice;
            calculateProfitMargin();
        }
    });
});
</script>
{% endblock %}
