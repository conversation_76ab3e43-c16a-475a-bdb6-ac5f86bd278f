{% extends "base.html" %}

{% block title %}إضافة عميل جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-user-plus text-primary me-2"></i>
                إضافة عميل جديد
            </h2>
            <p class="text-muted mb-0">إضافة عميل جديد إلى النظام</p>
        </div>
        <div>
            <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للعملاء
            </a>
        </div>
    </div>

    <!-- نموذج إضافة العميل -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-plus me-2"></i>بيانات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <!-- اسم العميل -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user me-1"></i>اسم العميل *
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال اسم العميل
                                </div>
                            </div>

                            <!-- رقم الهاتف -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone me-1"></i>رقم الهاتف
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       placeholder="05xxxxxxxx">
                                <div class="form-text">مثال: 0501234567</div>
                            </div>

                            <!-- البريد الإلكتروني -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>البريد الإلكتروني
                                </label>
                                <input type="email" class="form-control" id="email" name="email"
                                       placeholder="<EMAIL>">
                            </div>

                            <!-- الحد الائتماني -->
                            <div class="col-md-6 mb-3">
                                <label for="credit_limit" class="form-label">
                                    <i class="fas fa-credit-card me-1"></i>الحد الائتماني
                                    <span class="libya-flag"></span> (د.ل)
                                </label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="credit_limit" name="credit_limit"
                                           min="0" step="0.001" value="0">
                                    <span class="input-group-text">
                                        <span class="libya-flag"></span> د.ل
                                    </span>
                                </div>
                                <div class="form-text">الحد الأقصى للدين المسموح به بالدينار الليبي</div>
                            </div>

                            <!-- العنوان -->
                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>العنوان
                                </label>
                                <textarea class="form-control" id="address" name="address" rows="3"
                                          placeholder="العنوان الكامل للعميل"></textarea>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ العميل
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تنسيق رقم الهاتف
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 10) {
        value = value.substring(0, 10);
    }
    e.target.value = value;
});

// التحقق من البريد الإلكتروني
document.getElementById('email').addEventListener('blur', function(e) {
    const email = e.target.value;
    if (email && !email.includes('@')) {
        e.target.setCustomValidity('يرجى إدخال بريد إلكتروني صحيح');
    } else {
        e.target.setCustomValidity('');
    }
});
</script>
{% endblock %}
