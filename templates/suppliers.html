{% extends "base.html" %}

{% block title %}إدارة الموردين{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-truck"></i> إدارة الموردين
        </h1>
        <a href="{{ url_for('add_supplier') }}" class="btn btn-primary btn-sm shadow-sm">
            <i class="fas fa-plus fa-sm text-white-50"></i> إضافة مورد جديد
        </a>
    </div>

    <!-- جدول الموردين -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">قائمة الموردين</h6>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-bordered" id="suppliersTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>الاسم</th>
                            <th>اسم الشركة</th>
                            <th>الهاتف</th>
                            <th>البريد الإلكتروني</th>
                            <th>الحد الائتماني</th>
                            <th>الرصيد الحالي</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for supplier in suppliers %}
                        <tr>
                            <td>{{ supplier.name }}</td>
                            <td>{{ supplier.company_name or '-' }}</td>
                            <td>{{ supplier.phone or '-' }}</td>
                            <td>{{ supplier.email or '-' }}</td>
                            <td>
                                <span class="text-info fw-bold">
                                    {{ "%.3f"|format(supplier.credit_limit) }} د.ل
                                    <span class="libya-flag"></span>
                                </span>
                            </td>
                            <td>
                                {% if supplier.current_balance > 0 %}
                                <span class="text-danger fw-bold">
                                    {{ "%.3f"|format(supplier.current_balance) }} د.ل
                                    <span class="libya-flag"></span>
                                </span>
                                {% elif supplier.current_balance < 0 %}
                                <span class="text-success fw-bold">
                                    {{ "%.3f"|format(abs(supplier.current_balance)) }} د.ل
                                    <span class="libya-flag"></span>
                                </span>
                                {% else %}
                                <span class="text-muted">0.000 د.ل</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if supplier.is_active %}
                                <span class="badge badge-success">نشط</span>
                                {% else %}
                                <span class="badge badge-secondary">غير نشط</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ url_for('edit_supplier', supplier_id=supplier.id) }}" 
                                       class="btn btn-sm btn-outline-primary" title="تعديل">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button type="button" class="btn btn-sm btn-outline-info" 
                                            onclick="viewSupplierDetails({{ supplier.id }})" title="عرض التفاصيل">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <a href="{{ url_for('purchases') }}?supplier_id={{ supplier.id }}" 
                                       class="btn btn-sm btn-outline-success" title="فواتير الشراء">
                                        <i class="fas fa-file-invoice"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Modal تفاصيل المورد -->
<div class="modal fade" id="supplierDetailsModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تفاصيل المورد</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="supplierDetailsContent">
                <!-- سيتم تحميل التفاصيل هنا -->
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#suppliersTable').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Arabic.json"
        },
        "order": [[ 0, "asc" ]],
        "pageLength": 25
    });
});

function viewSupplierDetails(supplierId) {
    // يمكن إضافة AJAX لجلب تفاصيل المورد
    $('#supplierDetailsModal').modal('show');
}
</script>

<style>
.libya-flag {
    display: inline-block;
    width: 16px;
    height: 12px;
    background: linear-gradient(to bottom, #e4002b 33%, #000000 33%, #000000 66%, #009639 66%);
    margin-left: 5px;
    border-radius: 2px;
}

.table td {
    vertical-align: middle;
}

.btn-group .btn {
    margin-right: 2px;
}
</style>
{% endblock %}
