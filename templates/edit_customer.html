{% extends "base.html" %}

{% block title %}تعديل العميل - {{ customer.name }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-user-edit text-warning me-2"></i>
                تعديل العميل
            </h2>
            <p class="text-muted mb-0">تعديل بيانات العميل: {{ customer.name }}</p>
        </div>
        <div>
            <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للعملاء
            </a>
        </div>
    </div>

    <!-- نموذج تعديل العميل -->
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card shadow-sm">
                <div class="card-header bg-warning text-dark">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-edit me-2"></i>تعديل بيانات العميل
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" class="needs-validation" novalidate>
                        <div class="row">
                            <!-- اسم العميل -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user me-1"></i>اسم العميل *
                                </label>
                                <input type="text" class="form-control" id="name" name="name"
                                       value="{{ customer.name }}" required>
                                <div class="invalid-feedback">
                                    يرجى إدخال اسم العميل
                                </div>
                            </div>

                            <!-- رقم الهاتف -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone me-1"></i>رقم الهاتف
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone"
                                       value="{{ customer.phone or '' }}" placeholder="05xxxxxxxx">
                                <div class="form-text">مثال: 0501234567</div>
                            </div>

                            <!-- البريد الإلكتروني -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope me-1"></i>البريد الإلكتروني
                                </label>
                                <input type="email" class="form-control" id="email" name="email"
                                       value="{{ customer.email or '' }}" placeholder="<EMAIL>">
                            </div>

                            <!-- الحد الائتماني -->
                            <div class="col-md-6 mb-3">
                                <label for="credit_limit" class="form-label">
                                    <i class="fas fa-credit-card me-1"></i>الحد الائتماني
                                    <span class="libya-flag"></span> (د.ل)
                                </label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="credit_limit" name="credit_limit"
                                           min="0" step="0.001" value="{{ customer.credit_limit or 0 }}">
                                    <span class="input-group-text">
                                        <span class="libya-flag"></span> د.ل
                                    </span>
                                </div>
                                <div class="form-text">الحد الأقصى للدين المسموح به بالدينار الليبي</div>
                            </div>

                            <!-- العنوان -->
                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">
                                    <i class="fas fa-map-marker-alt me-1"></i>العنوان
                                </label>
                                <textarea class="form-control" id="address" name="address" rows="3"
                                          placeholder="العنوان الكامل للعميل">{{ customer.address or '' }}</textarea>
                            </div>
                        </div>

                        <!-- معلومات إضافية -->
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="fas fa-wallet me-1"></i>الرصيد الحالي
                                    <span class="libya-flag"></span>
                                </label>
                                <div class="form-control-plaintext price-display">
                                    {% if customer.current_balance and customer.current_balance > 0 %}
                                    <span class="text-danger fw-bold">
                                        <span class="currency-lyd">{{ "%.3f"|format(customer.current_balance) }}</span> د.ل
                                        <span class="libya-flag"></span> (دين)
                                    </span>
                                    {% elif customer.current_balance and customer.current_balance < 0 %}
                                    <span class="text-success fw-bold">
                                        <span class="currency-lyd">{{ "%.3f"|format(abs(customer.current_balance)) }}</span> د.ل
                                        <span class="libya-flag"></span> (رصيد)
                                    </span>
                                    {% else %}
                                    <span class="text-muted">
                                        <span class="currency-lyd">0.000</span> د.ل
                                        <span class="libya-flag"></span>
                                    </span>
                                    {% endif %}
                                </div>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label">
                                    <i class="fas fa-calendar me-1"></i>تاريخ الإنشاء
                                </label>
                                <div class="form-control-plaintext">
                                    {{ customer.created_at.strftime('%Y-%m-%d %H:%M') }}
                                </div>
                            </div>
                        </div>

                        <!-- أزرار التحكم -->
                        <div class="d-flex justify-content-between">
                            <a href="{{ url_for('customers') }}" class="btn btn-outline-secondary">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-warning">
                                <i class="fas fa-save me-2"></i>حفظ التعديلات
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// التحقق من صحة النموذج
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();

// تنسيق رقم الهاتف
document.getElementById('phone').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length > 10) {
        value = value.substring(0, 10);
    }
    e.target.value = value;
});

// التحقق من البريد الإلكتروني
document.getElementById('email').addEventListener('blur', function(e) {
    const email = e.target.value;
    if (email && !email.includes('@')) {
        e.target.setCustomValidity('يرجى إدخال بريد إلكتروني صحيح');
    } else {
        e.target.setCustomValidity('');
    }
});
</script>
{% endblock %}
