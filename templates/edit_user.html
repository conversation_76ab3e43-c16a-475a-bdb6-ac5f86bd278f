{% extends "base.html" %}

{% block title %}تعديل المستخدم - نظام إدارة المبيعات{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">
                <i class="fas fa-user-edit me-2"></i>تعديل المستخدم: {{ user.username }}
            </h1>
            <a href="{{ url_for('users') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>العودة للمستخدمين
            </a>
        </div>
    </div>
</div>

<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user me-2"></i>تعديل بيانات المستخدم
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" class="needs-validation" novalidate>
                    <div class="row">
                        <!-- اسم المستخدم -->
                        <div class="col-md-6 mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user me-1"></i>اسم المستخدم *
                            </label>
                            <input type="text" class="form-control" id="username" name="username"
                                   value="{{ user.username }}" required>
                            <div class="invalid-feedback">
                                يرجى إدخال اسم المستخدم
                            </div>
                        </div>

                        <!-- البريد الإلكتروني -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-1"></i>البريد الإلكتروني *
                            </label>
                            <input type="email" class="form-control" id="email" name="email"
                                   value="{{ user.email }}" required>
                            <div class="invalid-feedback">
                                يرجى إدخال بريد إلكتروني صحيح
                            </div>
                        </div>
                    </div>

                    <!-- الدور -->
                    <div class="mb-3">
                        <label for="role" class="form-label">
                            <i class="fas fa-user-tag me-1"></i>الدور *
                        </label>
                        <select class="form-select" id="role" name="role" required>
                            <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>
                                مدير - صلاحيات كاملة
                            </option>
                            <option value="seller" {% if user.role == 'seller' %}selected{% endif %}>
                                بائع - إنشاء فواتير وعرض البيانات
                            </option>
                            <option value="warehouse_manager" {% if user.role == 'warehouse_manager' %}selected{% endif %}>
                                مشرف مخزن - إدارة المنتجات والمخزون
                            </option>
                        </select>
                        <div class="invalid-feedback">
                            يرجى اختيار الدور
                        </div>
                    </div>

                    <!-- تغيير كلمة المرور -->
                    <div class="card mb-4">
                        <div class="card-header">
                            <h6 class="card-title mb-0">
                                <i class="fas fa-key me-2"></i>تغيير كلمة المرور
                                <small class="text-muted">(اختياري)</small>
                            </h6>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <!-- كلمة المرور الجديدة -->
                                <div class="col-md-6 mb-3">
                                    <label for="password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>كلمة المرور الجديدة
                                    </label>
                                    <div class="input-group">
                                        <input type="password" class="form-control" id="password"
                                               name="password" minlength="6">
                                        <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                        </button>
                                    </div>
                                    <small class="form-text text-muted">
                                        اتركه فارغاً إذا كنت لا تريد تغيير كلمة المرور
                                    </small>
                                </div>

                                <!-- تأكيد كلمة المرور -->
                                <div class="col-md-6 mb-3">
                                    <label for="confirm_password" class="form-label">
                                        <i class="fas fa-lock me-1"></i>تأكيد كلمة المرور
                                    </label>
                                    <input type="password" class="form-control" id="confirm_password"
                                           name="confirm_password">
                                    <div class="invalid-feedback">
                                        كلمات المرور غير متطابقة
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- معلومات إضافية -->
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>معلومات المستخدم:</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <small><strong>تاريخ الإنشاء:</strong>
                                    {{ user.created_at.strftime('%Y-%m-%d %H:%M') if user.created_at else 'غير محدد' }}
                                </small>
                            </div>
                            <div class="col-md-6">
                                <small><strong>آخر دخول:</strong>
                                    غير متوفر حالياً
                                </small>
                            </div>
                        </div>
                    </div>

                    <!-- تحذير للمستخدم الحالي -->
                    {% if user.id == current_user.id %}
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>تنبيه:</strong> أنت تقوم بتعديل حسابك الخاص. كن حذراً عند تغيير الدور أو كلمة المرور.
                    </div>
                    {% endif %}

                    <!-- أزرار الإجراءات -->
                    <div class="d-flex justify-content-between">
                        <button type="button" class="btn btn-outline-secondary" onclick="resetForm()">
                            <i class="fas fa-undo me-2"></i>إعادة تعيين
                        </button>
                        <div>
                            <a href="{{ url_for('users') }}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-2"></i>إلغاء
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>حفظ التغييرات
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// إظهار/إخفاء كلمة المرور
function togglePassword() {
    const passwordField = document.getElementById('password');
    const toggleIcon = document.getElementById('passwordToggleIcon');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

// التحقق من تطابق كلمات المرور
function validatePasswordMatch() {
    const password = document.getElementById('password').value;
    const confirmPassword = document.getElementById('confirm_password').value;
    const confirmField = document.getElementById('confirm_password');

    // إذا لم يتم إدخال كلمة مرور، لا نحتاج للتحقق
    if (!password && !confirmPassword) {
        confirmField.setCustomValidity('');
        confirmField.classList.remove('is-invalid');
        return true;
    }

    if (password !== confirmPassword) {
        confirmField.setCustomValidity('كلمات المرور غير متطابقة');
        confirmField.classList.add('is-invalid');
        return false;
    } else {
        confirmField.setCustomValidity('');
        confirmField.classList.remove('is-invalid');
        return true;
    }
}

// إعادة تعيين النموذج
function resetForm() {
    if (confirm('هل أنت متأكد من إعادة تعيين النموذج؟')) {
        location.reload();
    }
}

// إضافة مستمعي الأحداث
document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('form');
    const passwordField = document.getElementById('password');
    const confirmPasswordField = document.getElementById('confirm_password');

    // التحقق من تطابق كلمات المرور عند الكتابة
    confirmPasswordField.addEventListener('input', validatePasswordMatch);
    passwordField.addEventListener('input', function() {
        if (confirmPasswordField.value) {
            validatePasswordMatch();
        }
    });

    // التحقق من النموذج قبل الإرسال
    form.addEventListener('submit', function(e) {
        if (!form.checkValidity() || !validatePasswordMatch()) {
            e.preventDefault();
            e.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // التحقق من اسم المستخدم (بدون مسافات)
    document.getElementById('username').addEventListener('input', function(e) {
        e.target.value = e.target.value.replace(/\s/g, '');
    });

    // تحذير عند تغيير الدور للمستخدم الحالي
    {% if user.id == current_user.id %}
    document.getElementById('role').addEventListener('change', function() {
        if (this.value !== '{{ user.role }}') {
            alert('تنبيه: أنت تقوم بتغيير دورك الخاص. تأكد من أن هذا ما تريده.');
        }
    });
    {% endif %}
});
</script>
{% endblock %}
