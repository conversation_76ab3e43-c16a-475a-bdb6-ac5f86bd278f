<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>فاتورة رقم {{ invoice.invoice_number }}</title>
    
    <!-- Bootstrap CSS (RTL) -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Google Fonts Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .invoice-header {
            border-bottom: 3px solid #0d6efd;
            margin-bottom: 30px;
            padding-bottom: 20px;
        }
        
        .company-info {
            text-align: center;
        }
        
        .company-name {
            font-size: 28px;
            font-weight: 700;
            color: #0d6efd;
            margin-bottom: 10px;
        }
        
        .invoice-title {
            background: #0d6efd;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 18px;
            font-weight: 600;
            text-align: center;
            margin: 20px 0;
        }
        
        .invoice-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        
        .table th {
            background: #e9ecef;
            font-weight: 600;
            border: 1px solid #dee2e6;
        }
        
        .table td {
            border: 1px solid #dee2e6;
        }
        
        .total-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
            border: 2px solid #0d6efd;
        }
        
        .final-total {
            font-size: 20px;
            font-weight: 700;
            color: #0d6efd;
        }
        
        .footer-note {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 12px;
        }
        
        @media print {
            .no-print {
                display: none !important;
            }
            
            body {
                margin: 0;
                padding: 15px;
            }
            
            .container {
                max-width: none;
                margin: 0;
                padding: 0;
            }
        }
        
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        
        .payment-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            border-left: 4px solid #2196f3;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- أزرار الطباعة -->
        <div class="no-print mb-3">
            <div class="d-flex justify-content-between">
                <button onclick="window.print()" class="btn btn-primary">
                    <i class="fas fa-print me-2"></i>طباعة
                </button>
                <button onclick="window.close()" class="btn btn-secondary">
                    <i class="fas fa-times me-2"></i>إغلاق
                </button>
            </div>
        </div>

        <!-- رأس الفاتورة -->
        <div class="invoice-header">
            <div class="row">
                <div class="col-md-6">
                    <div class="company-info">
                        <div class="company-name">شركة المبيعات المتقدمة</div>
                        <div class="text-muted">
                            <div>الرياض، المملكة العربية السعودية</div>
                            <div>هاتف: +966 11 123 4567</div>
                            <div>البريد الإلكتروني: <EMAIL></div>
                            <div>الرقم الضريبي: 123456789012345</div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="text-end">
                        <div class="invoice-title">فاتورة مبيعات</div>
                        <div class="invoice-details">
                            <div class="row mb-2">
                                <div class="col-6"><strong>رقم الفاتورة:</strong></div>
                                <div class="col-6">{{ invoice.invoice_number }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-6"><strong>التاريخ:</strong></div>
                                <div class="col-6">{{ invoice.created_at.strftime('%Y-%m-%d') }}</div>
                            </div>
                            <div class="row mb-2">
                                <div class="col-6"><strong>الوقت:</strong></div>
                                <div class="col-6">{{ invoice.created_at.strftime('%H:%M') }}</div>
                            </div>
                            <div class="row">
                                <div class="col-6"><strong>البائع:</strong></div>
                                <div class="col-6">{{ invoice.user.username }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- معلومات العميل -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="payment-info">
                    <h6 class="mb-3"><i class="fas fa-user me-2"></i>بيانات العميل</h6>
                    {% if invoice.customer %}
                    <div><strong>الاسم:</strong> {{ invoice.customer.name }}</div>
                    {% if invoice.customer.phone %}
                    <div><strong>الهاتف:</strong> {{ invoice.customer.phone }}</div>
                    {% endif %}
                    {% if invoice.customer.email %}
                    <div><strong>البريد:</strong> {{ invoice.customer.email }}</div>
                    {% endif %}
                    {% if invoice.customer.address %}
                    <div><strong>العنوان:</strong> {{ invoice.customer.address }}</div>
                    {% endif %}
                    {% else %}
                    <div class="text-center text-muted">
                        <i class="fas fa-money-bill fa-2x mb-2"></i>
                        <div>عميل نقدي</div>
                    </div>
                    {% endif %}
                </div>
            </div>
            <div class="col-md-6">
                <div class="payment-info">
                    <h6 class="mb-3"><i class="fas fa-credit-card me-2"></i>معلومات الدفع</h6>
                    <div><strong>طريقة الدفع:</strong> 
                        {% if invoice.payment_method == 'cash' %}نقدي
                        {% elif invoice.payment_method == 'card' %}بطاقة
                        {% elif invoice.payment_method == 'credit' %}آجل
                        {% endif %}
                    </div>
                    <div><strong>حالة الفاتورة:</strong> 
                        {% if invoice.status == 'completed' %}مكتملة
                        {% elif invoice.status == 'pending' %}معلقة
                        {% elif invoice.status == 'cancelled' %}ملغية
                        {% endif %}
                    </div>
                    {% if invoice.notes %}
                    <div><strong>ملاحظات:</strong> {{ invoice.notes }}</div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- عناصر الفاتورة -->
        <div class="table-responsive mb-4">
            <table class="table table-bordered">
                <thead>
                    <tr>
                        <th style="width: 5%;">#</th>
                        <th style="width: 40%;">اسم المنتج</th>
                        <th style="width: 15%;">السعر</th>
                        <th style="width: 10%;">الكمية</th>
                        <th style="width: 10%;">الوحدة</th>
                        <th style="width: 20%;">المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    {% for item in invoice.items %}
                    <tr>
                        <td class="text-center">{{ loop.index }}</td>
                        <td>
                            <strong>{{ item.product.name }}</strong>
                            {% if item.product.barcode %}
                            <br><small class="text-muted">الباركود: {{ item.product.barcode }}</small>
                            {% endif %}
                        </td>
                        <td class="text-center">{{ "%.2f"|format(item.unit_price) }} ر.س</td>
                        <td class="text-center">{{ item.quantity }}</td>
                        <td class="text-center">{{ item.product.unit }}</td>
                        <td class="text-center fw-bold">{{ "%.2f"|format(item.total_price) }} ر.س</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- المجاميع -->
        <div class="row">
            <div class="col-md-6">
                <!-- QR Code (للمستقبل) -->
                <div class="qr-code">
                    <div class="border p-3 d-inline-block">
                        <div class="text-muted">
                            <i class="fas fa-qrcode fa-3x"></i>
                            <div class="mt-2">رمز QR</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="total-section">
                    <div class="row mb-2">
                        <div class="col-6"><strong>المجموع الفرعي:</strong></div>
                        <div class="col-6 text-end">{{ "%.2f"|format(invoice.total_amount) }} ر.س</div>
                    </div>
                    {% if invoice.discount > 0 %}
                    <div class="row mb-2">
                        <div class="col-6"><strong>الخصم:</strong></div>
                        <div class="col-6 text-end text-danger">- {{ "%.2f"|format(invoice.discount) }} ر.س</div>
                    </div>
                    {% endif %}
                    {% if invoice.tax > 0 %}
                    <div class="row mb-2">
                        <div class="col-6"><strong>الضريبة (15%):</strong></div>
                        <div class="col-6 text-end">{{ "%.2f"|format(invoice.tax) }} ر.س</div>
                    </div>
                    {% endif %}
                    <hr>
                    <div class="row">
                        <div class="col-6"><strong class="final-total">المجموع النهائي:</strong></div>
                        <div class="col-6 text-end final-total">{{ "%.2f"|format(invoice.final_amount) }} ر.س</div>
                    </div>
                    <div class="row mt-2">
                        <div class="col-6"><strong>المبلغ المدفوع:</strong></div>
                        <div class="col-6 text-end text-success">{{ "%.2f"|format(invoice.paid_amount) }} ر.س</div>
                    </div>
                    {% if invoice.final_amount - invoice.paid_amount != 0 %}
                    <div class="row">
                        <div class="col-6"><strong>المتبقي:</strong></div>
                        <div class="col-6 text-end text-warning">{{ "%.2f"|format(invoice.final_amount - invoice.paid_amount) }} ر.س</div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- تذييل الفاتورة -->
        <div class="footer-note">
            <div class="row">
                <div class="col-md-4">
                    <strong>شروط وأحكام:</strong>
                    <ul class="list-unstyled mt-2" style="font-size: 11px;">
                        <li>• البضاعة المباعة لا ترد ولا تستبدل إلا بعذر مقبول</li>
                        <li>• يحق للشركة استرداد البضاعة في حالة عدم السداد</li>
                        <li>• جميع المنتجات مضمونة حسب ضمان الشركة المصنعة</li>
                    </ul>
                </div>
                <div class="col-md-4 text-center">
                    <div class="mt-4">
                        <div style="border-top: 1px solid #000; width: 150px; margin: 0 auto;"></div>
                        <div class="mt-2">توقيع العميل</div>
                    </div>
                </div>
                <div class="col-md-4 text-center">
                    <div class="mt-4">
                        <div style="border-top: 1px solid #000; width: 150px; margin: 0 auto;"></div>
                        <div class="mt-2">توقيع البائع</div>
                    </div>
                </div>
            </div>
            <div class="text-center mt-4">
                <strong>شكراً لتعاملكم معنا</strong>
                <br>
                تم إنشاء هذه الفاتورة بواسطة نظام إدارة المبيعات والمخازن
                <br>
                {{ invoice.created_at.strftime('%Y-%m-%d %H:%M:%S') }}
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // طباعة تلقائية عند التحميل (اختياري)
        // window.onload = function() { window.print(); }
        
        // إغلاق النافذة بعد الطباعة
        window.onafterprint = function() {
            // window.close();
        }
    </script>
</body>
</html>
