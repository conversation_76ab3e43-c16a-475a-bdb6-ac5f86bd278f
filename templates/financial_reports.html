{% extends "base.html" %}

{% block title %}التقارير المالية{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-chart-pie"></i> التقارير المالية
        </h1>
        <div class="btn-group">
            <button type="button" class="btn btn-primary btn-sm" onclick="exportReport('pdf')">
                <i class="fas fa-file-pdf"></i> تصدير PDF
            </button>
            <button type="button" class="btn btn-success btn-sm" onclick="exportReport('excel')">
                <i class="fas fa-file-excel"></i> تصدير Excel
            </button>
        </div>
    </div>

    <!-- فترة التقرير -->
    <div class="card shadow mb-4">
        <div class="card-header py-3">
            <h6 class="m-0 font-weight-bold text-primary">فترة التقرير</h6>
        </div>
        <div class="card-body">
            <form id="reportPeriodForm" class="row">
                <div class="col-md-4 mb-3">
                    <label for="start_date" class="form-label">من تاريخ</label>
                    <input type="date" class="form-control" id="start_date" name="start_date">
                </div>
                <div class="col-md-4 mb-3">
                    <label for="end_date" class="form-label">إلى تاريخ</label>
                    <input type="date" class="form-control" id="end_date" name="end_date">
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="button" class="btn btn-primary" onclick="generateReports()">
                            <i class="fas fa-chart-bar"></i> إنشاء التقارير
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="setQuickPeriod('month')">
                            الشهر الحالي
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="setQuickPeriod('year')">
                            السنة الحالية
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- قائمة الدخل -->
    <div class="row mb-4">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">قائمة الدخل</h6>
                </div>
                <div class="card-body">
                    <div id="incomeStatementContent">
                        <div class="table-responsive">
                            <table class="table table-borderless">
                                <tbody>
                                    <tr>
                                        <td><strong>إجمالي المبيعات:</strong></td>
                                        <td class="text-end">
                                            <span id="totalSales" class="text-success fw-bold">
                                                0.000 د.ل <span class="libya-flag"></span>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>تكلفة البضاعة المباعة:</strong></td>
                                        <td class="text-end">
                                            <span id="totalPurchases" class="text-danger fw-bold">
                                                0.000 د.ل <span class="libya-flag"></span>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr class="border-top">
                                        <td><strong>الربح الإجمالي:</strong></td>
                                        <td class="text-end">
                                            <span id="grossProfit" class="text-primary fw-bold">
                                                0.000 د.ل <span class="libya-flag"></span>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr>
                                        <td><strong>المصروفات التشغيلية:</strong></td>
                                        <td class="text-end">
                                            <span class="text-warning fw-bold">
                                                0.000 د.ل <span class="libya-flag"></span>
                                            </span>
                                        </td>
                                    </tr>
                                    <tr class="border-top">
                                        <td><strong>صافي الربح:</strong></td>
                                        <td class="text-end">
                                            <span id="netProfit" class="text-success fw-bold fs-5">
                                                0.000 د.ل <span class="libya-flag"></span>
                                            </span>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- الميزانية العمومية -->
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">الميزانية العمومية</h6>
                </div>
                <div class="card-body">
                    <div id="balanceSheetContent">
                        <div class="row">
                            <div class="col-12 mb-3">
                                <h6 class="text-primary">الأصول</h6>
                                <table class="table table-sm table-borderless">
                                    <tbody>
                                        <tr>
                                            <td>المخزون:</td>
                                            <td class="text-end">
                                                <span id="inventoryValue" class="fw-bold">
                                                    0.000 د.ل <span class="libya-flag"></span>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>ذمم العملاء:</td>
                                            <td class="text-end">
                                                <span id="customersBalance" class="fw-bold">
                                                    0.000 د.ل <span class="libya-flag"></span>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr class="border-top">
                                            <td><strong>إجمالي الأصول:</strong></td>
                                            <td class="text-end">
                                                <span id="totalAssets" class="text-primary fw-bold">
                                                    0.000 د.ل <span class="libya-flag"></span>
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div class="col-12">
                                <h6 class="text-warning">الخصوم وحقوق الملكية</h6>
                                <table class="table table-sm table-borderless">
                                    <tbody>
                                        <tr>
                                            <td>ذمم الموردين:</td>
                                            <td class="text-end">
                                                <span id="suppliersBalance" class="fw-bold">
                                                    0.000 د.ل <span class="libya-flag"></span>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>حقوق الملكية:</td>
                                            <td class="text-end">
                                                <span id="equity" class="fw-bold">
                                                    0.000 د.ل <span class="libya-flag"></span>
                                                </span>
                                            </td>
                                        </tr>
                                        <tr class="border-top">
                                            <td><strong>إجمالي الخصوم وحقوق الملكية:</strong></td>
                                            <td class="text-end">
                                                <span id="totalLiabilitiesEquity" class="text-warning fw-bold">
                                                    0.000 د.ل <span class="libya-flag"></span>
                                                </span>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row">
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">توزيع الإيرادات</h6>
                </div>
                <div class="card-body">
                    <canvas id="revenueChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
        <div class="col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">اتجاه الأرباح</h6>
                </div>
                <div class="card-body">
                    <canvas id="profitTrendChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
$(document).ready(function() {
    // تعيين التواريخ الافتراضية
    setQuickPeriod('month');
    
    // إنشاء التقارير الأولية
    generateReports();
});

function setQuickPeriod(period) {
    var today = new Date();
    var startDate, endDate;
    
    if (period === 'month') {
        startDate = new Date(today.getFullYear(), today.getMonth(), 1);
        endDate = new Date(today.getFullYear(), today.getMonth() + 1, 0);
    } else if (period === 'year') {
        startDate = new Date(today.getFullYear(), 0, 1);
        endDate = new Date(today.getFullYear(), 11, 31);
    }
    
    $('#start_date').val(startDate.toISOString().split('T')[0]);
    $('#end_date').val(endDate.toISOString().split('T')[0]);
}

function generateReports() {
    var startDate = $('#start_date').val();
    var endDate = $('#end_date').val();
    
    if (!startDate || !endDate) {
        alert('يرجى تحديد فترة التقرير');
        return;
    }
    
    // تحميل قائمة الدخل
    loadIncomeStatement(startDate, endDate);
    
    // تحميل الميزانية العمومية
    loadBalanceSheet();
    
    // تحديث الرسوم البيانية
    updateCharts(startDate, endDate);
}

function loadIncomeStatement(startDate, endDate) {
    $.ajax({
        url: '/api/financial/income-statement',
        method: 'GET',
        data: {
            start_date: startDate,
            end_date: endDate
        },
        success: function(data) {
            $('#totalSales').html(data.total_sales.toFixed(3) + ' د.ل <span class="libya-flag"></span>');
            $('#totalPurchases').html(data.total_purchases.toFixed(3) + ' د.ل <span class="libya-flag"></span>');
            $('#grossProfit').html(data.gross_profit.toFixed(3) + ' د.ل <span class="libya-flag"></span>');
            $('#netProfit').html(data.net_profit.toFixed(3) + ' د.ل <span class="libya-flag"></span>');
        },
        error: function() {
            alert('حدث خطأ في تحميل قائمة الدخل');
        }
    });
}

function loadBalanceSheet() {
    $.ajax({
        url: '/api/financial/balance-sheet',
        method: 'GET',
        success: function(data) {
            $('#inventoryValue').html(data.assets.inventory.toFixed(3) + ' د.ل <span class="libya-flag"></span>');
            $('#customersBalance').html(data.assets.customers.toFixed(3) + ' د.ل <span class="libya-flag"></span>');
            $('#totalAssets').html(data.assets.total.toFixed(3) + ' د.ل <span class="libya-flag"></span>');
            $('#suppliersBalance').html(data.liabilities.suppliers.toFixed(3) + ' د.ل <span class="libya-flag"></span>');
            $('#equity').html(data.equity.toFixed(3) + ' د.ل <span class="libya-flag"></span>');
            $('#totalLiabilitiesEquity').html((data.liabilities.total + data.equity).toFixed(3) + ' د.ل <span class="libya-flag"></span>');
        },
        error: function() {
            alert('حدث خطأ في تحميل الميزانية العمومية');
        }
    });
}

function updateCharts(startDate, endDate) {
    // رسم بياني للإيرادات (مبسط)
    var ctx1 = document.getElementById('revenueChart').getContext('2d');
    new Chart(ctx1, {
        type: 'pie',
        data: {
            labels: ['المبيعات', 'الخدمات', 'أخرى'],
            datasets: [{
                data: [70, 20, 10],
                backgroundColor: ['#4e73df', '#1cc88a', '#36b9cc']
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
    
    // رسم بياني لاتجاه الأرباح (مبسط)
    var ctx2 = document.getElementById('profitTrendChart').getContext('2d');
    new Chart(ctx2, {
        type: 'line',
        data: {
            labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
            datasets: [{
                label: 'الأرباح (د.ل)',
                data: [1200, 1900, 3000, 5000, 2000, 3000],
                borderColor: '#1cc88a',
                backgroundColor: 'rgba(28, 200, 138, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function exportReport(format) {
    var startDate = $('#start_date').val();
    var endDate = $('#end_date').val();
    
    if (!startDate || !endDate) {
        alert('يرجى تحديد فترة التقرير أولاً');
        return;
    }
    
    // يمكن إضافة وظيفة التصدير هنا
    alert('سيتم إضافة وظيفة التصدير إلى ' + format.toUpperCase() + ' قريباً');
}
</script>

<style>
.libya-flag {
    display: inline-block;
    width: 16px;
    height: 12px;
    background: linear-gradient(to bottom, #e4002b 33%, #000000 33%, #000000 66%, #009639 66%);
    margin-left: 5px;
    border-radius: 2px;
}

.table td {
    vertical-align: middle;
}

.fs-5 {
    font-size: 1.25rem !important;
}

canvas {
    max-height: 300px;
}
</style>
{% endblock %}
