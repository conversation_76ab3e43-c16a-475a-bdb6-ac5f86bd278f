{% extends "base.html" %}

{% block title %}جرد الأرباح{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس الصفحة -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-chart-line text-success me-2"></i>
                جرد الأرباح
            </h2>
            <p class="text-muted mb-0">تتبع وتحليل الأرباح اليومية والشهرية والسنوية</p>
        </div>
        <div>
            <button class="btn btn-primary" onclick="exportReport()">
                <i class="fas fa-download me-2"></i>تصدير التقرير
            </button>
        </div>
    </div>

    <!-- بطاقات الملخص -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">الربح اليومي</h6>
                            <h4 class="mb-0" id="dailyProfit">
                                <span class="libya-flag me-1"></span>
                                <span class="currency-lyd">0.000</span>
                            </h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-day fa-2x opacity-75"></i>
                        </div>
                    </div>
                    <small class="mt-2 d-block" id="dailyChange">+0% من أمس</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">الربح الشهري</h6>
                            <h4 class="mb-0" id="monthlyProfit">
                                <span class="libya-flag me-1"></span>
                                <span class="currency-lyd">0.000</span>
                            </h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                        </div>
                    </div>
                    <small class="mt-2 d-block" id="monthlyChange">+0% من الشهر الماضي</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">الربح السنوي</h6>
                            <h4 class="mb-0" id="yearlyProfit">
                                <span class="libya-flag me-1"></span>
                                <span class="currency-lyd">0.000</span>
                            </h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar fa-2x opacity-75"></i>
                        </div>
                    </div>
                    <small class="mt-2 d-block" id="yearlyChange">+0% من السنة الماضية</small>
                </div>
            </div>
        </div>
        
        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h6 class="card-title">هامش الربح</h6>
                            <h4 class="mb-0" id="profitMargin">0%</h4>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-percentage fa-2x opacity-75"></i>
                        </div>
                    </div>
                    <small class="mt-2 d-block">متوسط هامش الربح</small>
                </div>
            </div>
        </div>
    </div>

    <!-- أدوات التحكم -->
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-filter me-2"></i>فلترة البيانات
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-4">
                            <label for="periodSelect" class="form-label">الفترة</label>
                            <select class="form-select" id="periodSelect" onchange="updatePeriod()">
                                <option value="daily">يومي</option>
                                <option value="monthly" selected>شهري</option>
                                <option value="yearly">سنوي</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label for="yearSelect" class="form-label">السنة</label>
                            <select class="form-select" id="yearSelect" onchange="updateData()">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </select>
                        </div>
                        <div class="col-md-4" id="monthSelectContainer">
                            <label for="monthSelect" class="form-label">الشهر</label>
                            <select class="form-select" id="monthSelect" onchange="updateData()">
                                <option value="1">يناير</option>
                                <option value="2">فبراير</option>
                                <option value="3">مارس</option>
                                <option value="4">أبريل</option>
                                <option value="5">مايو</option>
                                <option value="6">يونيو</option>
                                <option value="7">يوليو</option>
                                <option value="8">أغسطس</option>
                                <option value="9">سبتمبر</option>
                                <option value="10">أكتوبر</option>
                                <option value="11">نوفمبر</option>
                                <option value="12">ديسمبر</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>إحصائيات سريعة
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <h6 class="text-muted">عدد الفواتير</h6>
                            <h5 id="invoicesCount">0</h5>
                        </div>
                        <div class="col-4">
                            <h6 class="text-muted">المنتجات المباعة</h6>
                            <h5 id="itemsSold">0</h5>
                        </div>
                        <div class="col-4">
                            <h6 class="text-muted">متوسط الربح</h6>
                            <h5 id="averageProfit">
                                <span class="currency-lyd">0.000</span>
                                <span class="libya-flag"></span>
                            </h5>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- الرسوم البيانية -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>اتجاه الأرباح
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="profitTrendChart" height="100"></canvas>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-pie me-2"></i>توزيع الأرباح
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="profitDistributionChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- أكثر المنتجات ربحية -->
    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-trophy me-2"></i>أكثر المنتجات ربحية
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>الترتيب</th>
                                    <th>المنتج</th>
                                    <th>الكمية المباعة</th>
                                    <th>إجمالي المبيعات</th>
                                    <th>إجمالي الربح</th>
                                    <th>هامش الربح</th>
                                </tr>
                            </thead>
                            <tbody id="topProductsTable">
                                <!-- سيتم ملؤها بـ JavaScript -->
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تفاصيل الأرباح -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-list me-2"></i>تفاصيل الأرباح
                    </h5>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewDetailedReport()">
                        <i class="fas fa-external-link-alt me-1"></i>تقرير مفصل
                    </button>
                </div>
                <div class="card-body">
                    <div id="profitDetails">
                        <!-- سيتم ملؤها بـ JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
let profitTrendChart = null;
let profitDistributionChart = null;

// تهيئة الصفحة
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
    loadDashboardData();
});

function initializePage() {
    // ملء قائمة السنوات
    const currentYear = new Date().getFullYear();
    const yearSelect = document.getElementById('yearSelect');
    
    for (let year = currentYear; year >= currentYear - 5; year--) {
        const option = document.createElement('option');
        option.value = year;
        option.textContent = year;
        if (year === currentYear) option.selected = true;
        yearSelect.appendChild(option);
    }
    
    // تعيين الشهر الحالي
    const currentMonth = new Date().getMonth() + 1;
    document.getElementById('monthSelect').value = currentMonth;
}

async function loadDashboardData() {
    try {
        // تحميل البيانات الأساسية
        await Promise.all([
            loadSummaryCards(),
            loadProfitTrends(),
            loadTopProducts(),
            loadProfitDetails()
        ]);
    } catch (error) {
        console.error('خطأ في تحميل البيانات:', error);
    }
}

async function loadSummaryCards() {
    try {
        // الربح اليومي
        const dailyResponse = await fetch('/api/profits/daily');
        const dailyData = await dailyResponse.json();
        
        if (dailyData.success) {
            document.getElementById('dailyProfit').querySelector('.currency-lyd').textContent = 
                formatCurrency(dailyData.data.total_profit);
        }
        
        // الربح الشهري
        const monthlyResponse = await fetch('/api/profits/monthly');
        const monthlyData = await monthlyResponse.json();
        
        if (monthlyData.success) {
            document.getElementById('monthlyProfit').querySelector('.currency-lyd').textContent = 
                formatCurrency(monthlyData.data.total_profit);
            document.getElementById('profitMargin').textContent = 
                monthlyData.data.profit_margin.toFixed(1) + '%';
        }
        
        // الربح السنوي
        const yearlyResponse = await fetch('/api/profits/yearly');
        const yearlyData = await yearlyResponse.json();
        
        if (yearlyData.success) {
            document.getElementById('yearlyProfit').querySelector('.currency-lyd').textContent = 
                formatCurrency(yearlyData.data.total_profit);
            
            // عرض معدل النمو
            if (yearlyData.data.growth_rate !== undefined) {
                const growthElement = document.getElementById('yearlyChange');
                const growth = yearlyData.data.growth_rate;
                growthElement.textContent = `${growth > 0 ? '+' : ''}${growth.toFixed(1)}% من السنة الماضية`;
                growthElement.className = growth >= 0 ? 'text-success' : 'text-danger';
            }
        }
        
    } catch (error) {
        console.error('خطأ في تحميل بطاقات الملخص:', error);
    }
}

function formatCurrency(amount) {
    return new Intl.NumberFormat('ar-LY', {
        minimumFractionDigits: 3,
        maximumFractionDigits: 3
    }).format(amount);
}

function updatePeriod() {
    const period = document.getElementById('periodSelect').value;
    const monthContainer = document.getElementById('monthSelectContainer');
    
    if (period === 'yearly') {
        monthContainer.style.display = 'none';
    } else {
        monthContainer.style.display = 'block';
    }
    
    updateData();
}

function updateData() {
    loadDashboardData();
}

async function loadProfitTrends() {
    try {
        const response = await fetch('/api/profits/trends?days=30');
        const result = await response.json();
        
        if (result.success) {
            updateProfitTrendChart(result.data);
        }
    } catch (error) {
        console.error('خطأ في تحميل اتجاهات الأرباح:', error);
    }
}

function updateProfitTrendChart(data) {
    const ctx = document.getElementById('profitTrendChart').getContext('2d');
    
    if (profitTrendChart) {
        profitTrendChart.destroy();
    }
    
    profitTrendChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: data.map(item => item.date),
            datasets: [{
                label: 'الربح اليومي',
                data: data.map(item => item.profit),
                borderColor: '#28a745',
                backgroundColor: 'rgba(40, 167, 69, 0.1)',
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return formatCurrency(value) + ' د.ل';
                        }
                    }
                }
            }
        }
    });
}

async function loadTopProducts() {
    try {
        const response = await fetch('/api/profits/top-products?limit=10&period=month');
        const result = await response.json();
        
        if (result.success) {
            updateTopProductsTable(result.data);
        }
    } catch (error) {
        console.error('خطأ في تحميل أكثر المنتجات ربحية:', error);
    }
}

function updateTopProductsTable(products) {
    const tbody = document.getElementById('topProductsTable');
    tbody.innerHTML = '';
    
    products.forEach((product, index) => {
        const margin = product.total_sales > 0 ? 
            (product.total_profit / product.total_sales * 100).toFixed(1) : 0;
        
        const row = `
            <tr>
                <td>
                    <span class="badge bg-${index < 3 ? 'warning' : 'secondary'}">${index + 1}</span>
                </td>
                <td>${product.product_name}</td>
                <td>${product.total_quantity}</td>
                <td>
                    <span class="currency-lyd">${formatCurrency(product.total_sales)}</span>
                    <span class="libya-flag"></span>
                </td>
                <td>
                    <span class="currency-lyd text-success">${formatCurrency(product.total_profit)}</span>
                    <span class="libya-flag"></span>
                </td>
                <td>
                    <span class="badge bg-${margin > 20 ? 'success' : margin > 10 ? 'warning' : 'danger'}">
                        ${margin}%
                    </span>
                </td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

async function loadProfitDetails() {
    // تحميل تفاصيل الأرباح حسب الفترة المحددة
    const period = document.getElementById('periodSelect').value;
    // سيتم تنفيذها لاحقاً
}

function viewDetailedReport() {
    const period = document.getElementById('periodSelect').value;
    const year = document.getElementById('yearSelect').value;
    const month = document.getElementById('monthSelect').value;
    
    let url = `/profits/report?type=${period}&year=${year}`;
    if (period !== 'yearly') {
        url += `&month=${month}`;
    }
    
    window.open(url, '_blank');
}

function exportReport() {
    // تصدير التقرير
    alert('سيتم تطوير ميزة التصدير قريباً');
}
</script>
{% endblock %}
