{% extends "base.html" %}

{% block title %}إضافة مورد جديد{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- عنوان الصفحة -->
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">
            <i class="fas fa-plus"></i> إضافة مورد جديد
        </h1>
        <a href="{{ url_for('suppliers') }}" class="btn btn-secondary btn-sm shadow-sm">
            <i class="fas fa-arrow-left fa-sm text-white-50"></i> العودة للموردين
        </a>
    </div>

    <!-- نموذج إضافة المورد -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">بيانات المورد</h6>
                </div>
                <div class="card-body">
                    <form method="POST" id="supplierForm">
                        <div class="row">
                            <!-- الاسم -->
                            <div class="col-md-6 mb-3">
                                <label for="name" class="form-label">اسم المورد <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>

                            <!-- اسم الشركة -->
                            <div class="col-md-6 mb-3">
                                <label for="company_name" class="form-label">اسم الشركة</label>
                                <input type="text" class="form-control" id="company_name" name="company_name">
                            </div>

                            <!-- الهاتف -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="phone" name="phone" 
                                       pattern="^(\+218|0)[0-9]{9}$" placeholder="+218xxxxxxxxx">
                                <div class="form-text">مثال: +218912345678</div>
                            </div>

                            <!-- البريد الإلكتروني -->
                            <div class="col-md-6 mb-3">
                                <label for="email" class="form-label">البريد الإلكتروني</label>
                                <input type="email" class="form-control" id="email" name="email">
                            </div>

                            <!-- العنوان -->
                            <div class="col-12 mb-3">
                                <label for="address" class="form-label">العنوان</label>
                                <textarea class="form-control" id="address" name="address" rows="3"></textarea>
                            </div>

                            <!-- الرقم الضريبي -->
                            <div class="col-md-6 mb-3">
                                <label for="tax_number" class="form-label">الرقم الضريبي</label>
                                <input type="text" class="form-control" id="tax_number" name="tax_number">
                            </div>

                            <!-- الحد الائتماني -->
                            <div class="col-md-6 mb-3">
                                <label for="credit_limit" class="form-label">الحد الائتماني</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="credit_limit" name="credit_limit" 
                                           min="0" step="0.001" value="0">
                                    <div class="input-group-append">
                                        <span class="input-group-text">
                                            د.ل <span class="libya-flag"></span>
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- أزرار الحفظ -->
                        <div class="row">
                            <div class="col-12">
                                <hr>
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save"></i> حفظ المورد
                                </button>
                                <a href="{{ url_for('suppliers') }}" class="btn btn-secondary">
                                    <i class="fas fa-times"></i> إلغاء
                                </a>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- معلومات إضافية -->
        <div class="col-lg-4">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-info">معلومات مهمة</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle"></i> نصائح:</h6>
                        <ul class="mb-0">
                            <li>اسم المورد مطلوب</li>
                            <li>استخدم أرقام هواتف ليبية (+218)</li>
                            <li>الحد الائتماني يحدد أقصى مبلغ مستحق</li>
                            <li>يمكن تعديل البيانات لاحقاً</li>
                        </ul>
                    </div>
                </div>
            </div>

            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-success">الخطوات التالية</h6>
                </div>
                <div class="card-body">
                    <p>بعد إضافة المورد يمكنك:</p>
                    <ul>
                        <li>إنشاء فواتير شراء</li>
                        <li>تسجيل المدفوعات</li>
                        <li>متابعة الحساب</li>
                        <li>عرض التقارير</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // التحقق من صحة النموذج
    $('#supplierForm').on('submit', function(e) {
        var name = $('#name').val().trim();
        if (!name) {
            e.preventDefault();
            alert('يرجى إدخال اسم المورد');
            $('#name').focus();
            return false;
        }
        
        var phone = $('#phone').val().trim();
        if (phone && !phone.match(/^(\+218|0)[0-9]{9}$/)) {
            e.preventDefault();
            alert('يرجى إدخال رقم هاتف ليبي صحيح');
            $('#phone').focus();
            return false;
        }
    });

    // تنسيق رقم الهاتف
    $('#phone').on('input', function() {
        var value = $(this).val().replace(/\D/g, '');
        if (value.startsWith('218')) {
            $(this).val('+' + value);
        } else if (value.startsWith('0')) {
            $(this).val(value);
        }
    });
});
</script>

<style>
.libya-flag {
    display: inline-block;
    width: 16px;
    height: 12px;
    background: linear-gradient(to bottom, #e4002b 33%, #000000 33%, #000000 66%, #009639 66%);
    margin-left: 5px;
    border-radius: 2px;
}

.form-label {
    font-weight: 600;
    color: #5a5c69;
}

.card-header h6 {
    color: #5a5c69;
}

.alert ul {
    padding-left: 20px;
}
</style>
{% endblock %}
