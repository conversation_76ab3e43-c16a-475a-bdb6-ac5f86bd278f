{% extends "base.html" %}

{% block title %}تقرير الأرباح التفصيلي{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- رأس التقرير -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-0">
                <i class="fas fa-file-alt text-primary me-2"></i>
                تقرير الأرباح التفصيلي
            </h2>
            <p class="text-muted mb-0">
                {% if report_type == 'daily' %}
                    تقرير يومي - {{ data.date.strftime('%Y-%m-%d') }}
                {% elif report_type == 'monthly' %}
                    تقرير شهري - {{ data.month_name }} {{ data.year }}
                {% elif report_type == 'yearly' %}
                    تقرير سنوي - {{ data.year }}
                {% endif %}
            </p>
        </div>
        <div>
            <button class="btn btn-success" onclick="window.print()">
                <i class="fas fa-print me-2"></i>طباعة
            </button>
            <button class="btn btn-primary" onclick="exportToPDF()">
                <i class="fas fa-file-pdf me-2"></i>تصدير PDF
            </button>
        </div>
    </div>

    <!-- ملخص الأرباح -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="card bg-success text-white">
                <div class="card-body text-center">
                    <h6 class="card-title">إجمالي الربح</h6>
                    <h3 class="mb-0">
                        <span class="libya-flag me-2"></span>
                        <span class="currency-lyd">{{ "%.3f"|format(data.total_profit) }}</span>
                    </h3>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-info text-white">
                <div class="card-body text-center">
                    <h6 class="card-title">إجمالي المبيعات</h6>
                    <h3 class="mb-0">
                        <span class="libya-flag me-2"></span>
                        <span class="currency-lyd">{{ "%.3f"|format(data.total_sales) }}</span>
                    </h3>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-warning text-white">
                <div class="card-body text-center">
                    <h6 class="card-title">إجمالي التكلفة</h6>
                    <h3 class="mb-0">
                        <span class="libya-flag me-2"></span>
                        <span class="currency-lyd">{{ "%.3f"|format(data.total_cost) }}</span>
                    </h3>
                </div>
            </div>
        </div>

        <div class="col-md-3">
            <div class="card bg-primary text-white">
                <div class="card-body text-center">
                    <h6 class="card-title">هامش الربح</h6>
                    <h3 class="mb-0">{{ "%.1f"|format(data.profit_margin) }}%</h3>
                </div>
            </div>
        </div>
    </div>

    <!-- إحصائيات إضافية -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h6 class="text-muted">عدد الفواتير</h6>
                    <h4 class="text-primary">{{ data.invoices_count }}</h4>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h6 class="text-muted">المنتجات المباعة</h6>
                    <h4 class="text-success">{{ data.items_sold }}</h4>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-body text-center">
                    <h6 class="text-muted">متوسط الربح</h6>
                    <h4 class="text-info">
                        {% if report_type == 'daily' %}
                            <span class="currency-lyd">{{ "%.3f"|format(data.total_profit) }}</span>
                            <span class="libya-flag"></span>
                        {% elif report_type == 'monthly' %}
                            <span class="currency-lyd">{{ "%.3f"|format(data.average_daily_profit) }}</span>
                            <span class="libya-flag"></span>
                            <small class="d-block">يومياً</small>
                        {% elif report_type == 'yearly' %}
                            <span class="currency-lyd">{{ "%.3f"|format(data.average_monthly_profit) }}</span>
                            <span class="libya-flag"></span>
                            <small class="d-block">شهرياً</small>
                        {% endif %}
                    </h4>
                </div>
            </div>
        </div>
    </div>

    {% if report_type == 'yearly' %}
    <!-- تقرير سنوي - الأرباح الشهرية -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-chart-bar me-2"></i>الأرباح الشهرية
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>الشهر</th>
                            <th>إجمالي الربح</th>
                            <th>إجمالي المبيعات</th>
                            <th>هامش الربح</th>
                            <th>عدد الفواتير</th>
                            <th>المنتجات المباعة</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for month_num, month_data in data.monthly_profits.items() %}
                        <tr>
                            <td>{{ month_data.month_name }}</td>
                            <td>
                                <span class="currency-lyd text-success">{{ "%.3f"|format(month_data.total_profit) }}</span>
                                <span class="libya-flag"></span>
                            </td>
                            <td>
                                <span class="currency-lyd">{{ "%.3f"|format(month_data.total_sales) }}</span>
                                <span class="libya-flag"></span>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if month_data.profit_margin > 20 else 'warning' if month_data.profit_margin > 10 else 'danger' }}">
                                    {{ "%.1f"|format(month_data.profit_margin) }}%
                                </span>
                            </td>
                            <td>{{ month_data.invoices_count }}</td>
                            <td>{{ month_data.items_sold }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- أفضل وأسوأ الشهور -->
    {% if data.best_month and data.worst_month %}
    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-trophy me-2"></i>أفضل شهر
                    </h6>
                </div>
                <div class="card-body">
                    <h5>{{ data.best_month.month_name }}</h5>
                    <p class="mb-1">
                        <strong>الربح:</strong>
                        <span class="currency-lyd text-success">{{ "%.3f"|format(data.best_month.total_profit) }}</span>
                        <span class="libya-flag"></span>
                    </p>
                    <p class="mb-0">
                        <strong>هامش الربح:</strong> {{ "%.1f"|format(data.best_month.profit_margin) }}%
                    </p>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card border-danger">
                <div class="card-header bg-danger text-white">
                    <h6 class="card-title mb-0">
                        <i class="fas fa-chart-line-down me-2"></i>أضعف شهر
                    </h6>
                </div>
                <div class="card-body">
                    <h5>{{ data.worst_month.month_name }}</h5>
                    <p class="mb-1">
                        <strong>الربح:</strong>
                        <span class="currency-lyd text-danger">{{ "%.3f"|format(data.worst_month.total_profit) }}</span>
                        <span class="libya-flag"></span>
                    </p>
                    <p class="mb-0">
                        <strong>هامش الربح:</strong> {{ "%.1f"|format(data.worst_month.profit_margin) }}%
                    </p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}

    {% elif report_type == 'monthly' %}
    <!-- تقرير شهري - الأرباح اليومية -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-calendar-day me-2"></i>الأرباح اليومية
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-sm">
                    <thead class="table-light">
                        <tr>
                            <th>التاريخ</th>
                            <th>الربح</th>
                            <th>المبيعات</th>
                            <th>هامش الربح</th>
                            <th>الفواتير</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for date_str, day_data in data.daily_profits.items() %}
                        {% if day_data.total_sales > 0 %}
                        <tr>
                            <td>{{ date_str }}</td>
                            <td>
                                <span class="currency-lyd text-success">{{ "%.3f"|format(day_data.total_profit) }}</span>
                                <span class="libya-flag"></span>
                            </td>
                            <td>
                                <span class="currency-lyd">{{ "%.3f"|format(day_data.total_sales) }}</span>
                                <span class="libya-flag"></span>
                            </td>
                            <td>
                                <span class="badge bg-{{ 'success' if day_data.profit_margin > 20 else 'warning' if day_data.profit_margin > 10 else 'danger' }}">
                                    {{ "%.1f"|format(day_data.profit_margin) }}%
                                </span>
                            </td>
                            <td>{{ day_data.invoices_count }}</td>
                        </tr>
                        {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    {% elif report_type == 'daily' %}
    <!-- تقرير يومي - تفاصيل الفواتير -->
    {% if data.details %}
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-receipt me-2"></i>تفاصيل الفواتير
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover table-sm">
                    <thead class="table-light">
                        <tr>
                            <th>رقم الفاتورة</th>
                            <th>المنتج</th>
                            <th>الكمية</th>
                            <th>سعر البيع</th>
                            <th>سعر الشراء</th>
                            <th>ربح الوحدة</th>
                            <th>إجمالي الربح</th>
                            <th>الوقت</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for detail in data.details %}
                        <tr>
                            <td>{{ detail.invoice_number }}</td>
                            <td>{{ detail.product_name }}</td>
                            <td>{{ detail.quantity }}</td>
                            <td>
                                <span class="currency-lyd">{{ "%.3f"|format(detail.unit_price) }}</span>
                                <span class="libya-flag"></span>
                            </td>
                            <td>
                                <span class="currency-lyd">{{ "%.3f"|format(detail.purchase_price) }}</span>
                                <span class="libya-flag"></span>
                            </td>
                            <td>
                                <span class="currency-lyd {{ 'text-success' if detail.unit_profit > 0 else 'text-danger' }}">
                                    {{ "%.3f"|format(detail.unit_profit) }}
                                </span>
                                <span class="libya-flag"></span>
                            </td>
                            <td>
                                <span class="currency-lyd {{ 'text-success' if detail.total_profit > 0 else 'text-danger' }}">
                                    {{ "%.3f"|format(detail.total_profit) }}
                                </span>
                                <span class="libya-flag"></span>
                            </td>
                            <td>{{ detail.created_at.strftime('%H:%M') }}</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    {% endif %}
    {% endif %}

    <!-- معلومات التقرير -->
    <div class="card">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6 class="text-muted">معلومات التقرير</h6>
                    <p class="mb-1"><strong>تاريخ الإنشاء:</strong> {{ datetime.now().strftime('%Y-%m-%d %H:%M') }}</p>
                    <p class="mb-1"><strong>العملة:</strong> الدينار الليبي (د.ل)</p>
                    <p class="mb-0"><strong>المستخدم:</strong> {{ current_user.username }}</p>
                </div>
                <div class="col-md-6 text-end">
                    <h6 class="text-muted">ملاحظات</h6>
                    <p class="small text-muted mb-0">
                        • جميع المبالغ بالدينار الليبي<br>
                        • الأرباح محسوبة بناءً على الفرق بين سعر البيع وسعر الشراء<br>
                        • هامش الربح = (الربح ÷ المبيعات) × 100
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function exportToPDF() {
    window.print();
}
</script>

<style>
@media print {
    .btn, .navbar, .sidebar {
        display: none !important;
    }

    .container-fluid {
        margin: 0;
        padding: 0;
    }

    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
    }
}
</style>
{% endblock %}
