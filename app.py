from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_login import LoginManager, login_user, logout_user, login_required, current_user
from models import (db, User, Product, Category, Customer, Invoice, InvoiceItem, StockMovement,
                   Supplier, PurchaseInvoice, PurchaseInvoiceItem, Payment,
                   FinancialAccount, JournalEntry, JournalEntryLine)
from barcode_utils import barcode_generator
from profit_calculator import profit_calculator
from datetime import datetime
from config import config
import os
import urllib.parse

# إنشاء التطبيق
app = Flask(__name__)

# إعداد الترميز للنصوص العربية
app.config['JSON_AS_ASCII'] = False

# تحديد بيئة التشغيل
config_name = os.environ.get('FLASK_ENV', 'production')  # افتراضي للإنتاج
app.config.from_object(config[config_name])
config[config_name].init_app(app)

# تهيئة قاعدة البيانات
db.init_app(app)

# تهيئة نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول لهذه الصفحة'

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# الصفحة الرئيسية
@app.route('/')
@login_required
def index():
    from utils import get_dashboard_data

    # الحصول على بيانات لوحة التحكم
    dashboard_data = get_dashboard_data()

    return render_template('dashboard.html', **dashboard_data)

# تسجيل الدخول
@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']
        user = User.query.filter_by(username=username).first()

        if user and user.check_password(password) and user.is_active:
            login_user(user)
            return redirect(url_for('index'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('login.html')

# تسجيل الخروج
@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('login'))

# إدارة المنتجات
@app.route('/products')
@login_required
def products():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    category_id = request.args.get('category', type=int)

    query = Product.query.filter_by(is_active=True)

    if search:
        # معالجة النص العربي بشكل صحيح
        try:
            # فك ترميز النص إذا كان مرمز
            search = urllib.parse.unquote(search, encoding='utf-8')
        except:
            try:
                search = search.encode('latin1').decode('utf-8')
            except:
                pass
        query = query.filter(Product.name.contains(search))
    if category_id:
        query = query.filter_by(category_id=category_id)

    products = query.paginate(page=page, per_page=20, error_out=False)
    categories = Category.query.all()

    return render_template('products.html', products=products, categories=categories)

# route بديل للبحث بـ POST
@app.route('/products/search', methods=['POST'])
@login_required
def products_search():
    data = request.get_json()
    search = data.get('search', '')
    category_id = data.get('category_id')

    query = Product.query.filter_by(is_active=True)

    if search:
        query = query.filter(Product.name.contains(search))
    if category_id:
        query = query.filter_by(category_id=category_id)

    products = query.limit(20).all()

    return jsonify([{
        'id': p.id,
        'name': p.name,
        'barcode': p.barcode,
        'selling_price': p.selling_price,
        'stock_quantity': p.stock_quantity,
        'unit': p.unit,
        'category': p.category.name if p.category else None
    } for p in products])

@app.route('/products/add', methods=['GET', 'POST'])
@login_required
def add_product():
    if current_user.role not in ['admin', 'warehouse_manager']:
        flash('ليس لديك صلاحية لإضافة منتجات', 'error')
        return redirect(url_for('products'))

    if request.method == 'POST':
        product = Product(
            name=request.form['name'],
            barcode=request.form.get('barcode'),
            category_id=request.form['category_id'],
            purchase_price=float(request.form['purchase_price']),
            selling_price=float(request.form['selling_price']),
            stock_quantity=int(request.form['quantity']),
            min_quantity=int(request.form['min_quantity']),
            unit=request.form['unit'],
            description=request.form.get('description')
        )

        db.session.add(product)
        db.session.commit()

        # تسجيل حركة مخزون
        if product.stock_quantity > 0:
            movement = StockMovement(
                product_id=product.id,
                user_id=current_user.id,
                movement_type='in',
                quantity=product.stock_quantity,
                reference_type='initial',
                notes='رصيد ابتدائي'
            )
            db.session.add(movement)
            db.session.commit()

        flash('تم إضافة المنتج بنجاح', 'success')
        return redirect(url_for('products'))

    categories = Category.query.all()
    return render_template('add_product.html', categories=categories)

# إدارة العملاء
@app.route('/customers')
@login_required
def customers():
    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')

    query = Customer.query
    if search:
        # معالجة النص العربي بشكل صحيح
        try:
            search = urllib.parse.unquote(search)
        except:
            pass
        query = query.filter(Customer.name.contains(search))

    customers = query.paginate(page=page, per_page=20, error_out=False)
    return render_template('customers.html', customers=customers)

@app.route('/customers/add', methods=['GET', 'POST'])
@login_required
def add_customer():
    if current_user.role not in ['admin', 'seller']:
        flash('ليس لديك صلاحية لإضافة عملاء', 'error')
        return redirect(url_for('customers'))

    if request.method == 'POST':
        name = request.form.get('name')
        phone = request.form.get('phone')
        email = request.form.get('email')
        address = request.form.get('address')
        credit_limit = request.form.get('credit_limit', 0)

        # التحقق من وجود البيانات المطلوبة
        if not name:
            flash('اسم العميل مطلوب', 'error')
            return render_template('add_customer.html')

        # التحقق من عدم وجود العميل
        existing_customer = Customer.query.filter(
            (Customer.name == name) | (Customer.phone == phone)
        ).first()

        if existing_customer:
            flash('العميل موجود بالفعل', 'error')
            return render_template('add_customer.html')

        # إنشاء عميل جديد
        customer = Customer(
            name=name,
            phone=phone,
            email=email,
            address=address,
            credit_limit=float(credit_limit) if credit_limit else 0
        )

        db.session.add(customer)
        db.session.commit()

        flash(f'تم إضافة العميل {name} بنجاح', 'success')
        return redirect(url_for('customers'))

    return render_template('add_customer.html')

# مسارات الباركود
@app.route('/api/barcode/generate/<int:product_id>')
@login_required
def generate_product_barcode(product_id):
    """إنشاء باركود للمنتج"""
    product = Product.query.get_or_404(product_id)

    # نوع الباركود من المعاملات
    barcode_type = request.args.get('type', 'code128')

    try:
        # إنشاء الباركود
        barcode_number, filepath, base64_data = barcode_generator.generate_product_barcode(
            product.id, product.name, barcode_type
        )

        if barcode_number and base64_data:
            # تحديث رقم الباركود في قاعدة البيانات
            if not product.barcode:
                product.barcode = barcode_number
                db.session.commit()

            return jsonify({
                'success': True,
                'barcode_number': barcode_number,
                'barcode_image': base64_data,
                'filepath': filepath,
                'product_name': product.name
            })
        else:
            return jsonify({'success': False, 'error': 'فشل في إنشاء الباركود'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/barcode/search/<barcode>')
@login_required
def search_by_barcode(barcode):
    """البحث عن منتج بالباركود"""
    product = Product.query.filter_by(barcode=barcode).first()

    if product:
        return jsonify({
            'success': True,
            'product': {
                'id': product.id,
                'name': product.name,
                'barcode': product.barcode,
                'selling_price': product.selling_price,
                'quantity': product.stock_quantity,
                'category': product.category.name if product.category else None
            }
        })
    else:
        return jsonify({'success': False, 'error': 'لم يتم العثور على المنتج'})

@app.route('/barcode/labels')
@login_required
def barcode_labels():
    """صفحة طباعة ملصقات الباركود"""
    products = Product.query.filter(Product.barcode.isnot(None)).all()
    return render_template('barcode_labels.html', products=products)

@app.route('/api/barcode/labels/generate', methods=['POST'])
@login_required
def generate_barcode_labels():
    """إنشاء ملصقات باركود للطباعة"""
    try:
        data = request.get_json()
        product_ids = data.get('product_ids', [])
        labels_per_row = data.get('labels_per_row', 3)

        if not product_ids:
            return jsonify({'success': False, 'error': 'لم يتم تحديد منتجات'}), 400

        # جلب المنتجات
        products = Product.query.filter(Product.id.in_(product_ids)).all()

        # تحويل إلى قاموس
        products_data = []
        for product in products:
            if product.barcode:
                products_data.append({
                    'id': product.id,
                    'name': product.name,
                    'barcode': product.barcode
                })

        # إنشاء الملصقات
        filepath, base64_data = barcode_generator.create_barcode_labels(
            products_data, labels_per_row
        )

        if base64_data:
            return jsonify({
                'success': True,
                'labels_image': base64_data,
                'filepath': filepath
            })
        else:
            return jsonify({'success': False, 'error': 'فشل في إنشاء الملصقات'}), 500

    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# مسارات جرد الأرباح
@app.route('/profits')
@login_required
def profits_dashboard():
    """لوحة تحكم الأرباح"""
    return render_template('profits_dashboard.html')

@app.route('/api/profits/daily')
@login_required
def api_daily_profits():
    """API الأرباح اليومية"""
    try:
        target_date = request.args.get('date')
        if target_date:
            from datetime import datetime
            target_date = datetime.strptime(target_date, '%Y-%m-%d').date()

        data = profit_calculator.get_daily_profit(target_date)
        return jsonify({'success': True, 'data': data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/profits/monthly')
@login_required
def api_monthly_profits():
    """API الأرباح الشهرية"""
    try:
        year = request.args.get('year', type=int)
        month = request.args.get('month', type=int)

        data = profit_calculator.get_monthly_profit(year, month)
        return jsonify({'success': True, 'data': data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/profits/yearly')
@login_required
def api_yearly_profits():
    """API الأرباح السنوية"""
    try:
        year = request.args.get('year', type=int)

        data = profit_calculator.get_yearly_profit(year)
        return jsonify({'success': True, 'data': data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/profits/trends')
@login_required
def api_profit_trends():
    """API اتجاهات الأرباح"""
    try:
        days = request.args.get('days', 30, type=int)

        data = profit_calculator.get_profit_trends(days)
        return jsonify({'success': True, 'data': data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/api/profits/top-products')
@login_required
def api_top_profitable_products():
    """API أكثر المنتجات ربحية"""
    try:
        limit = request.args.get('limit', 10, type=int)
        period = request.args.get('period', 'month')

        data = profit_calculator.get_top_profitable_products(limit, period)
        return jsonify({'success': True, 'data': data})
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

@app.route('/profits/report')
@login_required
def profit_report():
    """تقرير الأرباح التفصيلي"""
    report_type = request.args.get('type', 'monthly')
    year = request.args.get('year', datetime.now().year, type=int)
    month = request.args.get('month', datetime.now().month, type=int)

    if report_type == 'daily':
        from datetime import date
        target_date = request.args.get('date')
        if target_date:
            target_date = datetime.strptime(target_date, '%Y-%m-%d').date()
        else:
            target_date = date.today()

        data = profit_calculator.get_daily_profit(target_date)
    elif report_type == 'monthly':
        data = profit_calculator.get_monthly_profit(year, month)
    elif report_type == 'yearly':
        data = profit_calculator.get_yearly_profit(year)
    else:
        data = profit_calculator.get_monthly_profit(year, month)

    return render_template('profit_report.html', data=data, report_type=report_type, datetime=datetime)

@app.route('/customers/<int:customer_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_customer(customer_id):
    if current_user.role not in ['admin', 'seller']:
        flash('ليس لديك صلاحية لتعديل العملاء', 'error')
        return redirect(url_for('customers'))

    customer = Customer.query.get_or_404(customer_id)

    if request.method == 'POST':
        customer.name = request.form.get('name')
        customer.phone = request.form.get('phone')
        customer.email = request.form.get('email')
        customer.address = request.form.get('address')
        customer.credit_limit = float(request.form.get('credit_limit', 0))

        db.session.commit()
        flash(f'تم تحديث بيانات العميل {customer.name}', 'success')
        return redirect(url_for('customers'))

    return render_template('edit_customer.html', customer=customer)

@app.route('/customers/<int:customer_id>/delete', methods=['POST'])
@login_required
def delete_customer(customer_id):
    if current_user.role != 'admin':
        return jsonify({'error': 'ليس لديك صلاحية لحذف العملاء'}), 403

    customer = Customer.query.get_or_404(customer_id)

    # التحقق من وجود فواتير للعميل
    invoices_count = Invoice.query.filter_by(customer_id=customer_id).count()
    if invoices_count > 0:
        return jsonify({
            'error': f'لا يمكن حذف العميل لأن لديه {invoices_count} فاتورة'
        }), 400

    customer_name = customer.name
    db.session.delete(customer)
    db.session.commit()

    return jsonify({
        'success': True,
        'message': f'تم حذف العميل {customer_name} بنجاح'
    })

# إدارة المبيعات
@app.route('/sales')
@login_required
def sales():
    page = request.args.get('page', 1, type=int)
    invoices = Invoice.query.filter_by(invoice_type='sale').order_by(Invoice.created_at.desc()).paginate(
        page=page, per_page=20, error_out=False)
    return render_template('sales.html', invoices=invoices)

@app.route('/new_sale')
@app.route('/sales/new')
@login_required
def new_sale():
    if current_user.role not in ['admin', 'seller']:
        flash('ليس لديك صلاحية لإنشاء فواتير', 'error')
        return redirect(url_for('sales'))

    customers = Customer.query.all()
    products = Product.query.filter_by(is_active=True).all()
    return render_template('new_sale.html', customers=customers, products=products)

# API للمنتجات (للبحث السريع)
@app.route('/api/products/search')
@login_required
def search_products():
    query = request.args.get('q', '')

    # معالجة النص العربي بشكل صحيح
    if query:
        try:
            query = urllib.parse.unquote(query)
        except:
            pass

    products = Product.query.filter(
        Product.name.contains(query),
        Product.is_active == True
    ).limit(10).all()

    return jsonify([{
        'id': p.id,
        'name': p.name,
        'barcode': p.barcode,
        'price': p.selling_price,
        'quantity': p.stock_quantity,
        'unit': p.unit
    } for p in products])

# API للعملاء
@app.route('/api/customers', methods=['POST'])
@login_required
def add_customer_api():
    data = request.get_json()

    customer = Customer(
        name=data['name'],
        phone=data.get('phone'),
        email=data.get('email'),
        address=data.get('address'),
        credit_limit=data.get('credit_limit', 0)
    )

    db.session.add(customer)
    db.session.commit()

    return jsonify({'success': True, 'customer_id': customer.id})

@app.route('/api/customers/search')
@login_required
def search_customers():
    query = request.args.get('q', '')

    # معالجة النص العربي بشكل صحيح
    if query:
        try:
            query = urllib.parse.unquote(query)
        except:
            pass

    customers = Customer.query.filter(
        Customer.name.contains(query)
    ).limit(10).all()

    return jsonify([{
        'id': c.id,
        'name': c.name,
        'phone': c.phone,
        'email': c.email,
        'current_balance': c.current_balance
    } for c in customers])

# إنشاء فاتورة جديدة
@app.route('/api/invoices', methods=['POST'])
@login_required
def create_invoice():
    if current_user.role not in ['admin', 'seller']:
        return jsonify({'error': 'ليس لديك صلاحية'}), 403

    data = request.get_json()

    # إنشاء رقم فاتورة
    from utils import generate_invoice_number
    invoice_number = generate_invoice_number()

    invoice = Invoice(
        invoice_number=invoice_number,
        customer_id=data.get('customer_id'),
        user_id=current_user.id,
        invoice_type=data.get('invoice_type', 'sale'),
        payment_method=data.get('payment_method', 'cash'),
        discount=data.get('discount', 0),
        tax=data.get('tax', 0),
        notes=data.get('notes')
    )

    total_amount = 0

    # إضافة عناصر الفاتورة
    for item_data in data.get('items', []):
        product = Product.query.get(item_data['product_id'])
        if not product:
            return jsonify({'error': f'المنتج غير موجود: {item_data["product_id"]}'}), 400

        if product.stock_quantity < item_data['quantity']:
            return jsonify({'error': f'الكمية غير متوفرة للمنتج: {product.name}'}), 400

        item_total = item_data['quantity'] * item_data['unit_price']
        total_amount += item_total

        invoice_item = InvoiceItem(
            product_id=item_data['product_id'],
            quantity=item_data['quantity'],
            unit_price=item_data['unit_price'],
            total_price=item_total
        )
        invoice.items.append(invoice_item)

        # تحديث المخزون
        product.stock_quantity -= item_data['quantity']

        # تسجيل حركة المخزون
        movement = StockMovement(
            product_id=product.id,
            user_id=current_user.id,
            movement_type='out',
            quantity=item_data['quantity'],
            reference_type='invoice',
            reference_id=invoice.id,
            notes=f'بيع - فاتورة رقم {invoice_number}'
        )
        db.session.add(movement)

    invoice.total_amount = total_amount
    invoice.final_amount = total_amount - invoice.discount + invoice.tax
    invoice.paid_amount = data.get('paid_amount', invoice.final_amount)

    db.session.add(invoice)
    db.session.commit()

    return jsonify({
        'success': True,
        'invoice_id': invoice.id,
        'invoice_number': invoice_number
    })

# طباعة الفاتورة
@app.route('/invoices/<int:invoice_id>/print')
@login_required
def print_invoice(invoice_id):
    invoice = Invoice.query.get_or_404(invoice_id)
    return render_template('print_invoice.html', invoice=invoice)

# إدارة التصنيفات
@app.route('/categories')
@login_required
def categories():
    if current_user.role not in ['admin', 'warehouse_manager']:
        flash('ليس لديك صلاحية لعرض التصنيفات', 'error')
        return redirect(url_for('index'))

    categories = Category.query.all()
    return render_template('categories.html', categories=categories)

@app.route('/categories/add', methods=['POST'])
@login_required
def add_category():
    if current_user.role not in ['admin', 'warehouse_manager']:
        return jsonify({'error': 'ليس لديك صلاحية'}), 403

    data = request.get_json()

    category = Category(
        name=data['name'],
        description=data.get('description')
    )

    db.session.add(category)
    db.session.commit()

    return jsonify({'success': True, 'category_id': category.id})

# إدارة المستخدمين
@app.route('/users')
@login_required
def users():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لعرض المستخدمين', 'error')
        return redirect(url_for('index'))

    page = request.args.get('page', 1, type=int)
    search = request.args.get('search', '')
    role_filter = request.args.get('role', '')

    query = User.query

    if search:
        query = query.filter(User.username.contains(search) | User.email.contains(search))

    if role_filter:
        query = query.filter_by(role=role_filter)

    users = query.paginate(page=page, per_page=10, error_out=False)

    return render_template('users.html', users=users)

@app.route('/users/add', methods=['GET', 'POST'])
@login_required
def add_user():
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لإضافة مستخدمين', 'error')
        return redirect(url_for('index'))

    if request.method == 'POST':
        username = request.form.get('username')
        email = request.form.get('email')
        password = request.form.get('password')
        role = request.form.get('role')

        # التحقق من وجود البيانات المطلوبة
        if not username or not email or not password or not role:
            flash('جميع الحقول مطلوبة', 'error')
            return render_template('add_user.html')

        # التحقق من عدم وجود المستخدم
        existing_user = User.query.filter(
            (User.username == username) | (User.email == email)
        ).first()

        if existing_user:
            flash('اسم المستخدم أو البريد الإلكتروني موجود بالفعل', 'error')
            return render_template('add_user.html')

        # إنشاء مستخدم جديد
        user = User(
            username=username,
            email=email,
            role=role
        )
        user.set_password(password)

        db.session.add(user)
        db.session.commit()

        flash(f'تم إضافة المستخدم {username} بنجاح', 'success')
        return redirect(url_for('users'))

    return render_template('add_user.html')

@app.route('/users/<int:user_id>/edit', methods=['GET', 'POST'])
@login_required
def edit_user(user_id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لتعديل المستخدمين', 'error')
        return redirect(url_for('index'))

    user = User.query.get_or_404(user_id)

    if request.method == 'POST':
        user.username = request.form.get('username')
        user.email = request.form.get('email')
        user.role = request.form.get('role')

        # تغيير كلمة المرور إذا تم إدخال واحدة جديدة
        new_password = request.form.get('password')
        if new_password and new_password.strip():
            user.set_password(new_password)

        db.session.commit()
        flash(f'تم تحديث بيانات المستخدم {user.username}', 'success')
        return redirect(url_for('users'))

    return render_template('edit_user.html', user=user)

@app.route('/users/<int:user_id>/delete', methods=['POST'])
@login_required
def delete_user(user_id):
    if current_user.role != 'admin':
        flash('ليس لديك صلاحية لحذف المستخدمين', 'error')
        return redirect(url_for('index'))

    user = User.query.get_or_404(user_id)

    # منع حذف المستخدم الحالي
    if user.id == current_user.id:
        flash('لا يمكنك حذف حسابك الخاص', 'error')
        return redirect(url_for('users'))

    username = user.username
    db.session.delete(user)
    db.session.commit()

    flash(f'تم حذف المستخدم {username}', 'success')
    return redirect(url_for('users'))

# تقارير المبيعات
@app.route('/reports')
@login_required
def reports():
    return render_template('reports.html')

@app.route('/api/reports/sales')
@login_required
def sales_report():
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    query = Invoice.query.filter_by(invoice_type='sale')

    if start_date:
        query = query.filter(Invoice.created_at >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        query = query.filter(Invoice.created_at <= datetime.strptime(end_date, '%Y-%m-%d'))

    invoices = query.all()

    total_sales = sum(inv.final_amount for inv in invoices)
    total_invoices = len(invoices)

    return jsonify({
        'total_sales': total_sales,
        'total_invoices': total_invoices,
        'invoices': [{
            'id': inv.id,
            'invoice_number': inv.invoice_number,
            'customer_name': inv.customer.name if inv.customer else 'عميل نقدي',
            'total_amount': inv.final_amount,
            'created_at': inv.created_at.strftime('%Y-%m-%d %H:%M')
        } for inv in invoices]
    })

# ==================== إدارة الموردين ====================

@app.route('/suppliers')
@login_required
def suppliers():
    """صفحة إدارة الموردين"""
    suppliers = Supplier.query.all()
    return render_template('suppliers.html', suppliers=suppliers)

@app.route('/suppliers/add', methods=['GET', 'POST'])
@login_required
def add_supplier():
    """إضافة مورد جديد"""
    if request.method == 'POST':
        try:
            supplier = Supplier(
                name=request.form['name'],
                company_name=request.form.get('company_name'),
                phone=request.form.get('phone'),
                email=request.form.get('email'),
                address=request.form.get('address'),
                tax_number=request.form.get('tax_number'),
                credit_limit=float(request.form.get('credit_limit', 0))
            )
            db.session.add(supplier)
            db.session.commit()
            flash('تم إضافة المورد بنجاح!', 'success')
            return redirect(url_for('suppliers'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('add_supplier.html')

@app.route('/suppliers/edit/<int:supplier_id>', methods=['GET', 'POST'])
@login_required
def edit_supplier(supplier_id):
    """تعديل مورد"""
    supplier = Supplier.query.get_or_404(supplier_id)

    if request.method == 'POST':
        try:
            supplier.name = request.form['name']
            supplier.company_name = request.form.get('company_name')
            supplier.phone = request.form.get('phone')
            supplier.email = request.form.get('email')
            supplier.address = request.form.get('address')
            supplier.tax_number = request.form.get('tax_number')
            supplier.credit_limit = float(request.form.get('credit_limit', 0))
            supplier.is_active = 'is_active' in request.form

            db.session.commit()
            flash('تم تحديث المورد بنجاح!', 'success')
            return redirect(url_for('suppliers'))
        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    return render_template('edit_supplier.html', supplier=supplier)

# ==================== إدارة المشتريات ====================

@app.route('/purchases')
@login_required
def purchases():
    """صفحة إدارة المشتريات"""
    purchases = PurchaseInvoice.query.order_by(PurchaseInvoice.created_at.desc()).all()
    return render_template('purchases.html', purchases=purchases)

@app.route('/purchases/new', methods=['GET', 'POST'])
@login_required
def new_purchase():
    """إنشاء فاتورة شراء جديدة"""
    if request.method == 'POST':
        try:
            # إنشاء رقم فاتورة تلقائي
            last_invoice = PurchaseInvoice.query.order_by(PurchaseInvoice.id.desc()).first()
            invoice_number = f"PUR-{(last_invoice.id + 1) if last_invoice else 1:06d}"

            # إنشاء الفاتورة
            purchase = PurchaseInvoice(
                invoice_number=invoice_number,
                supplier_id=int(request.form['supplier_id']),
                user_id=current_user.id,
                invoice_date=datetime.strptime(request.form['invoice_date'], '%Y-%m-%d').date(),
                due_date=datetime.strptime(request.form['due_date'], '%Y-%m-%d').date() if request.form.get('due_date') else None,
                notes=request.form.get('notes')
            )

            db.session.add(purchase)
            db.session.flush()  # للحصول على معرف الفاتورة

            # إضافة عناصر الفاتورة
            subtotal = 0
            items_data = request.form.getlist('items')

            for item_data in items_data:
                if item_data:
                    item_info = item_data.split(',')
                    if len(item_info) >= 3:
                        product_id = int(item_info[0])
                        quantity = int(item_info[1])
                        unit_cost = float(item_info[2])
                        total_cost = quantity * unit_cost

                        item = PurchaseInvoiceItem(
                            purchase_invoice_id=purchase.id,
                            product_id=product_id,
                            quantity=quantity,
                            unit_cost=unit_cost,
                            total_cost=total_cost
                        )
                        db.session.add(item)
                        subtotal += total_cost

            # تحديث إجماليات الفاتورة
            tax_amount = float(request.form.get('tax_amount', 0))
            discount = float(request.form.get('discount', 0))
            purchase.subtotal = subtotal
            purchase.tax_amount = tax_amount
            purchase.discount = discount
            purchase.total_amount = subtotal + tax_amount - discount

            db.session.commit()
            flash('تم إنشاء فاتورة الشراء بنجاح!', 'success')
            return redirect(url_for('purchases'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    suppliers = Supplier.query.filter_by(is_active=True).all()
    products = Product.query.filter_by(is_active=True).all()
    return render_template('new_purchase.html', suppliers=suppliers, products=products)

# ==================== حركات المخزون المتقدمة ====================

@app.route('/stock/movements')
@login_required
def stock_movements():
    """صفحة حركات المخزون"""
    movements = StockMovement.query.order_by(StockMovement.created_at.desc()).limit(100).all()
    return render_template('stock_movements.html', movements=movements)

@app.route('/stock/adjustment', methods=['GET', 'POST'])
@login_required
def stock_adjustment():
    """تسوية المخزون"""
    if request.method == 'POST':
        try:
            product_id = int(request.form['product_id'])
            new_quantity = int(request.form['new_quantity'])
            notes = request.form.get('notes', '')

            product = Product.query.get_or_404(product_id)
            old_quantity = product.stock_quantity
            adjustment = new_quantity - old_quantity

            if adjustment != 0:
                # إنشاء حركة مخزون
                movement = StockMovement(
                    product_id=product_id,
                    user_id=current_user.id,
                    movement_type='adjustment',
                    quantity=abs(adjustment),
                    reference_type='adjustment',
                    balance_before=old_quantity,
                    balance_after=new_quantity,
                    notes=notes
                )

                # تحديث كمية المنتج
                product.stock_quantity = new_quantity

                db.session.add(movement)
                db.session.commit()

                flash(f'تم تعديل مخزون {product.name} من {old_quantity} إلى {new_quantity}', 'success')
            else:
                flash('لا يوجد تغيير في الكمية', 'info')

            return redirect(url_for('stock_movements'))

        except Exception as e:
            db.session.rollback()
            flash(f'حدث خطأ: {str(e)}', 'error')

    products = Product.query.filter_by(is_active=True).all()
    return render_template('stock_adjustment.html', products=products)

# ==================== التقارير المالية ====================

@app.route('/financial/reports')
@login_required
def financial_reports():
    """صفحة التقارير المالية"""
    return render_template('financial_reports.html')

@app.route('/api/financial/income-statement')
@login_required
def income_statement():
    """قائمة الدخل"""
    start_date = request.args.get('start_date')
    end_date = request.args.get('end_date')

    # المبيعات
    sales_query = Invoice.query.filter_by(invoice_type='sale')
    if start_date:
        sales_query = sales_query.filter(Invoice.created_at >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        sales_query = sales_query.filter(Invoice.created_at <= datetime.strptime(end_date, '%Y-%m-%d'))

    total_sales = sum(inv.final_amount for inv in sales_query.all())

    # المشتريات
    purchases_query = PurchaseInvoice.query
    if start_date:
        purchases_query = purchases_query.filter(PurchaseInvoice.created_at >= datetime.strptime(start_date, '%Y-%m-%d'))
    if end_date:
        purchases_query = purchases_query.filter(PurchaseInvoice.created_at <= datetime.strptime(end_date, '%Y-%m-%d'))

    total_purchases = sum(pur.total_amount for pur in purchases_query.all())

    # حساب الربح الإجمالي
    gross_profit = total_sales - total_purchases

    return jsonify({
        'currency': 'د.ل',
        'total_sales': total_sales,
        'total_purchases': total_purchases,
        'gross_profit': gross_profit,
        'net_profit': gross_profit  # مبسط - يمكن إضافة المصروفات لاحقاً
    })

@app.route('/api/financial/balance-sheet')
@login_required
def balance_sheet():
    """الميزانية العمومية"""
    # الأصول
    # المخزون (تقدير بناءً على تكلفة الشراء)
    inventory_value = 0
    products = Product.query.filter_by(is_active=True).all()
    for product in products:
        # استخدام متوسط تكلفة الشراء
        avg_cost = product.purchase_price or 0
        inventory_value += product.stock_quantity * avg_cost

    # العملاء (المبالغ المستحقة)
    customers_balance = sum(c.current_balance for c in Customer.query.all() if c.current_balance > 0)

    # الخصوم
    # الموردين (المبالغ المستحقة)
    suppliers_balance = sum(s.current_balance for s in Supplier.query.all() if s.current_balance > 0)

    total_assets = inventory_value + customers_balance
    total_liabilities = suppliers_balance
    equity = total_assets - total_liabilities

    return jsonify({
        'currency': 'د.ل',
        'assets': {
            'inventory': inventory_value,
            'customers': customers_balance,
            'total': total_assets
        },
        'liabilities': {
            'suppliers': suppliers_balance,
            'total': total_liabilities
        },
        'equity': equity
    })

# إنشاء قاعدة البيانات والبيانات الأولية
def create_initial_data():
    with app.app_context():
        db.create_all()

        # إنشاء مستخدم مدير افتراضي
        if not User.query.filter_by(username='admin').first():
            admin = User(
                username='admin',
                email='<EMAIL>',
                role='admin'
            )
            admin.set_password('admin123')
            db.session.add(admin)

        # إنشاء تصنيفات افتراضية
        if not Category.query.first():
            categories = [
                Category(name='إلكترونيات', description='الأجهزة الإلكترونية'),
                Category(name='ملابس', description='الملابس والأزياء'),
                Category(name='طعام ومشروبات', description='المواد الغذائية'),
                Category(name='أدوات منزلية', description='الأدوات المنزلية')
            ]
            for cat in categories:
                db.session.add(cat)

        db.session.commit()

if __name__ == '__main__':
    create_initial_data()
    app.run(debug=True, host='0.0.0.0', port=5000)
