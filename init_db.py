#!/usr/bin/env python3
"""
سكريبت تهيئة قاعدة البيانات والبيانات الأولية
"""

from app import app, db
from models import User, Category, Product, Customer
from datetime import datetime
import sys

def create_database():
    """إنشاء قاعدة البيانات والجداول"""
    print("🔄 إنشاء قاعدة البيانات...")

    with app.app_context():
        # إنشاء الجداول
        db.create_all()
        print("✅ تم إنشاء الجداول بنجاح")

def create_admin_user():
    """إنشاء مستخدم مدير افتراضي"""
    print("🔄 إنشاء مستخدم المدير...")

    with app.app_context():
        # التحقق من وجود مستخدم مدير
        try:
            admin = User.query.filter_by(username='admin').first()
            if admin:
                print("⚠️  مستخدم المدير موجود بالفعل")
                return
        except Exception as e:
            print(f"تحذير: {e}")
            admin = None

        # إنشاء مستخدم مدير جديد
        admin = User(
            username='admin',
            email='<EMAIL>',
            role='admin'
        )
        admin.set_password('admin123')

        db.session.add(admin)
        db.session.commit()

        print("✅ تم إنشاء مستخدم المدير بنجاح")
        print("   اسم المستخدم: admin")
        print("   كلمة المرور: admin123")

def create_sample_users():
    """إنشاء مستخدمين تجريبيين"""
    print("🔄 إنشاء مستخدمين تجريبيين...")

    with app.app_context():
        users_data = [
            {
                'username': 'seller1',
                'email': '<EMAIL>',
                'role': 'seller',
                'password': 'seller123'
            },
            {
                'username': 'warehouse1',
                'email': '<EMAIL>',
                'role': 'warehouse_manager',
                'password': 'warehouse123'
            }
        ]

        for user_data in users_data:
            # التحقق من عدم وجود المستخدم
            try:
                existing_user = User.query.filter_by(username=user_data['username']).first()
                if existing_user:
                    continue
            except Exception:
                existing_user = None

            user = User(
                username=user_data['username'],
                email=user_data['email'],
                role=user_data['role']
            )
            user.set_password(user_data['password'])

            db.session.add(user)

        db.session.commit()
        print("✅ تم إنشاء المستخدمين التجريبيين")

def create_categories():
    """إنشاء تصنيفات افتراضية"""
    print("🔄 إنشاء التصنيفات...")

    with app.app_context():
        categories_data = [
            {
                'name': 'إلكترونيات',
                'description': 'الأجهزة الإلكترونية والكهربائية'
            },
            {
                'name': 'ملابس',
                'description': 'الملابس والأزياء للرجال والنساء والأطفال'
            },
            {
                'name': 'طعام ومشروبات',
                'description': 'المواد الغذائية والمشروبات'
            },
            {
                'name': 'أدوات منزلية',
                'description': 'الأدوات والمعدات المنزلية'
            },
            {
                'name': 'كتب وقرطاسية',
                'description': 'الكتب والأدوات المكتبية'
            },
            {
                'name': 'رياضة وترفيه',
                'description': 'المعدات الرياضية وألعاب الترفيه'
            }
        ]

        for cat_data in categories_data:
            # التحقق من عدم وجود التصنيف
            existing_category = Category.query.filter_by(name=cat_data['name']).first()
            if existing_category:
                continue

            category = Category(
                name=cat_data['name'],
                description=cat_data['description']
            )

            db.session.add(category)

        db.session.commit()
        print("✅ تم إنشاء التصنيفات")

def create_sample_products():
    """إنشاء منتجات تجريبية"""
    print("🔄 إنشاء منتجات تجريبية...")

    with app.app_context():
        # الحصول على التصنيفات
        electronics = Category.query.filter_by(name='إلكترونيات').first()
        clothing = Category.query.filter_by(name='ملابس').first()
        food = Category.query.filter_by(name='طعام ومشروبات').first()
        home = Category.query.filter_by(name='أدوات منزلية').first()

        if not all([electronics, clothing, food, home]):
            print("⚠️  يجب إنشاء التصنيفات أولاً")
            return

        products_data = [
            # إلكترونيات
            {
                'name': 'هاتف ذكي سامسونج',
                'category_id': electronics.id,
                'barcode': '1234567890123',
                'purchase_price': 800.00,
                'selling_price': 1200.00,
                'quantity': 50,
                'min_quantity': 10,
                'unit': 'قطعة',
                'description': 'هاتف ذكي بمواصفات عالية'
            },
            {
                'name': 'لابتوب ديل',
                'category_id': electronics.id,
                'barcode': '1234567890124',
                'purchase_price': 2500.00,
                'selling_price': 3500.00,
                'quantity': 20,
                'min_quantity': 5,
                'unit': 'قطعة',
                'description': 'لابتوب للأعمال والدراسة'
            },
            # ملابس
            {
                'name': 'قميص قطني رجالي',
                'category_id': clothing.id,
                'barcode': '1234567890125',
                'purchase_price': 50.00,
                'selling_price': 85.00,
                'quantity': 100,
                'min_quantity': 20,
                'unit': 'قطعة',
                'description': 'قميص قطني عالي الجودة'
            },
            {
                'name': 'فستان نسائي',
                'category_id': clothing.id,
                'barcode': '1234567890126',
                'purchase_price': 120.00,
                'selling_price': 200.00,
                'quantity': 75,
                'min_quantity': 15,
                'unit': 'قطعة',
                'description': 'فستان أنيق للمناسبات'
            },
            # طعام ومشروبات
            {
                'name': 'أرز بسمتي',
                'category_id': food.id,
                'barcode': '1234567890127',
                'purchase_price': 15.00,
                'selling_price': 25.00,
                'quantity': 200,
                'min_quantity': 50,
                'unit': 'كيلو',
                'description': 'أرز بسمتي فاخر'
            },
            {
                'name': 'زيت زيتون',
                'category_id': food.id,
                'barcode': '1234567890128',
                'purchase_price': 35.00,
                'selling_price': 55.00,
                'quantity': 80,
                'min_quantity': 20,
                'unit': 'لتر',
                'description': 'زيت زيتون بكر ممتاز'
            },
            # أدوات منزلية
            {
                'name': 'مكنسة كهربائية',
                'category_id': home.id,
                'barcode': '1234567890129',
                'purchase_price': 300.00,
                'selling_price': 450.00,
                'quantity': 30,
                'min_quantity': 8,
                'unit': 'قطعة',
                'description': 'مكنسة كهربائية قوية'
            },
            {
                'name': 'طقم أواني طبخ',
                'category_id': home.id,
                'barcode': '1234567890130',
                'purchase_price': 150.00,
                'selling_price': 250.00,
                'quantity': 40,
                'min_quantity': 10,
                'unit': 'طقم',
                'description': 'طقم أواني طبخ من الستانلس ستيل'
            }
        ]

        for prod_data in products_data:
            # التحقق من عدم وجود المنتج
            existing_product = Product.query.filter_by(barcode=prod_data['barcode']).first()
            if existing_product:
                continue

            product = Product(**prod_data)
            db.session.add(product)

        db.session.commit()
        print("✅ تم إنشاء المنتجات التجريبية")

def create_sample_customers():
    """إنشاء عملاء تجريبيين"""
    print("🔄 إنشاء عملاء تجريبيين...")

    with app.app_context():
        customers_data = [
            {
                'name': 'أحمد محمد',
                'phone': '0501234567',
                'email': '<EMAIL>',
                'address': 'الرياض، حي النخيل',
                'credit_limit': 5000.00
            },
            {
                'name': 'فاطمة علي',
                'phone': '0507654321',
                'email': '<EMAIL>',
                'address': 'جدة، حي الصفا',
                'credit_limit': 3000.00
            },
            {
                'name': 'محمد السعد',
                'phone': '0551234567',
                'email': '<EMAIL>',
                'address': 'الدمام، حي الفيصلية',
                'credit_limit': 7000.00
            },
            {
                'name': 'نورا الأحمد',
                'phone': '0567890123',
                'email': '<EMAIL>',
                'address': 'مكة، حي العزيزية',
                'credit_limit': 2000.00
            }
        ]

        for cust_data in customers_data:
            # التحقق من عدم وجود العميل
            existing_customer = Customer.query.filter_by(phone=cust_data['phone']).first()
            if existing_customer:
                continue

            customer = Customer(**cust_data)
            db.session.add(customer)

        db.session.commit()
        print("✅ تم إنشاء العملاء التجريبيين")

def main():
    """الوظيفة الرئيسية"""
    print("🚀 بدء تهيئة قاعدة البيانات...")
    print("=" * 50)

    try:
        # إنشاء قاعدة البيانات
        create_database()

        # إنشاء البيانات الأولية
        create_admin_user()
        create_sample_users()
        create_categories()
        create_sample_products()
        create_sample_customers()

        print("=" * 50)
        print("🎉 تم إنجاز تهيئة قاعدة البيانات بنجاح!")
        print("\n📋 ملخص البيانات المُنشأة:")
        print("   • مستخدم مدير (admin/admin123)")
        print("   • مستخدمين تجريبيين")
        print("   • 6 تصنيفات")
        print("   • 8 منتجات تجريبية")
        print("   • 4 عملاء تجريبيين")
        print("\n🌐 يمكنك الآن تشغيل التطبيق:")
        print("   python app.py")

    except Exception as e:
        print(f"❌ حدث خطأ: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
