#!/usr/bin/env python3
"""
مراجعة نهائية شاملة للتطبيق
"""

import requests
import json
import sqlite3
import os
from datetime import datetime

class FinalAppReview:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "http://127.0.0.1:5000"
        self.issues = []
        self.warnings = []
        self.successes = []
        
    def add_issue(self, category, description, severity="medium"):
        """إضافة مشكلة للقائمة"""
        self.issues.append({
            'category': category,
            'description': description,
            'severity': severity,
            'timestamp': datetime.now()
        })
    
    def add_warning(self, category, description):
        """إضافة تحذير للقائمة"""
        self.warnings.append({
            'category': category,
            'description': description,
            'timestamp': datetime.now()
        })
    
    def add_success(self, category, description):
        """إضافة نجاح للقائمة"""
        self.successes.append({
            'category': category,
            'description': description,
            'timestamp': datetime.now()
        })
    
    def login(self):
        """تسجيل الدخول"""
        try:
            login_data = {'username': 'admin', 'password': 'admin123'}
            response = self.session.post(f"{self.base_url}/login", data=login_data, allow_redirects=False)
            if response.status_code == 302:
                self.add_success("Authentication", "تسجيل الدخول يعمل بنجاح")
                return True
            else:
                self.add_issue("Authentication", f"فشل تسجيل الدخول: كود {response.status_code}", "high")
                return False
        except Exception as e:
            self.add_issue("Authentication", f"خطأ في تسجيل الدخول: {e}", "high")
            return False
    
    def test_database_health(self):
        """فحص صحة قاعدة البيانات"""
        print("🗄️ فحص صحة قاعدة البيانات...")
        
        try:
            # فحص قاعدة البيانات الرئيسية
            db_paths = ['instance/sales_inventory.db', 'sales_inventory.db']
            db_found = False
            
            for db_path in db_paths:
                if os.path.exists(db_path):
                    db_found = True
                    print(f"  ✅ قاعدة البيانات موجودة: {db_path}")
                    
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    # فحص الجداول
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [table[0] for table in cursor.fetchall()]
                    
                    required_tables = ['user', 'category', 'product', 'customer', 'invoice', 'invoice_item', 'stock_movement']
                    missing_tables = []
                    
                    for table in required_tables:
                        if table in tables:
                            # فحص عدد السجلات
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cursor.fetchone()[0]
                            print(f"    📊 جدول {table}: {count} سجل")
                            
                            if count > 0:
                                self.add_success("Database", f"جدول {table} يحتوي على بيانات ({count} سجل)")
                            else:
                                self.add_warning("Database", f"جدول {table} فارغ")
                        else:
                            missing_tables.append(table)
                    
                    if missing_tables:
                        self.add_issue("Database", f"جداول مفقودة: {', '.join(missing_tables)}", "high")
                    else:
                        self.add_success("Database", "جميع الجداول المطلوبة موجودة")
                    
                    conn.close()
                    break
            
            if not db_found:
                self.add_issue("Database", "لم يتم العثور على قاعدة البيانات", "high")
                
        except Exception as e:
            self.add_issue("Database", f"خطأ في فحص قاعدة البيانات: {e}", "high")
    
    def test_core_functionality(self):
        """فحص الوظائف الأساسية"""
        print("🔧 فحص الوظائف الأساسية...")
        
        # فحص الصفحات الرئيسية
        pages = [
            ('/', 'لوحة التحكم'),
            ('/products', 'المنتجات'),
            ('/customers', 'العملاء'),
            ('/sales', 'المبيعات'),
            ('/users', 'المستخدمين'),
            ('/categories', 'التصنيفات'),
            ('/reports', 'التقارير')
        ]
        
        for url, name in pages:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                if response.status_code == 200:
                    print(f"  ✅ {name}")
                    self.add_success("Pages", f"صفحة {name} تعمل بنجاح")
                else:
                    print(f"  ❌ {name} - كود {response.status_code}")
                    self.add_issue("Pages", f"صفحة {name} لا تعمل - كود {response.status_code}", "medium")
            except Exception as e:
                print(f"  ❌ {name} - خطأ: {e}")
                self.add_issue("Pages", f"خطأ في صفحة {name}: {e}", "medium")
    
    def test_api_functionality(self):
        """فحص وظائف API"""
        print("🔌 فحص وظائف API...")
        
        apis = [
            ('/api/products/search?q=test', 'البحث في المنتجات'),
            ('/api/customers/search?q=test', 'البحث في العملاء'),
            ('/api/reports/sales', 'تقرير المبيعات')
        ]
        
        for url, name in apis:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                if response.status_code == 200:
                    data = response.json()
                    print(f"  ✅ {name}")
                    self.add_success("API", f"API {name} يعمل بنجاح")
                else:
                    print(f"  ❌ {name} - كود {response.status_code}")
                    self.add_issue("API", f"API {name} لا يعمل - كود {response.status_code}", "medium")
            except Exception as e:
                print(f"  ❌ {name} - خطأ: {e}")
                self.add_issue("API", f"خطأ في API {name}: {e}", "medium")
    
    def test_user_management(self):
        """فحص إدارة المستخدمين"""
        print("👥 فحص إدارة المستخدمين...")
        
        try:
            # اختبار إضافة مستخدم جديد
            user_data = {
                'username': f'testuser_{datetime.now().strftime("%H%M%S")}',
                'email': f'test_{datetime.now().strftime("%H%M%S")}@example.com',
                'password': 'testpass123',
                'role': 'seller'
            }
            
            response = self.session.post(f"{self.base_url}/users/add", data=user_data)
            
            if response.status_code in [200, 302]:
                print("  ✅ إضافة مستخدم جديد")
                self.add_success("User Management", "إضافة مستخدم جديد يعمل")
            else:
                print(f"  ❌ إضافة مستخدم جديد - كود {response.status_code}")
                self.add_issue("User Management", f"فشل إضافة مستخدم - كود {response.status_code}", "medium")
                
        except Exception as e:
            print(f"  ❌ إضافة مستخدم جديد - خطأ: {e}")
            self.add_issue("User Management", f"خطأ في إضافة مستخدم: {e}", "medium")
    
    def test_static_files(self):
        """فحص الملفات الثابتة"""
        print("📁 فحص الملفات الثابتة...")
        
        static_files = [
            ('/static/css/style.css', 'ملف CSS'),
            ('/static/js/app.js', 'ملف JavaScript'),
            ('/static/manifest.json', 'PWA Manifest'),
            ('/static/sw.js', 'Service Worker'),
            ('/static/images/icon-144x144.png', 'أيقونة PWA')
        ]
        
        for url, name in static_files:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                if response.status_code in [200, 304]:
                    print(f"  ✅ {name}")
                    self.add_success("Static Files", f"{name} متوفر")
                else:
                    print(f"  ❌ {name} - كود {response.status_code}")
                    self.add_warning("Static Files", f"{name} غير متوفر")
            except Exception as e:
                print(f"  ❌ {name} - خطأ: {e}")
                self.add_warning("Static Files", f"خطأ في {name}: {e}")
    
    def test_security(self):
        """فحص الأمان"""
        print("🔒 فحص الأمان...")
        
        # فحص الحماية بدون تسجيل دخول
        test_session = requests.Session()
        
        protected_routes = ['/products', '/customers', '/sales', '/users']
        
        for route in protected_routes:
            try:
                response = test_session.get(f"{self.base_url}{route}", allow_redirects=False)
                if response.status_code == 302:
                    location = response.headers.get('Location', '')
                    if 'login' in location.lower():
                        print(f"  ✅ {route} محمي")
                        self.add_success("Security", f"مسار {route} محمي بشكل صحيح")
                    else:
                        print(f"  ⚠️  {route} محمي لكن إعادة التوجيه غير واضحة")
                        self.add_warning("Security", f"مسار {route} محمي لكن إعادة التوجيه غير واضحة")
                else:
                    print(f"  ❌ {route} قد يكون غير محمي")
                    self.add_issue("Security", f"مسار {route} قد يكون غير محمي", "high")
            except Exception as e:
                self.add_warning("Security", f"خطأ في فحص الحماية لـ {route}: {e}")
    
    def run_comprehensive_review(self):
        """تشغيل المراجعة الشاملة"""
        print("🔍 بدء المراجعة الشاملة للتطبيق")
        print("=" * 60)
        
        # تسجيل الدخول
        if not self.login():
            print("❌ فشل تسجيل الدخول - لا يمكن متابعة المراجعة")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح\n")
        
        # تشغيل جميع الفحوصات
        tests = [
            self.test_database_health,
            self.test_core_functionality,
            self.test_api_functionality,
            self.test_user_management,
            self.test_static_files,
            self.test_security
        ]
        
        for test in tests:
            try:
                test()
                print()
            except Exception as e:
                self.add_issue("Test Error", f"خطأ في تشغيل الفحص: {e}", "high")
                print(f"❌ خطأ في الفحص: {e}\n")
        
        # عرض النتائج
        self.show_final_results()
    
    def show_final_results(self):
        """عرض النتائج النهائية"""
        print("=" * 60)
        print("📊 نتائج المراجعة الشاملة النهائية")
        print("=" * 60)
        
        # عرض النجاحات
        if self.successes:
            print(f"\n✅ النجاحات ({len(self.successes)}):")
            for success in self.successes:
                print(f"  🟢 [{success['category']}] {success['description']}")
        
        # عرض المشاكل
        if self.issues:
            print(f"\n❌ المشاكل المكتشفة ({len(self.issues)}):")
            for issue in self.issues:
                severity_icon = {
                    'high': '🔴',
                    'medium': '🟡',
                    'low': '🟢'
                }.get(issue['severity'], '⚪')
                
                print(f"  {severity_icon} [{issue['category']}] {issue['description']}")
        else:
            print("\n✅ لم يتم اكتشاف أي مشاكل!")
        
        # عرض التحذيرات
        if self.warnings:
            print(f"\n⚠️  التحذيرات ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  🟡 [{warning['category']}] {warning['description']}")
        else:
            print("\n✅ لا توجد تحذيرات!")
        
        # التقييم العام
        high_issues = len([i for i in self.issues if i['severity'] == 'high'])
        medium_issues = len([i for i in self.issues if i['severity'] == 'medium'])
        low_issues = len([i for i in self.issues if i['severity'] == 'low'])
        
        print(f"\n📈 التقييم العام:")
        print(f"  ✅ نجاحات: {len(self.successes)}")
        print(f"  🔴 مشاكل عالية الخطورة: {high_issues}")
        print(f"  🟡 مشاكل متوسطة الخطورة: {medium_issues}")
        print(f"  🟢 مشاكل منخفضة الخطورة: {low_issues}")
        print(f"  ⚠️  تحذيرات: {len(self.warnings)}")
        
        # النتيجة النهائية
        total_issues = len(self.issues)
        total_successes = len(self.successes)
        
        if high_issues == 0 and medium_issues <= 2 and total_successes >= 10:
            print(f"\n🎉 التطبيق في حالة ممتازة!")
            print(f"🏆 جاهز للاستخدام الإنتاجي!")
            return True
        elif high_issues == 0 and medium_issues <= 5:
            print(f"\n✅ التطبيق في حالة جيدة مع بعض التحسينات المطلوبة")
            return True
        else:
            print(f"\n⚠️  التطبيق يحتاج لإصلاحات قبل الاستخدام الإنتاجي")
            return False

def main():
    """الوظيفة الرئيسية"""
    print(f"🕐 وقت المراجعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 عنوان التطبيق: http://127.0.0.1:5000")
    print()
    
    reviewer = FinalAppReview()
    success = reviewer.run_comprehensive_review()
    
    return success

if __name__ == "__main__":
    main()
