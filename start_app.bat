@echo off
chcp 65001 >nul
title تطبيق إدارة المبيعات والمخازن

echo 🚀 بدء تشغيل تطبيق إدارة المبيعات والمخازن...
echo ==================================================

REM الانتقال لمجلد التطبيق
cd /d "%~dp0"

REM تفعيل البيئة الافتراضية
echo 🔧 تفعيل البيئة الافتراضية...
call venv\Scripts\activate.bat

REM التحقق من وجود قاعدة البيانات
if not exist "instance\sales_inventory.db" (
    echo 📊 إنشاء قاعدة البيانات والبيانات التجريبية...
    python init_db.py
    python add_sample_data.py
)

REM تشغيل التطبيق
echo 🌐 تشغيل التطبيق...
echo ==================================================
echo ✅ التطبيق يعمل الآن!
echo 🌐 افتح المتصفح على: http://localhost:5000
echo 👤 المستخدم: admin
echo 🔑 كلمة المرور: admin123
echo ==================================================
echo ⏹️  لإيقاف التطبيق اضغط Ctrl+C
echo.

REM تشغيل التطبيق
python run.py

pause
