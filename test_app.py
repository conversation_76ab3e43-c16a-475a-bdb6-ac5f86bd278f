#!/usr/bin/env python3
"""
اختبار سريع لوظائف التطبيق
"""

import requests
import json
from datetime import datetime

# إعدادات الاختبار
BASE_URL = "http://127.0.0.1:5000"
TEST_USER = {"username": "admin", "password": "admin123"}

class SalesAppTester:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = BASE_URL
        
    def test_connection(self):
        """اختبار الاتصال بالتطبيق"""
        print("🔄 اختبار الاتصال بالتطبيق...")
        try:
            response = self.session.get(self.base_url)
            if response.status_code in [200, 302]:
                print("✅ التطبيق متاح ويعمل")
                return True
            else:
                print(f"❌ خطأ في الاتصال: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ فشل الاتصال: {e}")
            return False
    
    def test_login_page(self):
        """اختبار صفحة تسجيل الدخول"""
        print("🔄 اختبار صفحة تسجيل الدخول...")
        try:
            response = self.session.get(f"{self.base_url}/login")
            if response.status_code == 200:
                print("✅ صفحة تسجيل الدخول تعمل")
                return True
            else:
                print(f"❌ خطأ في صفحة تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ فشل في الوصول لصفحة تسجيل الدخول: {e}")
            return False
    
    def test_login(self):
        """اختبار عملية تسجيل الدخول"""
        print("🔄 اختبار تسجيل الدخول...")
        try:
            # الحصول على صفحة تسجيل الدخول أولاً
            login_page = self.session.get(f"{self.base_url}/login")
            
            # محاولة تسجيل الدخول
            login_data = {
                'username': TEST_USER['username'],
                'password': TEST_USER['password']
            }
            
            response = self.session.post(f"{self.base_url}/login", data=login_data)
            
            if response.status_code == 302:  # إعادة توجيه بعد نجاح تسجيل الدخول
                print("✅ تسجيل الدخول نجح")
                return True
            else:
                print(f"❌ فشل تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_dashboard(self):
        """اختبار لوحة التحكم"""
        print("🔄 اختبار لوحة التحكم...")
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                print("✅ لوحة التحكم تعمل")
                return True
            else:
                print(f"❌ خطأ في لوحة التحكم: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ فشل في الوصول للوحة التحكم: {e}")
            return False
    
    def test_products_page(self):
        """اختبار صفحة المنتجات"""
        print("🔄 اختبار صفحة المنتجات...")
        try:
            response = self.session.get(f"{self.base_url}/products")
            if response.status_code == 200:
                print("✅ صفحة المنتجات تعمل")
                return True
            else:
                print(f"❌ خطأ في صفحة المنتجات: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ فشل في الوصول لصفحة المنتجات: {e}")
            return False
    
    def test_customers_page(self):
        """اختبار صفحة العملاء"""
        print("🔄 اختبار صفحة العملاء...")
        try:
            response = self.session.get(f"{self.base_url}/customers")
            if response.status_code == 200:
                print("✅ صفحة العملاء تعمل")
                return True
            else:
                print(f"❌ خطأ في صفحة العملاء: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ فشل في الوصول لصفحة العملاء: {e}")
            return False
    
    def test_sales_page(self):
        """اختبار صفحة المبيعات"""
        print("🔄 اختبار صفحة المبيعات...")
        try:
            response = self.session.get(f"{self.base_url}/sales")
            if response.status_code == 200:
                print("✅ صفحة المبيعات تعمل")
                return True
            else:
                print(f"❌ خطأ في صفحة المبيعات: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ فشل في الوصول لصفحة المبيعات: {e}")
            return False
    
    def test_api_products_search(self):
        """اختبار API البحث في المنتجات"""
        print("🔄 اختبار API البحث في المنتجات...")
        try:
            response = self.session.get(f"{self.base_url}/api/products/search?q=هاتف")
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API البحث يعمل - وجد {len(data)} منتج")
                return True
            else:
                print(f"❌ خطأ في API البحث: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ فشل في API البحث: {e}")
            return False
    
    def test_static_files(self):
        """اختبار الملفات الثابتة"""
        print("🔄 اختبار الملفات الثابتة...")
        static_files = [
            "/static/css/style.css",
            "/static/js/app.js",
            "/static/manifest.json",
            "/static/sw.js"
        ]
        
        all_good = True
        for file_path in static_files:
            try:
                response = self.session.get(f"{self.base_url}{file_path}")
                if response.status_code == 200:
                    print(f"  ✅ {file_path}")
                else:
                    print(f"  ❌ {file_path} - {response.status_code}")
                    all_good = False
            except Exception as e:
                print(f"  ❌ {file_path} - {e}")
                all_good = False
        
        if all_good:
            print("✅ جميع الملفات الثابتة تعمل")
        else:
            print("⚠️  بعض الملفات الثابتة لا تعمل")
        
        return all_good
    
    def run_all_tests(self):
        """تشغيل جميع الاختبارات"""
        print("🧪 بدء اختبار نظام إدارة المبيعات والمخازن")
        print("=" * 60)
        
        tests = [
            ("الاتصال", self.test_connection),
            ("صفحة تسجيل الدخول", self.test_login_page),
            ("تسجيل الدخول", self.test_login),
            ("لوحة التحكم", self.test_dashboard),
            ("صفحة المنتجات", self.test_products_page),
            ("صفحة العملاء", self.test_customers_page),
            ("صفحة المبيعات", self.test_sales_page),
            ("API البحث", self.test_api_products_search),
            ("الملفات الثابتة", self.test_static_files)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 اختبار: {test_name}")
            if test_func():
                passed += 1
            print("-" * 40)
        
        print("\n" + "=" * 60)
        print(f"📊 نتائج الاختبار:")
        print(f"✅ نجح: {passed}/{total}")
        print(f"❌ فشل: {total - passed}/{total}")
        print(f"📈 معدل النجاح: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 جميع الاختبارات نجحت! التطبيق يعمل بشكل مثالي")
            return True
        else:
            print(f"\n⚠️  {total - passed} اختبار فشل. يرجى مراجعة المشاكل أعلاه")
            return False

def main():
    """الوظيفة الرئيسية"""
    print(f"🕐 وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 عنوان التطبيق: {BASE_URL}")
    print(f"👤 مستخدم الاختبار: {TEST_USER['username']}")
    print()
    
    tester = SalesAppTester()
    success = tester.run_all_tests()
    
    print("\n" + "=" * 60)
    if success:
        print("🏆 التطبيق جاهز للاستخدام!")
    else:
        print("🔧 يحتاج التطبيق لبعض الإصلاحات")
    
    return success

if __name__ == "__main__":
    main()
