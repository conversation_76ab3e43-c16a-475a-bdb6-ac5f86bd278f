"""
وظائف مساعدة للتطبيق
"""
from datetime import datetime, timedelta
from functools import wraps
from flask import abort, current_app
from flask_login import current_user
import re
import os
try:
    import qrcode
    from io import BytesIO
    import base64
    QR_AVAILABLE = True
except ImportError:
    QR_AVAILABLE = False
    qrcode = None
    BytesIO = None
    base64 = None

def permission_required(permission):
    """ديكوريتر للتحقق من الصلاحيات"""
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            if not current_user.is_authenticated:
                abort(401)

            from config import ROLES
            user_permissions = ROLES.get(current_user.role, {}).get('permissions', [])

            if permission not in user_permissions:
                abort(403)

            return f(*args, **kwargs)
        return decorated_function
    return decorator

def generate_invoice_number():
    """توليد رقم فاتورة جديد"""
    from models import Invoice

    today = datetime.now()
    date_prefix = today.strftime('%Y%m%d')

    # البحث عن آخر فاتورة في نفس اليوم
    last_invoice = Invoice.query.filter(
        Invoice.invoice_number.like(f"{current_app.config['INVOICE_PREFIX']}-{date_prefix}-%")
    ).order_by(Invoice.id.desc()).first()

    if last_invoice:
        # استخراج الرقم التسلسلي من آخر فاتورة
        last_number = int(last_invoice.invoice_number.split('-')[-1])
        next_number = last_number + 1
    else:
        next_number = 1

    # تنسيق الرقم
    number_str = str(next_number).zfill(current_app.config['INVOICE_NUMBER_LENGTH'])

    return f"{current_app.config['INVOICE_PREFIX']}-{date_prefix}-{number_str}"

def generate_barcode():
    """توليد باركود عشوائي"""
    timestamp = str(int(datetime.now().timestamp()))
    return timestamp + str(hash(timestamp))[-3:]

def validate_phone(phone):
    """التحقق من صحة رقم الهاتف الليبي"""
    if not phone:
        return True  # رقم الهاتف اختياري

    # نمط للأرقام الليبية
    pattern = r'^(\+218|218|0)?[9][0-9]{8}$'
    return bool(re.match(pattern, phone))

def validate_email(email):
    """التحقق من صحة البريد الإلكتروني"""
    if not email:
        return True  # البريد الإلكتروني اختياري

    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return bool(re.match(pattern, email))

def format_currency(amount):
    """تنسيق المبلغ بالعملة الليبية"""
    return f"{amount:,.3f} د.ل"

def format_date(date, format='%Y-%m-%d'):
    """تنسيق التاريخ"""
    if isinstance(date, str):
        return date
    return date.strftime(format) if date else ''

def format_datetime(datetime_obj, format='%Y-%m-%d %H:%M'):
    """تنسيق التاريخ والوقت"""
    if isinstance(datetime_obj, str):
        return datetime_obj
    return datetime_obj.strftime(format) if datetime_obj else ''

def calculate_tax(amount, tax_rate=None):
    """حساب الضريبة"""
    if tax_rate is None:
        tax_rate = current_app.config['TAX_RATE']
    return amount * tax_rate

def calculate_profit_margin(selling_price, purchase_price):
    """حساب هامش الربح"""
    if purchase_price == 0:
        return 0
    return ((selling_price - purchase_price) / purchase_price) * 100

def get_date_range(period):
    """الحصول على نطاق تاريخي حسب الفترة"""
    today = datetime.now().date()

    if period == 'today':
        return today, today
    elif period == 'yesterday':
        yesterday = today - timedelta(days=1)
        return yesterday, yesterday
    elif period == 'week':
        start_of_week = today - timedelta(days=today.weekday())
        return start_of_week, today
    elif period == 'month':
        start_of_month = today.replace(day=1)
        return start_of_month, today
    elif period == 'year':
        start_of_year = today.replace(month=1, day=1)
        return start_of_year, today
    else:
        return today, today

def generate_qr_code(data, size=10):
    """توليد رمز QR"""
    if not QR_AVAILABLE:
        return None

    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_L,
        box_size=size,
        border=4,
    )
    qr.add_data(data)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")

    # تحويل الصورة إلى base64
    buffer = BytesIO()
    img.save(buffer, format='PNG')
    img_str = base64.b64encode(buffer.getvalue()).decode()

    return f"data:image/png;base64,{img_str}"

def allowed_file(filename, allowed_extensions=None):
    """التحقق من امتداد الملف المسموح"""
    if allowed_extensions is None:
        allowed_extensions = current_app.config['ALLOWED_EXTENSIONS']

    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in allowed_extensions

def sanitize_filename(filename):
    """تنظيف اسم الملف"""
    # إزالة الأحرف غير المسموحة
    filename = re.sub(r'[^\w\s-.]', '', filename)
    # استبدال المسافات بشرطات
    filename = re.sub(r'[-\s]+', '-', filename)
    return filename

def paginate_query(query, page, per_page=20):
    """تقسيم النتائج إلى صفحات"""
    return query.paginate(
        page=page,
        per_page=per_page,
        error_out=False
    )

def get_low_stock_products(threshold=None):
    """الحصول على المنتجات ذات المخزون المنخفض"""
    from models import Product

    query = Product.query.filter(
        Product.is_active == True,
        Product.stock_quantity <= Product.min_quantity
    )

    if threshold:
        query = query.filter(Product.stock_quantity <= threshold)

    return query.all()

def calculate_inventory_value():
    """حساب قيمة المخزون"""
    from models import Product

    products = Product.query.filter_by(is_active=True).all()

    total_purchase_value = sum(p.stock_quantity * p.purchase_price for p in products)
    total_selling_value = sum(p.stock_quantity * p.selling_price for p in products)

    return {
        'purchase_value': total_purchase_value,
        'selling_value': total_selling_value,
        'potential_profit': total_selling_value - total_purchase_value
    }

def get_sales_statistics(start_date=None, end_date=None):
    """الحصول على إحصائيات المبيعات"""
    from models import Invoice

    query = Invoice.query.filter_by(invoice_type='sale', status='completed')

    if start_date:
        query = query.filter(Invoice.created_at >= start_date)
    if end_date:
        query = query.filter(Invoice.created_at <= end_date)

    invoices = query.all()

    total_sales = sum(inv.final_amount for inv in invoices)
    total_invoices = len(invoices)
    average_invoice = total_sales / total_invoices if total_invoices > 0 else 0

    return {
        'total_sales': total_sales,
        'total_invoices': total_invoices,
        'average_invoice': average_invoice,
        'invoices': invoices
    }

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    import shutil
    from datetime import datetime

    db_path = current_app.config['SQLALCHEMY_DATABASE_URI'].replace('sqlite:///', '')
    backup_name = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
    backup_path = os.path.join('backups', backup_name)

    os.makedirs('backups', exist_ok=True)
    shutil.copy2(db_path, backup_path)

    return backup_path

def export_to_excel(data, filename, sheet_name='Sheet1'):
    """تصدير البيانات إلى Excel"""
    try:
        import pandas as pd

        df = pd.DataFrame(data)

        # تنسيق الأعمدة العربية
        if 'created_at' in df.columns:
            df['created_at'] = pd.to_datetime(df['created_at']).dt.strftime('%Y-%m-%d %H:%M')

        # حفظ الملف
        export_path = os.path.join(current_app.config['EXPORT_FOLDER'], filename)
        df.to_excel(export_path, sheet_name=sheet_name, index=False, engine='openpyxl')

        return export_path
    except ImportError:
        raise Exception("مكتبة pandas غير مثبتة")

def export_to_csv(data, filename):
    """تصدير البيانات إلى CSV"""
    try:
        import pandas as pd

        df = pd.DataFrame(data)
        export_path = os.path.join(current_app.config['EXPORT_FOLDER'], filename)
        df.to_csv(export_path, index=False, encoding='utf-8-sig')

        return export_path
    except ImportError:
        raise Exception("مكتبة pandas غير مثبتة")

def log_user_activity(action, details=None):
    """تسجيل نشاط المستخدم"""
    # يمكن إضافة جدول لتسجيل الأنشطة لاحقاً
    current_app.logger.info(
        f"User {current_user.username} performed action: {action}"
        f"{f' - {details}' if details else ''}"
    )

def send_notification(user_id, title, message, notification_type='info'):
    """إرسال إشعار للمستخدم"""
    # يمكن تطوير نظام الإشعارات لاحقاً
    current_app.logger.info(f"Notification for user {user_id}: {title} - {message} ({notification_type})")

def validate_inventory_adjustment(product_id, new_quantity):
    """التحقق من صحة تعديل المخزون"""
    from models import Product

    product = Product.query.get(product_id)
    if not product:
        return False, "المنتج غير موجود"

    if new_quantity < 0:
        return False, "الكمية لا يمكن أن تكون سالبة"

    return True, "صحيح"

def get_dashboard_data():
    """الحصول على بيانات لوحة التحكم"""
    from models import Product, Customer, Invoice

    # إحصائيات أساسية
    total_products = Product.query.filter_by(is_active=True).count()
    total_customers = Customer.query.count()

    # مبيعات اليوم
    today = datetime.now().date()
    today_sales = Invoice.query.filter(
        Invoice.created_at >= today,
        Invoice.invoice_type == 'sale',
        Invoice.status == 'completed'
    ).count()

    # منتجات قليلة المخزون
    low_stock = Product.query.filter(
        Product.stock_quantity <= Product.min_quantity,
        Product.is_active == True
    ).count()

    return {
        'total_products': total_products,
        'total_customers': total_customers,
        'today_sales': today_sales,
        'low_stock_count': low_stock
    }
