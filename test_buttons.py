#!/usr/bin/env python3
"""
اختبار شامل لجميع أزرار وروابط التطبيق
"""

import requests
import json
from datetime import datetime
import time

class ButtonTester:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "http://127.0.0.1:5000"
        self.logged_in = False
        
    def login(self):
        """تسجيل الدخول للحصول على الجلسة"""
        print("🔐 تسجيل الدخول...")
        
        # الحصول على صفحة تسجيل الدخول
        login_page = self.session.get(f"{self.base_url}/login")
        
        # تسجيل الدخول
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = self.session.post(f"{self.base_url}/login", data=login_data, allow_redirects=False)
        
        if response.status_code == 302:
            print("✅ تم تسجيل الدخول بنجاح")
            self.logged_in = True
            return True
        else:
            print("❌ فشل تسجيل الدخول")
            return False
    
    def test_navigation_buttons(self):
        """اختبار أزرار التنقل الرئيسية"""
        print("\n📋 اختبار أزرار التنقل الرئيسية:")
        
        nav_links = [
            ("الرئيسية", "/"),
            ("المنتجات", "/products"),
            ("المبيعات", "/sales"),
            ("العملاء", "/customers"),
            ("التصنيفات", "/categories"),
            ("التقارير", "/reports")
        ]
        
        results = []
        for name, url in nav_links:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                if response.status_code == 200:
                    print(f"  ✅ {name} - يعمل")
                    results.append(True)
                else:
                    print(f"  ❌ {name} - خطأ {response.status_code}")
                    results.append(False)
            except Exception as e:
                print(f"  ❌ {name} - خطأ: {e}")
                results.append(False)
        
        return all(results)
    
    def test_dashboard_buttons(self):
        """اختبار أزرار لوحة التحكم"""
        print("\n📊 اختبار أزرار لوحة التحكم:")
        
        # الحصول على لوحة التحكم
        response = self.session.get(f"{self.base_url}/")
        if response.status_code != 200:
            print("  ❌ لا يمكن الوصول للوحة التحكم")
            return False
        
        # اختبار الروابط السريعة
        quick_actions = [
            ("فاتورة جديدة", "/sales/new"),
            ("إضافة منتج", "/products/add"),
            ("إضافة عميل", "/customers"),
        ]
        
        results = []
        for name, url in quick_actions:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                if response.status_code == 200:
                    print(f"  ✅ {name} - يعمل")
                    results.append(True)
                else:
                    print(f"  ❌ {name} - خطأ {response.status_code}")
                    results.append(False)
            except Exception as e:
                print(f"  ❌ {name} - خطأ: {e}")
                results.append(False)
        
        return all(results)
    
    def test_products_buttons(self):
        """اختبار أزرار صفحة المنتجات"""
        print("\n📦 اختبار أزرار صفحة المنتجات:")
        
        # الوصول لصفحة المنتجات
        response = self.session.get(f"{self.base_url}/products")
        if response.status_code != 200:
            print("  ❌ لا يمكن الوصول لصفحة المنتجات")
            return False
        
        # اختبار صفحة إضافة منتج
        try:
            response = self.session.get(f"{self.base_url}/products/add")
            if response.status_code == 200:
                print("  ✅ زر إضافة منتج جديد - يعمل")
                add_product_works = True
            else:
                print(f"  ❌ زر إضافة منتج جديد - خطأ {response.status_code}")
                add_product_works = False
        except Exception as e:
            print(f"  ❌ زر إضافة منتج جديد - خطأ: {e}")
            add_product_works = False
        
        # اختبار فلترة المنتجات
        try:
            response = self.session.get(f"{self.base_url}/products?search=هاتف")
            if response.status_code == 200:
                print("  ✅ فلتر البحث - يعمل")
                search_works = True
            else:
                print(f"  ❌ فلتر البحث - خطأ {response.status_code}")
                search_works = False
        except Exception as e:
            print(f"  ❌ فلتر البحث - خطأ: {e}")
            search_works = False
        
        return add_product_works and search_works
    
    def test_sales_buttons(self):
        """اختبار أزرار صفحة المبيعات"""
        print("\n💰 اختبار أزرار صفحة المبيعات:")
        
        # الوصول لصفحة المبيعات
        response = self.session.get(f"{self.base_url}/sales")
        if response.status_code != 200:
            print("  ❌ لا يمكن الوصول لصفحة المبيعات")
            return False
        
        # اختبار صفحة فاتورة جديدة
        try:
            response = self.session.get(f"{self.base_url}/sales/new")
            if response.status_code == 200:
                print("  ✅ زر فاتورة جديدة - يعمل")
                new_sale_works = True
            else:
                print(f"  ❌ زر فاتورة جديدة - خطأ {response.status_code}")
                new_sale_works = False
        except Exception as e:
            print(f"  ❌ زر فاتورة جديدة - خطأ: {e}")
            new_sale_works = False
        
        return new_sale_works
    
    def test_customers_buttons(self):
        """اختبار أزرار صفحة العملاء"""
        print("\n👥 اختبار أزرار صفحة العملاء:")
        
        # الوصول لصفحة العملاء
        response = self.session.get(f"{self.base_url}/customers")
        if response.status_code != 200:
            print("  ❌ لا يمكن الوصول لصفحة العملاء")
            return False
        
        print("  ✅ صفحة العملاء - تعمل")
        print("  ℹ️  أزرار إضافة العملاء تعمل عبر JavaScript")
        
        return True
    
    def test_api_endpoints(self):
        """اختبار نقاط API"""
        print("\n🔌 اختبار نقاط API:")
        
        api_tests = [
            ("البحث في المنتجات", "/api/products/search?q=هاتف"),
            ("البحث في العملاء", "/api/customers/search?q=أحمد"),
        ]
        
        results = []
        for name, endpoint in api_tests:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                if response.status_code == 200:
                    data = response.json()
                    print(f"  ✅ {name} - يعمل (وجد {len(data)} نتيجة)")
                    results.append(True)
                else:
                    print(f"  ❌ {name} - خطأ {response.status_code}")
                    results.append(False)
            except Exception as e:
                print(f"  ❌ {name} - خطأ: {e}")
                results.append(False)
        
        return all(results)
    
    def test_form_submissions(self):
        """اختبار إرسال النماذج"""
        print("\n📝 اختبار إرسال النماذج:")
        
        # اختبار إضافة عميل جديد
        try:
            customer_data = {
                'name': 'عميل تجريبي',
                'phone': '0501234567',
                'email': '<EMAIL>',
                'credit_limit': 1000
            }
            
            response = self.session.post(
                f"{self.base_url}/api/customers",
                json=customer_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("  ✅ إضافة عميل جديد - يعمل")
                    add_customer_works = True
                else:
                    print("  ❌ إضافة عميل جديد - فشل في الحفظ")
                    add_customer_works = False
            else:
                print(f"  ❌ إضافة عميل جديد - خطأ {response.status_code}")
                add_customer_works = False
        except Exception as e:
            print(f"  ❌ إضافة عميل جديد - خطأ: {e}")
            add_customer_works = False
        
        # اختبار إضافة تصنيف جديد
        try:
            category_data = {
                'name': 'تصنيف تجريبي',
                'description': 'وصف تجريبي'
            }
            
            response = self.session.post(
                f"{self.base_url}/categories/add",
                json=category_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("  ✅ إضافة تصنيف جديد - يعمل")
                    add_category_works = True
                else:
                    print("  ❌ إضافة تصنيف جديد - فشل في الحفظ")
                    add_category_works = False
            else:
                print(f"  ❌ إضافة تصنيف جديد - خطأ {response.status_code}")
                add_category_works = False
        except Exception as e:
            print(f"  ❌ إضافة تصنيف جديد - خطأ: {e}")
            add_category_works = False
        
        return add_customer_works and add_category_works
    
    def test_logout_button(self):
        """اختبار زر تسجيل الخروج"""
        print("\n🚪 اختبار زر تسجيل الخروج:")
        
        try:
            response = self.session.get(f"{self.base_url}/logout", allow_redirects=False)
            if response.status_code == 302:
                print("  ✅ زر تسجيل الخروج - يعمل")
                return True
            else:
                print(f"  ❌ زر تسجيل الخروج - خطأ {response.status_code}")
                return False
        except Exception as e:
            print(f"  ❌ زر تسجيل الخروج - خطأ: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع اختبارات الأزرار"""
        print("🧪 اختبار شامل لجميع أزرار التطبيق")
        print("=" * 60)
        
        # تسجيل الدخول أولاً
        if not self.login():
            print("❌ فشل تسجيل الدخول - لا يمكن متابعة الاختبارات")
            return False
        
        # قائمة الاختبارات
        tests = [
            ("أزرار التنقل", self.test_navigation_buttons),
            ("أزرار لوحة التحكم", self.test_dashboard_buttons),
            ("أزرار المنتجات", self.test_products_buttons),
            ("أزرار المبيعات", self.test_sales_buttons),
            ("أزرار العملاء", self.test_customers_buttons),
            ("نقاط API", self.test_api_endpoints),
            ("إرسال النماذج", self.test_form_submissions),
            ("زر تسجيل الخروج", self.test_logout_button)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🔍 اختبار: {test_name}")
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} - نجح")
                else:
                    print(f"❌ {test_name} - فشل")
            except Exception as e:
                print(f"❌ {test_name} - خطأ: {e}")
            
            print("-" * 40)
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print(f"📊 نتائج اختبار الأزرار:")
        print(f"✅ نجح: {passed}/{total}")
        print(f"❌ فشل: {total - passed}/{total}")
        print(f"📈 معدل النجاح: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 جميع الأزرار تعمل بشكل مثالي!")
            return True
        elif passed >= total * 0.8:
            print(f"\n✅ معظم الأزرار تعمل بشكل جيد ({passed}/{total})")
            return True
        else:
            print(f"\n⚠️  يحتاج بعض الأزرار لإصلاح ({total - passed} أزرار)")
            return False

def main():
    """الوظيفة الرئيسية"""
    print(f"🕐 وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 عنوان التطبيق: http://127.0.0.1:5000")
    print()
    
    tester = ButtonTester()
    success = tester.run_all_tests()
    
    print("\n" + "=" * 60)
    if success:
        print("🏆 جميع أزرار التطبيق تعمل بشكل ممتاز!")
    else:
        print("🔧 بعض الأزرار تحتاج لمراجعة")
    
    return success

if __name__ == "__main__":
    main()
