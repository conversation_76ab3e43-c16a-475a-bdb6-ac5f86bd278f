#!/usr/bin/env python3
"""
فحص جاهزية التطبيق للنشر الإنتاجي
"""

import os
import sys
import sqlite3
import requests
from datetime import datetime

class ProductionReadinessChecker:
    def __init__(self):
        self.issues = []
        self.warnings = []
        self.recommendations = []
        self.score = 0
        self.max_score = 0
        
    def add_issue(self, category, description, severity="medium"):
        """إضافة مشكلة"""
        self.issues.append({
            'category': category,
            'description': description,
            'severity': severity
        })
        print(f"❌ [{category}] {description}")
    
    def add_warning(self, category, description):
        """إضافة تحذير"""
        self.warnings.append({
            'category': category,
            'description': description
        })
        print(f"⚠️  [{category}] {description}")
    
    def add_recommendation(self, category, description):
        """إضافة توصية"""
        self.recommendations.append({
            'category': category,
            'description': description
        })
        print(f"💡 [{category}] {description}")
    
    def check_point(self, description, condition, weight=1):
        """فحص نقطة معينة"""
        self.max_score += weight
        if condition:
            self.score += weight
            print(f"✅ {description}")
            return True
        else:
            print(f"❌ {description}")
            return False
    
    def check_security_config(self):
        """فحص إعدادات الأمان"""
        print("\n🔒 فحص إعدادات الأمان...")
        
        # فحص SECRET_KEY
        try:
            from config import Config
            secret_key = getattr(Config, 'SECRET_KEY', None)
            if secret_key and secret_key != 'dev-secret-key':
                self.check_point("SECRET_KEY آمن ومخصص", True)
            else:
                self.check_point("SECRET_KEY آمن ومخصص", False)
                self.add_issue("Security", "SECRET_KEY يجب تغييره للإنتاج", "high")
        except:
            self.add_issue("Security", "لا يمكن الوصول لإعدادات الأمان", "high")
        
        # فحص DEBUG mode
        try:
            debug_mode = getattr(Config, 'DEBUG', True)
            self.check_point("DEBUG mode مغلق", not debug_mode)
            if debug_mode:
                self.add_issue("Security", "DEBUG mode يجب إغلاقه في الإنتاج", "high")
        except:
            self.add_warning("Security", "لا يمكن تحديد حالة DEBUG mode")
    
    def check_database_config(self):
        """فحص إعدادات قاعدة البيانات"""
        print("\n🗄️ فحص إعدادات قاعدة البيانات...")
        
        # فحص وجود قاعدة البيانات
        db_paths = ['instance/sales_inventory.db', 'sales_inventory.db']
        db_found = False
        
        for db_path in db_paths:
            if os.path.exists(db_path):
                db_found = True
                self.check_point(f"قاعدة البيانات موجودة ({db_path})", True)
                
                # فحص سلامة قاعدة البيانات
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    # فحص الجداول
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [table[0] for table in cursor.fetchall()]
                    
                    required_tables = ['user', 'category', 'product', 'customer', 'invoice', 'invoice_item', 'stock_movement']
                    all_tables_exist = all(table in tables for table in required_tables)
                    
                    self.check_point("جميع الجداول المطلوبة موجودة", all_tables_exist)
                    
                    # فحص وجود مستخدم مدير
                    cursor.execute("SELECT COUNT(*) FROM user WHERE role='admin'")
                    admin_count = cursor.fetchone()[0]
                    self.check_point("يوجد مستخدم مدير", admin_count > 0)
                    
                    conn.close()
                    
                except Exception as e:
                    self.add_issue("Database", f"خطأ في فحص قاعدة البيانات: {e}", "high")
                
                break
        
        if not db_found:
            self.check_point("قاعدة البيانات موجودة", False)
            self.add_issue("Database", "قاعدة البيانات غير موجودة", "high")
    
    def check_dependencies(self):
        """فحص التبعيات"""
        print("\n📦 فحص التبعيات...")
        
        # فحص requirements.txt
        if os.path.exists('requirements.txt'):
            self.check_point("ملف requirements.txt موجود", True)
            
            # قراءة التبعيات
            with open('requirements.txt', 'r') as f:
                requirements = f.read()
                
            # فحص التبعيات الأساسية
            essential_deps = ['Flask', 'SQLAlchemy', 'Flask-Login', 'Werkzeug']
            for dep in essential_deps:
                if dep.lower() in requirements.lower():
                    self.check_point(f"تبعية {dep} موجودة", True)
                else:
                    self.check_point(f"تبعية {dep} موجودة", False)
                    self.add_issue("Dependencies", f"تبعية {dep} مفقودة", "medium")
        else:
            self.check_point("ملف requirements.txt موجود", False)
            self.add_issue("Dependencies", "ملف requirements.txt مفقود", "medium")
    
    def check_static_files(self):
        """فحص الملفات الثابتة"""
        print("\n📁 فحص الملفات الثابتة...")
        
        static_files = [
            'static/css/style.css',
            'static/js/app.js',
            'static/manifest.json',
            'static/sw.js'
        ]
        
        for file_path in static_files:
            exists = os.path.exists(file_path)
            self.check_point(f"ملف {file_path} موجود", exists)
            if not exists:
                self.add_warning("Static Files", f"ملف {file_path} مفقود")
    
    def check_pwa_readiness(self):
        """فحص جاهزية PWA"""
        print("\n📱 فحص جاهزية PWA...")
        
        # فحص manifest.json
        if os.path.exists('static/manifest.json'):
            self.check_point("ملف manifest.json موجود", True)
            try:
                import json
                with open('static/manifest.json', 'r') as f:
                    manifest = json.load(f)
                
                required_fields = ['name', 'short_name', 'start_url', 'display', 'icons']
                for field in required_fields:
                    if field in manifest:
                        self.check_point(f"حقل {field} في manifest", True)
                    else:
                        self.check_point(f"حقل {field} في manifest", False)
                        self.add_warning("PWA", f"حقل {field} مفقود في manifest")
                        
            except Exception as e:
                self.add_warning("PWA", f"خطأ في قراءة manifest.json: {e}")
        else:
            self.check_point("ملف manifest.json موجود", False)
        
        # فحص Service Worker
        if os.path.exists('static/sw.js'):
            self.check_point("ملف Service Worker موجود", True)
        else:
            self.check_point("ملف Service Worker موجود", False)
        
        # فحص الأيقونات
        icon_sizes = ['144x144', '192x192', '512x512']
        for size in icon_sizes:
            icon_path = f'static/images/icon-{size}.png'
            exists = os.path.exists(icon_path)
            self.check_point(f"أيقونة {size} موجودة", exists)
    
    def check_performance(self):
        """فحص الأداء"""
        print("\n⚡ فحص الأداء...")
        
        try:
            # اختبار سرعة الاستجابة
            import time
            start_time = time.time()
            response = requests.get('http://127.0.0.1:5000/', timeout=5)
            end_time = time.time()
            
            response_time = end_time - start_time
            
            if response_time < 1.0:
                self.check_point("وقت الاستجابة سريع (< 1 ثانية)", True)
            elif response_time < 3.0:
                self.check_point("وقت الاستجابة مقبول (< 3 ثواني)", True)
                self.add_warning("Performance", f"وقت الاستجابة بطيء نسبياً: {response_time:.2f} ثانية")
            else:
                self.check_point("وقت الاستجابة مقبول", False)
                self.add_issue("Performance", f"وقت الاستجابة بطيء جداً: {response_time:.2f} ثانية", "medium")
                
        except requests.exceptions.ConnectionError:
            self.check_point("التطبيق يعمل", False)
            self.add_issue("Performance", "التطبيق غير متاح للاختبار", "high")
        except Exception as e:
            self.add_warning("Performance", f"خطأ في اختبار الأداء: {e}")
    
    def check_production_server(self):
        """فحص إعدادات الخادم الإنتاجي"""
        print("\n🖥️ فحص إعدادات الخادم الإنتاجي...")
        
        # فحص وجود ملف WSGI
        wsgi_files = ['wsgi.py', 'app.py']
        wsgi_found = False
        for wsgi_file in wsgi_files:
            if os.path.exists(wsgi_file):
                wsgi_found = True
                self.check_point(f"ملف WSGI ({wsgi_file}) موجود", True)
                break
        
        if not wsgi_found:
            self.check_point("ملف WSGI موجود", False)
            self.add_recommendation("Deployment", "إنشاء ملف wsgi.py للنشر الإنتاجي")
        
        # التحقق من Flask development server
        self.add_recommendation("Deployment", "استخدام خادم إنتاجي مثل Gunicorn أو uWSGI بدلاً من Flask development server")
        self.add_recommendation("Deployment", "استخدام خادم ويب عكسي مثل Nginx")
    
    def check_backup_strategy(self):
        """فحص استراتيجية النسخ الاحتياطية"""
        print("\n💾 فحص استراتيجية النسخ الاحتياطية...")
        
        if os.path.exists('backup.py'):
            self.check_point("نظام النسخ الاحتياطية موجود", True)
        else:
            self.check_point("نظام النسخ الاحتياطية موجود", False)
            self.add_recommendation("Backup", "إضافة نظام نسخ احتياطية تلقائية")
    
    def generate_deployment_recommendations(self):
        """توليد توصيات النشر"""
        print("\n📋 توصيات النشر الإنتاجي:")
        
        deployment_recommendations = [
            "استخدام متغيرات البيئة لإعدادات الأمان",
            "تفعيل HTTPS مع شهادة SSL",
            "إعداد نظام مراقبة ولوجات",
            "تفعيل ضغط الملفات الثابتة",
            "إعداد CDN للملفات الثابتة",
            "تفعيل نظام cache للأداء",
            "إعداد نسخ احتياطية تلقائية",
            "تفعيل نظام تنبيهات للأخطاء",
            "إعداد load balancer للتوسع",
            "تفعيل نظام مراقبة الأداء"
        ]
        
        for rec in deployment_recommendations:
            self.add_recommendation("Deployment", rec)
    
    def run_production_check(self):
        """تشغيل فحص الجاهزية للإنتاج"""
        print("🔍 فحص جاهزية التطبيق للنشر الإنتاجي")
        print("=" * 60)
        print(f"🕐 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # تشغيل جميع الفحوصات
        checks = [
            self.check_security_config,
            self.check_database_config,
            self.check_dependencies,
            self.check_static_files,
            self.check_pwa_readiness,
            self.check_performance,
            self.check_production_server,
            self.check_backup_strategy
        ]
        
        for check in checks:
            try:
                check()
            except Exception as e:
                print(f"💥 خطأ في الفحص: {e}")
        
        # توليد التوصيات
        self.generate_deployment_recommendations()
        
        # عرض النتائج
        self.show_results()
    
    def show_results(self):
        """عرض نتائج الفحص"""
        print("\n" + "=" * 60)
        print("📊 نتائج فحص الجاهزية للإنتاج")
        print("=" * 60)
        
        # النتيجة الإجمالية
        if self.max_score > 0:
            percentage = (self.score / self.max_score) * 100
            print(f"\n📈 نتيجة الجاهزية: {self.score}/{self.max_score} ({percentage:.1f}%)")
        
        # عرض المشاكل
        if self.issues:
            print(f"\n❌ مشاكل يجب حلها ({len(self.issues)}):")
            for issue in self.issues:
                severity_icon = {
                    'high': '🔴',
                    'medium': '🟡',
                    'low': '🟢'
                }.get(issue['severity'], '⚪')
                print(f"  {severity_icon} [{issue['category']}] {issue['description']}")
        
        # عرض التحذيرات
        if self.warnings:
            print(f"\n⚠️  تحذيرات ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  🟡 [{warning['category']}] {warning['description']}")
        
        # عرض التوصيات
        if self.recommendations:
            print(f"\n💡 توصيات للتحسين ({len(self.recommendations)}):")
            for rec in self.recommendations[:10]:  # أول 10 توصيات
                print(f"  💡 [{rec['category']}] {rec['description']}")
        
        # التقييم النهائي
        high_issues = len([i for i in self.issues if i['severity'] == 'high'])
        medium_issues = len([i for i in self.issues if i['severity'] == 'medium'])
        
        print(f"\n🎯 التقييم النهائي:")
        if high_issues == 0 and medium_issues <= 2 and percentage >= 80:
            print("🎉 التطبيق جاهز للنشر الإنتاجي!")
            print("✅ يمكن نشره مع تطبيق التوصيات")
            return True
        elif high_issues == 0 and percentage >= 70:
            print("⚠️  التطبيق قريب من الجاهزية")
            print("🔧 يحتاج لبعض التحسينات قبل النشر")
            return False
        else:
            print("❌ التطبيق غير جاهز للنشر الإنتاجي")
            print("🛠️ يحتاج لإصلاحات مهمة قبل النشر")
            return False

def main():
    """الوظيفة الرئيسية"""
    checker = ProductionReadinessChecker()
    ready = checker.run_production_check()
    return ready

if __name__ == "__main__":
    main()
