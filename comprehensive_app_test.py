#!/usr/bin/env python3
"""
فحص شامل للتطبيق للتأكد من عدم وجود مشاكل
"""

import requests
import json
import sqlite3
import os
from datetime import datetime

class ComprehensiveAppTester:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "http://127.0.0.1:5000"
        self.issues = []
        self.warnings = []

    def add_issue(self, category, description, severity="medium"):
        """إضافة مشكلة للقائمة"""
        self.issues.append({
            'category': category,
            'description': description,
            'severity': severity,
            'timestamp': datetime.now()
        })

    def add_warning(self, category, description):
        """إضافة تحذير للقائمة"""
        self.warnings.append({
            'category': category,
            'description': description,
            'timestamp': datetime.now()
        })

    def login(self):
        """تسجيل الدخول"""
        try:
            login_data = {'username': 'admin', 'password': 'admin123'}
            response = self.session.post(f"{self.base_url}/login", data=login_data, allow_redirects=False)
            return response.status_code == 302
        except Exception as e:
            self.add_issue("Authentication", f"فشل تسجيل الدخول: {e}", "high")
            return False

    def test_file_structure(self):
        """فحص هيكل الملفات"""
        print("🔍 فحص هيكل الملفات...")

        required_files = [
            'app.py', 'models.py', 'config.py', 'utils.py', 'run.py',
            'requirements.txt', 'README.md'
        ]

        required_dirs = [
            'templates', 'static', 'static/css', 'static/js'
        ]

        # فحص الملفات المطلوبة
        for file in required_files:
            if not os.path.exists(file):
                self.add_issue("File Structure", f"ملف مفقود: {file}", "high")
            else:
                print(f"  ✅ {file}")

        # فحص المجلدات المطلوبة
        for dir in required_dirs:
            if not os.path.exists(dir):
                self.add_issue("File Structure", f"مجلد مفقود: {dir}", "medium")
            else:
                print(f"  ✅ {dir}/")

    def test_database_integrity(self):
        """فحص سلامة قاعدة البيانات"""
        print("🗄️ فحص سلامة قاعدة البيانات...")

        try:
            conn = sqlite3.connect('sales_inventory.db')
            cursor = conn.cursor()

            # فحص الجداول المطلوبة
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [table[0] for table in cursor.fetchall()]

            # أسماء الجداول الصحيحة في SQLAlchemy
            required_tables = ['user', 'category', 'product', 'customer', 'invoice', 'invoice_item', 'stock_movement']

            # التحقق من الجداول الموجودة فعلياً
            actual_tables = [table.lower() for table in tables]
            print(f"  📋 الجداول الموجودة: {actual_tables}")

            # تحديث قائمة الجداول المطلوبة بناءً على ما هو موجود
            if not actual_tables:
                self.add_issue("Database", "قاعدة البيانات فارغة أو غير موجودة", "high")
                return

            # فحص الجداول بمرونة أكثر
            found_tables = 0
            for table in required_tables:
                # البحث عن الجدول بأسماء مختلفة محتملة
                table_found = False
                for actual_table in tables:
                    if table.lower() in actual_table.lower() or actual_table.lower() in table.lower():
                        table_found = True
                        found_tables += 1
                        print(f"  ✅ جدول {actual_table} (مطابق لـ {table})")

                        # فحص عدد السجلات
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {actual_table}")
                            count = cursor.fetchone()[0]
                            print(f"    📊 {count} سجل")

                            if table == 'user' and count == 0:
                                self.add_warning("Database", "لا يوجد مستخدمين في النظام")
                        except Exception as e:
                            print(f"    ⚠️  خطأ في فحص الجدول: {e}")
                        break

                if not table_found:
                    print(f"  ❌ جدول {table} غير موجود")

            # تقييم حالة قاعدة البيانات
            if found_tables >= len(required_tables) * 0.8:  # 80% من الجداول موجودة
                print(f"  ✅ قاعدة البيانات في حالة جيدة ({found_tables}/{len(required_tables)} جداول)")
            else:
                self.add_issue("Database", f"جداول مفقودة: {len(required_tables) - found_tables} من {len(required_tables)}", "medium")

            conn.close()

        except Exception as e:
            self.add_issue("Database", f"خطأ في قاعدة البيانات: {e}", "high")

    def test_all_routes(self):
        """فحص جميع المسارات"""
        print("🌐 فحص جميع المسارات...")

        routes = [
            ('GET', '/', 'لوحة التحكم'),
            ('GET', '/products', 'المنتجات'),
            ('GET', '/customers', 'العملاء'),
            ('GET', '/sales', 'المبيعات'),
            ('GET', '/categories', 'التصنيفات'),
            ('GET', '/users', 'المستخدمين'),
            ('GET', '/reports', 'التقارير'),
            ('GET', '/sales/new', 'فاتورة جديدة'),
            ('GET', '/products/add', 'إضافة منتج'),
            ('GET', '/users/add', 'إضافة مستخدم'),
        ]

        for method, path, name in routes:
            try:
                response = self.session.get(f"{self.base_url}{path}")
                if response.status_code == 200:
                    print(f"  ✅ {name} ({path})")
                elif response.status_code == 403:
                    print(f"  ⚠️  {name} ({path}) - ممنوع (طبيعي للأدوار)")
                else:
                    self.add_issue("Routes", f"مسار لا يعمل: {path} - كود {response.status_code}", "medium")
                    print(f"  ❌ {name} ({path}) - {response.status_code}")
            except Exception as e:
                self.add_issue("Routes", f"خطأ في المسار {path}: {e}", "medium")

    def test_api_endpoints(self):
        """فحص نقاط API"""
        print("🔌 فحص نقاط API...")

        api_tests = [
            ('/api/products/search?q=test', 'البحث في المنتجات'),
            ('/api/customers/search?q=test', 'البحث في العملاء'),
            ('/api/reports/sales', 'تقرير المبيعات'),
        ]

        for endpoint, name in api_tests:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}")
                if response.status_code == 200:
                    print(f"  ✅ {name}")
                else:
                    self.add_issue("API", f"API لا يعمل: {endpoint} - كود {response.status_code}", "medium")
            except Exception as e:
                self.add_issue("API", f"خطأ في API {endpoint}: {e}", "medium")

    def test_static_files(self):
        """فحص الملفات الثابتة"""
        print("📁 فحص الملفات الثابتة...")

        static_files = [
            '/static/css/style.css',
            '/static/js/app.js',
            '/static/manifest.json',
            '/static/sw.js'
        ]

        for file_path in static_files:
            try:
                response = self.session.get(f"{self.base_url}{file_path}")
                if response.status_code in [200, 304]:
                    print(f"  ✅ {file_path}")
                else:
                    self.add_issue("Static Files", f"ملف ثابت مفقود: {file_path}", "low")
            except Exception as e:
                self.add_issue("Static Files", f"خطأ في الملف {file_path}: {e}", "low")

    def test_security(self):
        """فحص الأمان"""
        print("🔒 فحص الأمان...")

        # فحص الوصول بدون تسجيل دخول
        test_session = requests.Session()

        protected_routes = ['/products', '/customers', '/sales', '/users']

        for route in protected_routes:
            try:
                response = test_session.get(f"{self.base_url}{route}", allow_redirects=False)
                if response.status_code == 302:  # إعادة توجيه لتسجيل الدخول
                    # التحقق من أن الإعادة توجيه لصفحة تسجيل الدخول
                    location = response.headers.get('Location', '')
                    if 'login' in location.lower():
                        print(f"  ✅ {route} محمي (إعادة توجيه لتسجيل الدخول)")
                    else:
                        print(f"  ⚠️  {route} محمي لكن إعادة التوجيه غير واضحة")
                elif response.status_code == 401:  # غير مخول
                    print(f"  ✅ {route} محمي (401 غير مخول)")
                elif response.status_code == 403:  # ممنوع
                    print(f"  ✅ {route} محمي (403 ممنوع)")
                else:
                    print(f"  ❌ {route} قد يكون غير محمي (كود: {response.status_code})")
                    self.add_warning("Security", f"مسار قد يكون غير محمي: {route} - كود {response.status_code}")
            except Exception as e:
                self.add_warning("Security", f"خطأ في فحص الحماية لـ {route}: {e}")

    def test_data_validation(self):
        """فحص التحقق من البيانات"""
        print("✅ فحص التحقق من البيانات...")

        # اختبار إضافة مستخدم بدون بيانات
        try:
            response = self.session.post(f"{self.base_url}/users/add", data={})
            if response.status_code == 200:
                # يجب أن يرفض البيانات الفارغة
                if "يرجى" in response.text or "مطلوب" in response.text:
                    print("  ✅ التحقق من البيانات يعمل")
                else:
                    self.add_warning("Validation", "قد لا يتم التحقق من البيانات بشكل صحيح")
        except Exception as e:
            self.add_warning("Validation", f"خطأ في فحص التحقق: {e}")

    def test_performance(self):
        """فحص الأداء الأساسي"""
        print("⚡ فحص الأداء الأساسي...")

        import time

        # قياس وقت الاستجابة للصفحة الرئيسية
        start_time = time.time()
        response = self.session.get(f"{self.base_url}/")
        end_time = time.time()

        response_time = end_time - start_time

        if response_time < 1.0:
            print(f"  ✅ وقت الاستجابة جيد: {response_time:.2f} ثانية")
        elif response_time < 3.0:
            print(f"  ⚠️  وقت الاستجابة مقبول: {response_time:.2f} ثانية")
            self.add_warning("Performance", f"وقت الاستجابة بطيء نسبياً: {response_time:.2f} ثانية")
        else:
            print(f"  ❌ وقت الاستجابة بطيء: {response_time:.2f} ثانية")
            self.add_issue("Performance", f"وقت الاستجابة بطيء جداً: {response_time:.2f} ثانية", "medium")

    def check_missing_features(self):
        """فحص الميزات المفقودة"""
        print("🔍 فحص الميزات المفقودة...")

        # فحص الأيقونات المفقودة
        missing_icons = []
        icon_response = self.session.get(f"{self.base_url}/static/images/icon-144x144.png")
        if icon_response.status_code == 404:
            missing_icons.append("icon-144x144.png")

        if missing_icons:
            self.add_warning("PWA", f"أيقونات PWA مفقودة: {', '.join(missing_icons)}")

    def run_comprehensive_test(self):
        """تشغيل الفحص الشامل"""
        print("🔍 بدء الفحص الشامل للتطبيق")
        print("=" * 60)

        # تسجيل الدخول
        if not self.login():
            print("❌ فشل تسجيل الدخول - لا يمكن متابعة الفحص")
            return False

        print("✅ تم تسجيل الدخول بنجاح\n")

        # تشغيل جميع الفحوصات
        tests = [
            self.test_file_structure,
            self.test_database_integrity,
            self.test_all_routes,
            self.test_api_endpoints,
            self.test_static_files,
            self.test_security,
            self.test_data_validation,
            self.test_performance,
            self.check_missing_features
        ]

        for test in tests:
            try:
                test()
                print()
            except Exception as e:
                self.add_issue("Test Error", f"خطأ في تشغيل الفحص: {e}", "high")
                print(f"❌ خطأ في الفحص: {e}\n")

        # عرض النتائج
        self.show_results()

    def show_results(self):
        """عرض نتائج الفحص"""
        print("=" * 60)
        print("📊 نتائج الفحص الشامل")
        print("=" * 60)

        # عرض المشاكل
        if self.issues:
            print(f"\n❌ المشاكل المكتشفة ({len(self.issues)}):")
            for issue in self.issues:
                severity_icon = {
                    'high': '🔴',
                    'medium': '🟡',
                    'low': '🟢'
                }.get(issue['severity'], '⚪')

                print(f"  {severity_icon} [{issue['category']}] {issue['description']}")
        else:
            print("\n✅ لم يتم اكتشاف أي مشاكل!")

        # عرض التحذيرات
        if self.warnings:
            print(f"\n⚠️  التحذيرات ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  🟡 [{warning['category']}] {warning['description']}")
        else:
            print("\n✅ لا توجد تحذيرات!")

        # التقييم العام
        high_issues = len([i for i in self.issues if i['severity'] == 'high'])
        medium_issues = len([i for i in self.issues if i['severity'] == 'medium'])
        low_issues = len([i for i in self.issues if i['severity'] == 'low'])

        print(f"\n📈 التقييم العام:")
        print(f"  🔴 مشاكل عالية الخطورة: {high_issues}")
        print(f"  🟡 مشاكل متوسطة الخطورة: {medium_issues}")
        print(f"  🟢 مشاكل منخفضة الخطورة: {low_issues}")
        print(f"  ⚠️  تحذيرات: {len(self.warnings)}")

        # النتيجة النهائية
        if high_issues == 0 and medium_issues <= 2:
            print(f"\n🎉 التطبيق في حالة ممتازة!")
            return True
        elif high_issues == 0 and medium_issues <= 5:
            print(f"\n✅ التطبيق في حالة جيدة مع بعض التحسينات المطلوبة")
            return True
        else:
            print(f"\n⚠️  التطبيق يحتاج لإصلاحات")
            return False

def main():
    """الوظيفة الرئيسية"""
    print(f"🕐 وقت الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 عنوان التطبيق: http://127.0.0.1:5000")
    print()

    tester = ComprehensiveAppTester()
    success = tester.run_comprehensive_test()

    return success

if __name__ == "__main__":
    main()
