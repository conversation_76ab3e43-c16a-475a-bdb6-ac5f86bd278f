2025-05-24 18:29:52,349 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 18:49:19,403 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 18:50:00,280 ERROR: Exception on /customers [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 137, in customers
    return render_template('customers.html', customers=customers)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 109, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 114, in block 'content'
    {% if customer.current_balance > 0 %}
    
TypeError: '>' not supported between instances of 'NoneType' and 'int'
2025-05-24 18:52:13,717 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 18:52:46,946 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 18:55:08,118 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 18:57:17,399 ERROR: Exception on /customers [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 137, in customers
    return render_template('customers.html', customers=customers)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 109, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 114, in block 'content'
    {% if customer.current_balance > 0 %}
    
TypeError: '>' not supported between instances of 'NoneType' and 'int'
2025-05-24 18:57:29,659 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 18:58:10,153 ERROR: Exception on /customers [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 137, in customers
    return render_template('customers.html', customers=customers)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 109, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 114, in block 'content'
    {% if customer.current_balance > 0 %}
    
TypeError: '>' not supported between instances of 'NoneType' and 'int'
2025-05-24 18:59:12,521 ERROR: Exception on /customers [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 137, in customers
    return render_template('customers.html', customers=customers)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 109, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 114, in block 'content'
    {% if customer.current_balance > 0 %}
    
TypeError: '>' not supported between instances of 'NoneType' and 'int'
2025-05-24 19:00:38,465 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 19:08:31,866 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 20:27:26,728 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 20:49:10,579 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 20:59:44,069 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 21:02:31,691 ERROR: Exception on /profits/report [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 387, in profit_report
    return render_template('profit_report.html', data=data, report_type=report_type)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/profit_report.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/profit_report.html", line 335, in block 'content'
    <p class="mb-1"><strong>تاريخ الإنشاء:</strong> {{ moment().format('YYYY-MM-DD HH:mm') }}</p>
    ^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'moment' is undefined
2025-05-24 21:02:53,906 ERROR: Exception on /profits/report [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 387, in profit_report
    return render_template('profit_report.html', data=data, report_type=report_type)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/profit_report.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/profit_report.html", line 335, in block 'content'
    <p class="mb-1"><strong>تاريخ الإنشاء:</strong> {{ moment().format('YYYY-MM-DD HH:mm') }}</p>
    ^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'moment' is undefined
2025-05-24 21:03:20,191 ERROR: Exception on /profits/report [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 387, in profit_report
    return render_template('profit_report.html', data=data, report_type=report_type)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/profit_report.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/profit_report.html", line 335, in block 'content'
    <p class="mb-1"><strong>تاريخ الإنشاء:</strong> {{ moment().format('YYYY-MM-DD HH:mm') }}</p>
    ^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'moment' is undefined
2025-05-24 21:05:30,592 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 21:22:47,001 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 22:03:33,980 ERROR: Exception on / [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 40, in index
    return render_template('dashboard.html', **dashboard_data)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 150, in block 'content'
    {% if low_stock_products > 0 %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'low_stock_products' is undefined
2025-05-24 22:03:34,084 ERROR: Exception on / [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 40, in index
    return render_template('dashboard.html', **dashboard_data)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 150, in block 'content'
    {% if low_stock_products > 0 %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'low_stock_products' is undefined
2025-05-24 22:03:42,714 ERROR: Exception on / [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 40, in index
    return render_template('dashboard.html', **dashboard_data)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 150, in block 'content'
    {% if low_stock_products > 0 %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'low_stock_products' is undefined
2025-05-24 22:03:44,972 ERROR: Exception on / [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 40, in index
    return render_template('dashboard.html', **dashboard_data)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 150, in block 'content'
    {% if low_stock_products > 0 %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'low_stock_products' is undefined
2025-05-24 22:04:20,087 ERROR: Exception on / [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 40, in index
    return render_template('dashboard.html', **dashboard_data)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 150, in block 'content'
    {% if low_stock_products > 0 %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'low_stock_products' is undefined
2025-05-24 22:04:57,669 ERROR: Exception on / [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 40, in index
    return render_template('dashboard.html', **dashboard_data)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 150, in block 'content'
    {% if low_stock_products > 0 %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'low_stock_products' is undefined
2025-05-24 22:05:06,971 ERROR: Exception on / [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 40, in index
    return render_template('dashboard.html', **dashboard_data)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 150, in block 'content'
    {% if low_stock_products > 0 %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'low_stock_products' is undefined
2025-05-24 22:05:41,195 ERROR: Exception on / [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 40, in index
    return render_template('dashboard.html', **dashboard_data)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/dashboard.html", line 150, in block 'content'
    {% if low_stock_products > 0 %}
    ^^^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'low_stock_products' is undefined
2025-05-26 17:10:10,122 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 17:10:10,499 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 17:16:15,352 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 21:17:16,720 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 21:18:16,401 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 21:18:43,022 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 21:19:01,855 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 21:20:17,590 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 21:20:55,186 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 21:26:55,880 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 21:27:36,245 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 21:27:36,646 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 22:06:13,728 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 22:09:23,040 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 22:10:32,305 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 22:11:14,142 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 22:11:14,620 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 22:12:32,207 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 22:13:02,777 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 22:13:11,821 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-26 22:13:12,378 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 19:50:20,747 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 19:50:21,274 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 19:53:07,423 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 19:53:14,929 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 19:53:15,338 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 19:55:36,147 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 19:58:20,322 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 19:59:51,411 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 20:00:22,572 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 20:10:56,286 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 20:10:56,685 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 20:12:49,839 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 20:13:19,453 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 20:13:32,000 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 20:59:18,406 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-27 20:59:18,834 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 19:28:50,257 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 19:28:50,869 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 21:38:27,154 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 21:38:49,016 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 21:39:08,805 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 21:39:32,539 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 21:40:23,799 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 21:40:45,765 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 21:41:03,746 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 21:41:23,655 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 21:41:40,751 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 21:42:48,833 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 21:43:12,750 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-30 21:43:35,462 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-31 18:21:50,848 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
2025-05-31 18:21:51,396 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:116]
