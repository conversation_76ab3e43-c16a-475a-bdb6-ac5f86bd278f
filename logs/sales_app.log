2025-05-24 18:29:52,349 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 18:49:19,403 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 18:50:00,280 ERROR: Exception on /customers [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 137, in customers
    return render_template('customers.html', customers=customers)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 109, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 114, in block 'content'
    {% if customer.current_balance > 0 %}
    
TypeError: '>' not supported between instances of 'NoneType' and 'int'
2025-05-24 18:52:13,717 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 18:52:46,946 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 18:55:08,118 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 18:57:17,399 ERROR: Exception on /customers [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 137, in customers
    return render_template('customers.html', customers=customers)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 109, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 114, in block 'content'
    {% if customer.current_balance > 0 %}
    
TypeError: '>' not supported between instances of 'NoneType' and 'int'
2025-05-24 18:57:29,659 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 18:58:10,153 ERROR: Exception on /customers [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 137, in customers
    return render_template('customers.html', customers=customers)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 109, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 114, in block 'content'
    {% if customer.current_balance > 0 %}
    
TypeError: '>' not supported between instances of 'NoneType' and 'int'
2025-05-24 18:59:12,521 ERROR: Exception on /customers [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 137, in customers
    return render_template('customers.html', customers=customers)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 109, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/customers.html", line 114, in block 'content'
    {% if customer.current_balance > 0 %}
    
TypeError: '>' not supported between instances of 'NoneType' and 'int'
2025-05-24 19:00:38,465 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 19:08:31,866 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 20:27:26,728 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 20:49:10,579 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 20:59:44,069 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 21:02:31,691 ERROR: Exception on /profits/report [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 387, in profit_report
    return render_template('profit_report.html', data=data, report_type=report_type)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/profit_report.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/profit_report.html", line 335, in block 'content'
    <p class="mb-1"><strong>تاريخ الإنشاء:</strong> {{ moment().format('YYYY-MM-DD HH:mm') }}</p>
    ^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'moment' is undefined
2025-05-24 21:02:53,906 ERROR: Exception on /profits/report [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 387, in profit_report
    return render_template('profit_report.html', data=data, report_type=report_type)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/profit_report.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/profit_report.html", line 335, in block 'content'
    <p class="mb-1"><strong>تاريخ الإنشاء:</strong> {{ moment().format('YYYY-MM-DD HH:mm') }}</p>
    ^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'moment' is undefined
2025-05-24 21:03:20,191 ERROR: Exception on /profits/report [GET] [in /home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py:875]
Traceback (most recent call last):
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask_login/utils.py", line 290, in decorated_view
    return current_app.ensure_sync(func)(*args, **kwargs)
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/app.py", line 387, in profit_report
    return render_template('profit_report.html', data=data, report_type=report_type)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 150, in render_template
    return _render(app, template, context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/flask/templating.py", line 131, in _render
    rv = template.render(context)
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 1295, in render
    self.environment.handle_exception()
    ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/environment.py", line 942, in handle_exception
    raise rewrite_traceback_stack(source=source)
  File "/home/<USER>/سطح المكتب/lly/templates/profit_report.html", line 1, in top-level template code
    {% extends "base.html" %}
  File "/home/<USER>/سطح المكتب/lly/templates/base.html", line 123, in top-level template code
    {% block content %}{% endblock %}
    ^^^^^^^^^^^^^^^^^
  File "/home/<USER>/سطح المكتب/lly/templates/profit_report.html", line 335, in block 'content'
    <p class="mb-1"><strong>تاريخ الإنشاء:</strong> {{ moment().format('YYYY-MM-DD HH:mm') }}</p>
    ^^^^^
  File "/home/<USER>/سطح المكتب/lly/venv/lib/python3.13/site-packages/jinja2/utils.py", line 92, in from_obj
    if hasattr(obj, "jinja_pass_arg"):
       ~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^
jinja2.exceptions.UndefinedError: 'moment' is undefined
2025-05-24 21:05:30,592 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
2025-05-24 21:22:47,001 INFO: Sales App startup [in /home/<USER>/سطح المكتب/lly/config.py:113]
