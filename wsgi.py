#!/usr/bin/env python3
"""
ملف WSGI للنشر الإنتاجي
"""

import os
import sys

# إضافة مجلد التطبيق إلى مسار Python
sys.path.insert(0, os.path.dirname(__file__))

# تعيين متغيرات البيئة للإنتاج
os.environ.setdefault('FLASK_ENV', 'production')

from app import app

# إنشاء البيانات الأولية إذا لم تكن موجودة
from app import create_initial_data
create_initial_data()

if __name__ == "__main__":
    app.run()
