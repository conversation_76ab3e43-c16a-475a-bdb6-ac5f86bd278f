#!/usr/bin/env python3
"""
فحص شامل لجميع خيارات التطبيق
"""

import requests
import json
import time
from datetime import datetime

class FullAppTester:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "http://127.0.0.1:5000"
        self.results = {}
        self.errors = []
        self.warnings = []
        self.successes = []

    def log_result(self, category, test_name, status, details=""):
        """تسجيل نتيجة الاختبار"""
        if category not in self.results:
            self.results[category] = {}

        self.results[category][test_name] = {
            'status': status,
            'details': details,
            'timestamp': datetime.now()
        }

        if status == 'success':
            print(f"  ✅ {test_name}")
            self.successes.append(f"{category}: {test_name}")
        elif status == 'warning':
            print(f"  ⚠️  {test_name} - {details}")
            self.warnings.append(f"{category}: {test_name} - {details}")
        else:
            print(f"  ❌ {test_name} - {details}")
            self.errors.append(f"{category}: {test_name} - {details}")

    def login(self):
        """تسجيل الدخول"""
        print("🔐 اختبار تسجيل الدخول...")
        try:
            # اختبار صفحة تسجيل الدخول
            response = self.session.get(f"{self.base_url}/login")
            if response.status_code == 200:
                self.log_result("Authentication", "صفحة تسجيل الدخول", "success")
            else:
                self.log_result("Authentication", "صفحة تسجيل الدخول", "error", f"كود {response.status_code}")
                return False

            # تسجيل الدخول
            login_data = {'username': 'admin', 'password': 'admin123'}
            response = self.session.post(f"{self.base_url}/login", data=login_data, allow_redirects=False)

            if response.status_code == 302:
                self.log_result("Authentication", "تسجيل الدخول", "success")
                return True
            else:
                self.log_result("Authentication", "تسجيل الدخول", "error", f"كود {response.status_code}")
                return False

        except Exception as e:
            self.log_result("Authentication", "تسجيل الدخول", "error", str(e))
            return False

    def test_dashboard(self):
        """فحص لوحة التحكم"""
        print("\n🏠 فحص لوحة التحكم...")

        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                content = response.text

                # فحص العناصر المهمة
                checks = [
                    ("الصفحة الرئيسية", "لوحة التحكم" in content or "Dashboard" in content),
                    ("إحصائيات المنتجات", "المنتجات" in content),
                    ("إحصائيات العملاء", "العملاء" in content),
                    ("إحصائيات المبيعات", "المبيعات" in content),
                    ("القائمة الجانبية", "sidebar" in content.lower()),
                ]

                for check_name, condition in checks:
                    if condition:
                        self.log_result("Dashboard", check_name, "success")
                    else:
                        self.log_result("Dashboard", check_name, "warning", "العنصر غير موجود")
            else:
                self.log_result("Dashboard", "تحميل الصفحة", "error", f"كود {response.status_code}")

        except Exception as e:
            self.log_result("Dashboard", "تحميل الصفحة", "error", str(e))

    def test_products(self):
        """فحص إدارة المنتجات"""
        print("\n📦 فحص إدارة المنتجات...")

        # فحص صفحة المنتجات
        try:
            response = self.session.get(f"{self.base_url}/products")
            if response.status_code == 200:
                self.log_result("Products", "صفحة المنتجات", "success")
            else:
                self.log_result("Products", "صفحة المنتجات", "error", f"كود {response.status_code}")
        except Exception as e:
            self.log_result("Products", "صفحة المنتجات", "error", str(e))

        # فحص صفحة إضافة منتج
        try:
            response = self.session.get(f"{self.base_url}/products/add")
            if response.status_code == 200:
                self.log_result("Products", "صفحة إضافة منتج", "success")
            else:
                self.log_result("Products", "صفحة إضافة منتج", "error", f"كود {response.status_code}")
        except Exception as e:
            self.log_result("Products", "صفحة إضافة منتج", "error", str(e))

        # فحص API البحث في المنتجات
        try:
            response = self.session.get(f"{self.base_url}/api/products/search?q=")
            if response.status_code == 200:
                products = response.json()
                self.log_result("Products", "API البحث", "success", f"وجد {len(products)} منتج")
            else:
                self.log_result("Products", "API البحث", "error", f"كود {response.status_code}")
        except Exception as e:
            self.log_result("Products", "API البحث", "error", str(e))

    def test_customers(self):
        """فحص إدارة العملاء"""
        print("\n👥 فحص إدارة العملاء...")

        # فحص صفحة العملاء
        try:
            response = self.session.get(f"{self.base_url}/customers")
            if response.status_code == 200:
                self.log_result("Customers", "صفحة العملاء", "success")
            else:
                self.log_result("Customers", "صفحة العملاء", "error", f"كود {response.status_code}")
        except Exception as e:
            self.log_result("Customers", "صفحة العملاء", "error", str(e))

        # فحص صفحة إضافة عميل
        try:
            response = self.session.get(f"{self.base_url}/customers/add")
            if response.status_code == 200:
                self.log_result("Customers", "صفحة إضافة عميل", "success")
            else:
                self.log_result("Customers", "صفحة إضافة عميل", "error", f"كود {response.status_code}")
        except Exception as e:
            self.log_result("Customers", "صفحة إضافة عميل", "error", str(e))

        # فحص API العملاء
        try:
            response = self.session.get(f"{self.base_url}/api/customers/search?q=")
            if response.status_code == 200:
                customers = response.json()
                self.log_result("Customers", "API البحث", "success", f"وجد {len(customers)} عميل")

                # اختبار إضافة عميل
                test_customer = {
                    'name': f'عميل اختبار {datetime.now().strftime("%H%M%S")}',
                    'phone': '0501234567',
                    'email': f'test_{datetime.now().strftime("%H%M%S")}@example.com'
                }

                add_response = self.session.post(f"{self.base_url}/api/customers",
                                               json=test_customer,
                                               headers={'Content-Type': 'application/json'})

                if add_response.status_code == 200:
                    result = add_response.json()
                    if result.get('success'):
                        self.log_result("Customers", "إضافة عميل", "success")

                        # اختبار حذف العميل المضاف
                        customer_id = result.get('customer_id')
                        if customer_id:
                            delete_response = self.session.post(f"{self.base_url}/customers/{customer_id}/delete")
                            if delete_response.status_code == 200:
                                delete_result = delete_response.json()
                                if delete_result.get('success'):
                                    self.log_result("Customers", "حذف العميل", "success")
                                else:
                                    self.log_result("Customers", "حذف العميل", "warning", delete_result.get('error'))
                            else:
                                self.log_result("Customers", "حذف العميل", "error", f"كود {delete_response.status_code}")
                    else:
                        self.log_result("Customers", "إضافة عميل", "error", result.get('error'))
                else:
                    self.log_result("Customers", "إضافة عميل", "error", f"كود {add_response.status_code}")

            else:
                self.log_result("Customers", "API البحث", "error", f"كود {response.status_code}")
        except Exception as e:
            self.log_result("Customers", "API البحث", "error", str(e))

    def test_sales(self):
        """فحص إدارة المبيعات"""
        print("\n💰 فحص إدارة المبيعات...")

        # فحص صفحة المبيعات
        try:
            response = self.session.get(f"{self.base_url}/sales")
            if response.status_code == 200:
                self.log_result("Sales", "صفحة المبيعات", "success")
            else:
                self.log_result("Sales", "صفحة المبيعات", "error", f"كود {response.status_code}")
        except Exception as e:
            self.log_result("Sales", "صفحة المبيعات", "error", str(e))

        # فحص صفحة فاتورة جديدة
        try:
            response = self.session.get(f"{self.base_url}/sales/new")
            if response.status_code == 200:
                self.log_result("Sales", "صفحة فاتورة جديدة", "success")
            else:
                self.log_result("Sales", "صفحة فاتورة جديدة", "error", f"كود {response.status_code}")
        except Exception as e:
            self.log_result("Sales", "صفحة فاتورة جديدة", "error", str(e))

    def test_users(self):
        """فحص إدارة المستخدمين"""
        print("\n👤 فحص إدارة المستخدمين...")

        # فحص صفحة المستخدمين
        try:
            response = self.session.get(f"{self.base_url}/users")
            if response.status_code == 200:
                self.log_result("Users", "صفحة المستخدمين", "success")
            else:
                self.log_result("Users", "صفحة المستخدمين", "error", f"كود {response.status_code}")
        except Exception as e:
            self.log_result("Users", "صفحة المستخدمين", "error", str(e))

        # فحص صفحة إضافة مستخدم
        try:
            response = self.session.get(f"{self.base_url}/users/add")
            if response.status_code == 200:
                self.log_result("Users", "صفحة إضافة مستخدم", "success")
            else:
                self.log_result("Users", "صفحة إضافة مستخدم", "error", f"كود {response.status_code}")
        except Exception as e:
            self.log_result("Users", "صفحة إضافة مستخدم", "error", str(e))

    def test_categories(self):
        """فحص إدارة التصنيفات"""
        print("\n📂 فحص إدارة التصنيفات...")

        try:
            response = self.session.get(f"{self.base_url}/categories")
            if response.status_code == 200:
                self.log_result("Categories", "صفحة التصنيفات", "success")
            else:
                self.log_result("Categories", "صفحة التصنيفات", "error", f"كود {response.status_code}")
        except Exception as e:
            self.log_result("Categories", "صفحة التصنيفات", "error", str(e))

    def test_reports(self):
        """فحص التقارير"""
        print("\n📊 فحص التقارير...")

        # فحص صفحة التقارير
        try:
            response = self.session.get(f"{self.base_url}/reports")
            if response.status_code == 200:
                self.log_result("Reports", "صفحة التقارير", "success")
            else:
                self.log_result("Reports", "صفحة التقارير", "error", f"كود {response.status_code}")
        except Exception as e:
            self.log_result("Reports", "صفحة التقارير", "error", str(e))

        # فحص API التقارير
        try:
            response = self.session.get(f"{self.base_url}/api/reports/sales")
            if response.status_code == 200:
                report = response.json()
                self.log_result("Reports", "API تقرير المبيعات", "success", f"إجمالي: {report.get('total_sales', 0)}")
            else:
                self.log_result("Reports", "API تقرير المبيعات", "error", f"كود {response.status_code}")
        except Exception as e:
            self.log_result("Reports", "API تقرير المبيعات", "error", str(e))

    def test_static_files(self):
        """فحص الملفات الثابتة"""
        print("\n📁 فحص الملفات الثابتة...")

        static_files = [
            ('/static/css/style.css', 'ملف CSS'),
            ('/static/js/app.js', 'ملف JavaScript'),
            ('/static/manifest.json', 'PWA Manifest'),
            ('/static/sw.js', 'Service Worker'),
            ('/static/images/icon-144x144.png', 'أيقونة PWA'),
        ]

        for url, name in static_files:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                if response.status_code in [200, 304]:
                    self.log_result("Static Files", name, "success")
                else:
                    self.log_result("Static Files", name, "error", f"كود {response.status_code}")
            except Exception as e:
                self.log_result("Static Files", name, "error", str(e))

    def test_performance(self):
        """فحص الأداء"""
        print("\n⚡ فحص الأداء...")

        # قياس وقت الاستجابة
        start_time = time.time()
        try:
            response = self.session.get(f"{self.base_url}/")
            end_time = time.time()
            response_time = end_time - start_time

            if response_time < 1.0:
                self.log_result("Performance", "وقت الاستجابة", "success", f"{response_time:.3f} ثانية")
            elif response_time < 3.0:
                self.log_result("Performance", "وقت الاستجابة", "warning", f"{response_time:.3f} ثانية")
            else:
                self.log_result("Performance", "وقت الاستجابة", "error", f"{response_time:.3f} ثانية")

        except Exception as e:
            self.log_result("Performance", "وقت الاستجابة", "error", str(e))

    def run_comprehensive_test(self):
        """تشغيل الفحص الشامل"""
        print("🔍 بدء الفحص الشامل لجميع خيارات التطبيق")
        print("=" * 70)
        print(f"🕐 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)

        # تسجيل الدخول
        if not self.login():
            print("❌ فشل تسجيل الدخول - لا يمكن متابعة الفحص")
            return False

        # تشغيل جميع الفحوصات
        tests = [
            self.test_dashboard,
            self.test_products,
            self.test_customers,
            self.test_sales,
            self.test_users,
            self.test_categories,
            self.test_reports,
            self.test_static_files,
            self.test_performance
        ]

        for test in tests:
            try:
                test()
            except Exception as e:
                print(f"💥 خطأ في تشغيل الفحص: {e}")

        # عرض النتائج النهائية
        self.show_comprehensive_results()

    def show_comprehensive_results(self):
        """عرض النتائج الشاملة"""
        print("\n" + "=" * 70)
        print("📊 نتائج الفحص الشامل لجميع خيارات التطبيق")
        print("=" * 70)

        # إحصائيات عامة
        total_tests = len(self.successes) + len(self.warnings) + len(self.errors)
        success_rate = (len(self.successes) / total_tests * 100) if total_tests > 0 else 0

        print(f"\n📈 الإحصائيات العامة:")
        print(f"  ✅ نجح: {len(self.successes)}")
        print(f"  ⚠️  تحذيرات: {len(self.warnings)}")
        print(f"  ❌ أخطاء: {len(self.errors)}")
        print(f"  📊 معدل النجاح: {success_rate:.1f}%")

        # تفاصيل حسب الفئة
        print(f"\n📋 تفاصيل النتائج حسب الفئة:")
        for category, tests in self.results.items():
            success_count = len([t for t in tests.values() if t['status'] == 'success'])
            total_count = len(tests)
            category_rate = (success_count / total_count * 100) if total_count > 0 else 0

            print(f"\n🔸 {category}:")
            print(f"   معدل النجاح: {category_rate:.1f}% ({success_count}/{total_count})")

            for test_name, result in tests.items():
                status_icon = {
                    'success': '✅',
                    'warning': '⚠️',
                    'error': '❌'
                }.get(result['status'], '❓')

                details = f" - {result['details']}" if result['details'] else ""
                print(f"   {status_icon} {test_name}{details}")

        # الأخطاء المهمة
        if self.errors:
            print(f"\n❌ الأخطاء التي تحتاج إصلاح:")
            for error in self.errors:
                print(f"  🔴 {error}")

        # التحذيرات
        if self.warnings:
            print(f"\n⚠️  التحذيرات:")
            for warning in self.warnings:
                print(f"  🟡 {warning}")

        # التقييم النهائي
        print(f"\n🎯 التقييم النهائي:")
        if success_rate >= 95:
            print("🎉 ممتاز! جميع خيارات التطبيق تعمل بشكل مثالي!")
        elif success_rate >= 85:
            print("✅ جيد جداً! معظم خيارات التطبيق تعمل بشكل صحيح")
        elif success_rate >= 70:
            print("👍 جيد! التطبيق يعمل مع بعض التحسينات المطلوبة")
        else:
            print("⚠️  يحتاج تحسين! هناك مشاكل تحتاج إصلاح")

        return success_rate >= 85

def main():
    """الوظيفة الرئيسية"""
    tester = FullAppTester()
    success = tester.run_comprehensive_test()
    return success

if __name__ == "__main__":
    main()
