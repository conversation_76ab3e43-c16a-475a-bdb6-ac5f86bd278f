/**
 * وظائف العملة الليبية
 * Libyan Currency Functions
 */

// إعدادات العملة الليبية
const LIBYA_CURRENCY = {
    code: 'LYD',
    symbol: 'د.ل',
    name: 'دين<PERSON><PERSON> ليبي',
    name_en: 'Libyan Dinar',
    decimals: 3,
    thousands_separator: ',',
    decimal_separator: '.'
};

/**
 * تنسيق المبلغ بالدينار الليبي
 * @param {number} amount - المبلغ
 * @param {boolean} showSymbol - إظهار رمز العملة
 * @returns {string} المبلغ منسق
 */
function formatLYD(amount, showSymbol = true) {
    if (amount === null || amount === undefined || isNaN(amount)) {
        amount = 0;
    }
    
    // تنسيق الرقم مع الفواصل
    const formatted = parseFloat(amount).toLocaleString('ar-LY', {
        minimumFractionDigits: LIBYA_CURRENCY.decimals,
        maximumFractionDigits: LIBYA_CURRENCY.decimals
    });
    
    return showSymbol ? `${formatted} ${LIBYA_CURRENCY.symbol}` : formatted;
}

/**
 * تحويل النص إلى رقم
 * @param {string} text - النص
 * @returns {number} الرقم
 */
function parseLYD(text) {
    if (!text) return 0;
    
    // إزالة رمز العملة والمسافات
    const cleaned = text.toString()
        .replace(LIBYA_CURRENCY.symbol, '')
        .replace(/\s/g, '')
        .replace(/,/g, '');
    
    return parseFloat(cleaned) || 0;
}

/**
 * إضافة علم ليبيا إلى العنصر
 * @param {HTMLElement} element - العنصر
 */
function addLibyaFlag(element) {
    if (!element) return;
    
    const flag = document.createElement('span');
    flag.className = 'libya-flag';
    flag.title = 'ليبيا';
    
    element.appendChild(flag);
}

/**
 * تحديث جميع عناصر العملة في الصفحة
 */
function updateCurrencyElements() {
    // تحديث عناصر العملة
    document.querySelectorAll('.currency-lyd').forEach(element => {
        const amount = parseLYD(element.textContent);
        element.textContent = formatLYD(amount, false);
    });
    
    // إضافة أعلام ليبيا
    document.querySelectorAll('.price-display:not(.flag-added)').forEach(element => {
        if (!element.querySelector('.libya-flag')) {
            addLibyaFlag(element);
            element.classList.add('flag-added');
        }
    });
}

/**
 * تحديث حقول الإدخال لتنسيق العملة
 */
function setupCurrencyInputs() {
    document.querySelectorAll('input[type="number"]').forEach(input => {
        // إذا كان الحقل متعلق بالسعر أو المبلغ
        if (input.name && (
            input.name.includes('price') || 
            input.name.includes('amount') || 
            input.name.includes('credit') ||
            input.name.includes('cost')
        )) {
            // إضافة مستمع للتغيير
            input.addEventListener('blur', function() {
                const value = parseFloat(this.value);
                if (!isNaN(value)) {
                    this.value = value.toFixed(LIBYA_CURRENCY.decimals);
                }
            });
            
            // إضافة تلميح
            if (!input.title) {
                input.title = `أدخل المبلغ بالدينار الليبي (${LIBYA_CURRENCY.symbol})`;
            }
        }
    });
}

/**
 * حساب هامش الربح
 * @param {number} purchasePrice - سعر الشراء
 * @param {number} sellingPrice - سعر البيع
 * @returns {number} هامش الربح بالنسبة المئوية
 */
function calculateProfitMargin(purchasePrice, sellingPrice) {
    if (!purchasePrice || purchasePrice <= 0) return 0;
    
    const profit = sellingPrice - purchasePrice;
    return (profit / purchasePrice) * 100;
}

/**
 * تحديث عرض هامش الربح
 */
function updateProfitMargin() {
    const purchasePriceInput = document.getElementById('purchase_price');
    const sellingPriceInput = document.getElementById('selling_price');
    const profitMarginSpan = document.getElementById('profit_margin');
    
    if (purchasePriceInput && sellingPriceInput && profitMarginSpan) {
        const updateMargin = () => {
            const purchasePrice = parseFloat(purchasePriceInput.value) || 0;
            const sellingPrice = parseFloat(sellingPriceInput.value) || 0;
            
            const margin = calculateProfitMargin(purchasePrice, sellingPrice);
            profitMarginSpan.textContent = `${margin.toFixed(1)}%`;
            
            // تلوين هامش الربح
            if (margin > 20) {
                profitMarginSpan.className = 'text-success fw-bold';
            } else if (margin > 10) {
                profitMarginSpan.className = 'text-warning fw-bold';
            } else if (margin > 0) {
                profitMarginSpan.className = 'text-info';
            } else {
                profitMarginSpan.className = 'text-danger fw-bold';
            }
        };
        
        purchasePriceInput.addEventListener('input', updateMargin);
        sellingPriceInput.addEventListener('input', updateMargin);
        
        // تحديث أولي
        updateMargin();
    }
}

/**
 * تحويل العملة من الريال السعودي إلى الدينار الليبي
 * @param {number} sarAmount - المبلغ بالريال السعودي
 * @returns {number} المبلغ بالدينار الليبي
 */
function convertSARtoLYD(sarAmount) {
    // سعر الصرف التقريبي (يمكن تحديثه)
    const exchangeRate = 0.82; // 1 ريال سعودي = 0.82 دينار ليبي (تقريبي)
    return sarAmount * exchangeRate;
}

/**
 * إضافة معلومات العملة إلى الصفحة
 */
function addCurrencyInfo() {
    // إضافة معلومات في الفوتر أو مكان مناسب
    const currencyInfo = document.createElement('div');
    currencyInfo.className = 'currency-info text-muted small mt-3';
    currencyInfo.innerHTML = `
        <i class="fas fa-info-circle me-1"></i>
        جميع الأسعار بالدينار الليبي (${LIBYA_CURRENCY.symbol})
        <span class="libya-flag ms-1"></span>
    `;
    
    // البحث عن مكان مناسب لإضافة المعلومات
    const container = document.querySelector('.container-fluid') || document.querySelector('.container');
    if (container) {
        container.appendChild(currencyInfo);
    }
}

/**
 * تهيئة وظائف العملة عند تحميل الصفحة
 */
document.addEventListener('DOMContentLoaded', function() {
    // تحديث عناصر العملة
    updateCurrencyElements();
    
    // إعداد حقول الإدخال
    setupCurrencyInputs();
    
    // تحديث هامش الربح
    updateProfitMargin();
    
    // إضافة معلومات العملة
    addCurrencyInfo();
    
    console.log('✅ تم تحميل وظائف العملة الليبية');
});

// تصدير الوظائف للاستخدام العام
window.LibyaCurrency = {
    format: formatLYD,
    parse: parseLYD,
    addFlag: addLibyaFlag,
    update: updateCurrencyElements,
    calculateProfit: calculateProfitMargin,
    convert: convertSARtoLYD,
    currency: LIBYA_CURRENCY
};
