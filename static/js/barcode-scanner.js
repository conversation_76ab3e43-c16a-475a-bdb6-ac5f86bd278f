/**
 * قارئ الباركود - Barcode Scanner
 * يدعم قراءة الباركود من الكاميرا أو رفع صورة
 */

class BarcodeScanner {
    constructor() {
        this.isScanning = false;
        this.stream = null;
        this.video = null;
        this.canvas = null;
        this.context = null;
        this.scanInterval = null;
        this.onBarcodeDetected = null;
        this.onError = null;
    }

    /**
     * تهيئة قارئ الباركود
     * @param {Object} options - خيارات التهيئة
     */
    async init(options = {}) {
        this.options = {
            video: options.video || 'barcode-video',
            canvas: options.canvas || 'barcode-canvas',
            width: options.width || 640,
            height: options.height || 480,
            facingMode: options.facingMode || 'environment', // الكاميرا الخلفية
            scanInterval: options.scanInterval || 500, // مللي ثانية
            ...options
        };

        // إنشاء عناصر HTML إذا لم تكن موجودة
        this.createElements();
        
        // تحميل مكتبة QuaggaJS لقراءة الباركود
        await this.loadQuaggaJS();
    }

    /**
     * إنشاء عناصر HTML المطلوبة
     */
    createElements() {
        // إنشاء عنصر الفيديو
        if (!document.getElementById(this.options.video)) {
            this.video = document.createElement('video');
            this.video.id = this.options.video;
            this.video.width = this.options.width;
            this.video.height = this.options.height;
            this.video.autoplay = true;
            this.video.muted = true;
            this.video.playsInline = true;
            this.video.style.display = 'none';
        } else {
            this.video = document.getElementById(this.options.video);
        }

        // إنشاء عنصر الكانفاس
        if (!document.getElementById(this.options.canvas)) {
            this.canvas = document.createElement('canvas');
            this.canvas.id = this.options.canvas;
            this.canvas.width = this.options.width;
            this.canvas.height = this.options.height;
            this.canvas.style.display = 'none';
        } else {
            this.canvas = document.getElementById(this.options.canvas);
        }

        this.context = this.canvas.getContext('2d');
    }

    /**
     * تحميل مكتبة QuaggaJS
     */
    async loadQuaggaJS() {
        return new Promise((resolve, reject) => {
            if (window.Quagga) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = 'https://cdnjs.cloudflare.com/ajax/libs/quagga/0.12.1/quagga.min.js';
            script.onload = () => resolve();
            script.onerror = () => reject(new Error('فشل في تحميل مكتبة قراءة الباركود'));
            document.head.appendChild(script);
        });
    }

    /**
     * بدء مسح الباركود من الكاميرا
     */
    async startCamera() {
        try {
            // طلب إذن الوصول للكاميرا
            this.stream = await navigator.mediaDevices.getUserMedia({
                video: {
                    facingMode: this.options.facingMode,
                    width: { ideal: this.options.width },
                    height: { ideal: this.options.height }
                }
            });

            this.video.srcObject = this.stream;
            this.video.style.display = 'block';
            
            // انتظار تحميل الفيديو
            await new Promise((resolve) => {
                this.video.onloadedmetadata = resolve;
            });

            this.isScanning = true;
            this.startScanning();

            return true;
        } catch (error) {
            console.error('خطأ في الوصول للكاميرا:', error);
            if (this.onError) {
                this.onError('فشل في الوصول للكاميرا: ' + error.message);
            }
            return false;
        }
    }

    /**
     * إيقاف مسح الباركود
     */
    stopCamera() {
        this.isScanning = false;
        
        if (this.scanInterval) {
            clearInterval(this.scanInterval);
            this.scanInterval = null;
        }

        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }

        if (this.video) {
            this.video.style.display = 'none';
            this.video.srcObject = null;
        }
    }

    /**
     * بدء عملية المسح
     */
    startScanning() {
        if (!this.isScanning) return;

        this.scanInterval = setInterval(() => {
            if (this.video.readyState === this.video.HAVE_ENOUGH_DATA) {
                this.captureAndScan();
            }
        }, this.options.scanInterval);
    }

    /**
     * التقاط صورة ومسحها
     */
    captureAndScan() {
        // رسم الفيديو على الكانفاس
        this.context.drawImage(this.video, 0, 0, this.canvas.width, this.canvas.height);
        
        // الحصول على بيانات الصورة
        const imageData = this.context.getImageData(0, 0, this.canvas.width, this.canvas.height);
        
        // مسح الباركود
        this.scanImageData(imageData);
    }

    /**
     * مسح بيانات الصورة للبحث عن باركود
     */
    scanImageData(imageData) {
        // استخدام QuaggaJS لقراءة الباركود
        if (window.Quagga) {
            Quagga.decodeSingle({
                decoder: {
                    readers: [
                        "code_128_reader",
                        "ean_reader",
                        "ean_8_reader",
                        "code_39_reader",
                        "code_39_vin_reader",
                        "codabar_reader",
                        "upc_reader",
                        "upc_e_reader",
                        "i2of5_reader"
                    ]
                },
                locate: true,
                src: this.canvas
            }, (result) => {
                if (result && result.codeResult) {
                    const barcode = result.codeResult.code;
                    if (this.onBarcodeDetected) {
                        this.onBarcodeDetected(barcode);
                    }
                }
            });
        }
    }

    /**
     * مسح صورة مرفوعة
     */
    async scanImage(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = (e) => {
                const img = new Image();
                img.onload = () => {
                    // رسم الصورة على الكانفاس
                    this.canvas.width = img.width;
                    this.canvas.height = img.height;
                    this.context.drawImage(img, 0, 0);

                    // مسح الباركود
                    if (window.Quagga) {
                        Quagga.decodeSingle({
                            decoder: {
                                readers: [
                                    "code_128_reader",
                                    "ean_reader",
                                    "ean_8_reader",
                                    "code_39_reader",
                                    "code_39_vin_reader",
                                    "codabar_reader",
                                    "upc_reader",
                                    "upc_e_reader",
                                    "i2of5_reader"
                                ]
                            },
                            locate: true,
                            src: this.canvas
                        }, (result) => {
                            if (result && result.codeResult) {
                                resolve(result.codeResult.code);
                            } else {
                                reject(new Error('لم يتم العثور على باركود في الصورة'));
                            }
                        });
                    } else {
                        reject(new Error('مكتبة قراءة الباركود غير متاحة'));
                    }
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        });
    }

    /**
     * تبديل الكاميرا (أمامية/خلفية)
     */
    async switchCamera() {
        const currentFacingMode = this.options.facingMode;
        this.options.facingMode = currentFacingMode === 'environment' ? 'user' : 'environment';
        
        this.stopCamera();
        await this.startCamera();
    }

    /**
     * تعيين دالة استدعاء عند اكتشاف باركود
     */
    onBarcode(callback) {
        this.onBarcodeDetected = callback;
    }

    /**
     * تعيين دالة استدعاء عند حدوث خطأ
     */
    onErrorCallback(callback) {
        this.onError = callback;
    }

    /**
     * التحقق من دعم الكاميرا
     */
    static isCameraSupported() {
        return !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia);
    }

    /**
     * إنشاء نافذة منبثقة لمسح الباركود
     */
    static createScannerModal() {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.id = 'barcodeScannerModal';
        modal.innerHTML = `
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-camera me-2"></i>مسح الباركود
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body text-center">
                        <div class="scanner-container position-relative">
                            <video id="barcode-video" class="img-fluid rounded"></video>
                            <canvas id="barcode-canvas" style="display: none;"></canvas>
                            
                            <!-- إطار المسح -->
                            <div class="scan-frame"></div>
                            
                            <!-- أزرار التحكم -->
                            <div class="scanner-controls mt-3">
                                <button class="btn btn-primary" id="startScanBtn">
                                    <i class="fas fa-play me-2"></i>بدء المسح
                                </button>
                                <button class="btn btn-secondary" id="stopScanBtn" style="display: none;">
                                    <i class="fas fa-stop me-2"></i>إيقاف المسح
                                </button>
                                <button class="btn btn-outline-primary" id="switchCameraBtn" style="display: none;">
                                    <i class="fas fa-sync-alt me-2"></i>تبديل الكاميرا
                                </button>
                            </div>
                            
                            <!-- رفع صورة -->
                            <div class="mt-3">
                                <label for="barcodeImageInput" class="btn btn-outline-success">
                                    <i class="fas fa-upload me-2"></i>رفع صورة
                                </label>
                                <input type="file" id="barcodeImageInput" accept="image/*" style="display: none;">
                            </div>
                            
                            <!-- النتيجة -->
                            <div id="scanResult" class="mt-3" style="display: none;">
                                <div class="alert alert-success">
                                    <strong>تم اكتشاف باركود:</strong>
                                    <code id="detectedBarcode"></code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة أنماط CSS
        const style = document.createElement('style');
        style.textContent = `
            .scan-frame {
                position: absolute;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 250px;
                height: 150px;
                border: 2px solid #007bff;
                border-radius: 10px;
                pointer-events: none;
            }
            
            .scan-frame::before {
                content: '';
                position: absolute;
                top: -2px;
                left: -2px;
                right: -2px;
                bottom: -2px;
                border: 2px solid rgba(0, 123, 255, 0.3);
                border-radius: 10px;
                animation: scanPulse 2s infinite;
            }
            
            @keyframes scanPulse {
                0%, 100% { opacity: 0.3; }
                50% { opacity: 1; }
            }
        `;
        document.head.appendChild(style);

        return modal;
    }
}

// تصدير الفئة للاستخدام العام
window.BarcodeScanner = BarcodeScanner;
