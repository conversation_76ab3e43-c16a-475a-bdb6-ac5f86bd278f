// تطبيق إدارة المبيعات والمخازن - JavaScript الرئيسي

class SalesApp {
    constructor() {
        this.init();
        this.registerServiceWorker();
        this.setupOfflineDetection();
        this.setupPWAInstall();
    }

    init() {
        // تهيئة التطبيق
        this.setupEventListeners();
        this.setupFormValidation();
        this.setupSearchFunctionality();
    }

    // تسجيل Service Worker للعمل أوفلاين
    registerServiceWorker() {
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/static/sw.js')
                .then(registration => {
                    console.log('Service Worker registered successfully:', registration);
                })
                .catch(error => {
                    console.log('Service Worker registration failed:', error);
                });
        }
    }

    // إعداد كشف حالة الاتصال
    setupOfflineDetection() {
        const offlineIndicator = document.getElementById('offline-indicator');

        const updateOnlineStatus = () => {
            if (navigator.onLine) {
                offlineIndicator.classList.add('d-none');
            } else {
                offlineIndicator.classList.remove('d-none');
            }
        };

        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', updateOnlineStatus);
        updateOnlineStatus();
    }

    // إعداد تثبيت PWA
    setupPWAInstall() {
        let deferredPrompt;

        window.addEventListener('beforeinstallprompt', (e) => {
            e.preventDefault();
            deferredPrompt = e;
            this.showInstallPrompt();
        });

        window.addEventListener('appinstalled', () => {
            console.log('PWA was installed');
            this.hideInstallPrompt();
        });
    }

    showInstallPrompt() {
        const installPrompt = document.createElement('div');
        installPrompt.className = 'pwa-install-prompt';
        installPrompt.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-download me-2"></i>
                <span class="me-3">تثبيت التطبيق على جهازك</span>
                <button class="btn btn-light btn-sm me-2" onclick="app.installPWA()">تثبيت</button>
                <button class="btn btn-outline-light btn-sm" onclick="app.hideInstallPrompt()">&times;</button>
            </div>
        `;
        installPrompt.style.display = 'block';
        document.body.appendChild(installPrompt);
    }

    hideInstallPrompt() {
        const prompt = document.querySelector('.pwa-install-prompt');
        if (prompt) {
            prompt.remove();
        }
    }

    async installPWA() {
        const prompt = document.querySelector('.pwa-install-prompt');
        if (deferredPrompt && prompt) {
            deferredPrompt.prompt();
            const { outcome } = await deferredPrompt.userChoice;
            console.log(`User response to the install prompt: ${outcome}`);
            deferredPrompt = null;
            this.hideInstallPrompt();
        }
    }

    // إعداد مستمعي الأحداث
    setupEventListeners() {
        // تأكيد الحذف
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('delete-btn')) {
                e.preventDefault();
                const message = e.target.dataset.message || 'هل أنت متأكد من الحذف؟';
                if (confirm(message)) {
                    window.location.href = e.target.href;
                }
            }
        });

        // إغلاق التنبيهات تلقائياً
        setTimeout(() => {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                if (alert.classList.contains('alert-success')) {
                    alert.style.transition = 'opacity 0.5s';
                    alert.style.opacity = '0';
                    setTimeout(() => alert.remove(), 500);
                }
            });
        }, 3000);
    }

    // إعداد التحقق من النماذج
    setupFormValidation() {
        const forms = document.querySelectorAll('.needs-validation');
        forms.forEach(form => {
            form.addEventListener('submit', (e) => {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    }

    // إعداد وظائف البحث
    setupSearchFunctionality() {
        const searchInputs = document.querySelectorAll('.search-input');
        searchInputs.forEach(input => {
            let timeout;
            input.addEventListener('input', (e) => {
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    this.performSearch(e.target.value, e.target.dataset.searchType);
                }, 300);
            });
        });
    }

    // تنفيذ البحث
    async performSearch(query, type) {
        if (!query || query.length < 2) return;

        try {
            const response = await fetch(`/api/${type}/search?q=${encodeURIComponent(query)}`);
            const results = await response.json();
            this.displaySearchResults(results, type);
        } catch (error) {
            console.error('Search error:', error);
        }
    }

    // عرض نتائج البحث
    displaySearchResults(results, type) {
        const resultsContainer = document.getElementById(`${type}-results`);
        if (!resultsContainer) return;

        resultsContainer.innerHTML = '';
        results.forEach(item => {
            const resultElement = this.createResultElement(item, type);
            resultsContainer.appendChild(resultElement);
        });
    }

    // إنشاء عنصر نتيجة البحث
    createResultElement(item, type) {
        const div = document.createElement('div');
        div.className = 'list-group-item list-group-item-action';

        if (type === 'products') {
            div.innerHTML = `
                <div class="d-flex w-100 justify-content-between">
                    <h6 class="mb-1">${item.name}</h6>
                    <small class="text-success">${item.price} د.ل <span class="libya-flag"></span></small>
                </div>
                <p class="mb-1">الكمية: ${item.quantity} ${item.unit}</p>
                <small>الباركود: ${item.barcode || 'غير محدد'}</small>
            `;
            div.addEventListener('click', () => this.selectProduct(item));
        }

        return div;
    }

    // اختيار منتج
    selectProduct(product) {
        // إضافة المنتج إلى الفاتورة أو النموذج
        console.log('Selected product:', product);
    }

    // حفظ البيانات محلياً
    saveToLocalStorage(key, data) {
        try {
            localStorage.setItem(key, JSON.stringify(data));
        } catch (error) {
            console.error('Error saving to localStorage:', error);
        }
    }

    // استرجاع البيانات من التخزين المحلي
    getFromLocalStorage(key) {
        try {
            const data = localStorage.getItem(key);
            return data ? JSON.parse(data) : null;
        } catch (error) {
            console.error('Error reading from localStorage:', error);
            return null;
        }
    }

    // مزامنة البيانات عند الاتصال
    async syncData() {
        if (!navigator.onLine) return;

        const pendingData = this.getFromLocalStorage('pendingSync');
        if (!pendingData || pendingData.length === 0) return;

        try {
            for (const item of pendingData) {
                await this.syncItem(item);
            }
            localStorage.removeItem('pendingSync');
            this.showNotification('تم مزامنة البيانات بنجاح', 'success');
        } catch (error) {
            console.error('Sync error:', error);
            this.showNotification('فشل في مزامنة البيانات', 'error');
        }
    }

    // مزامنة عنصر واحد
    async syncItem(item) {
        const response = await fetch(item.url, {
            method: item.method,
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(item.data)
        });

        if (!response.ok) {
            throw new Error(`Sync failed for ${item.type}`);
        }
    }

    // عرض إشعار
    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 5000);
    }

    // طباعة الفاتورة
    printInvoice(invoiceId) {
        const printWindow = window.open(`/invoices/${invoiceId}/print`, '_blank');
        printWindow.onload = () => {
            printWindow.print();
        };
    }

    // تصدير البيانات
    async exportData(type, format = 'excel') {
        try {
            const response = await fetch(`/api/export/${type}?format=${format}`);
            const blob = await response.blob();

            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${type}_${new Date().toISOString().split('T')[0]}.${format === 'excel' ? 'xlsx' : 'pdf'}`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
        } catch (error) {
            console.error('Export error:', error);
            this.showNotification('فشل في تصدير البيانات', 'error');
        }
    }
}

// تهيئة التطبيق عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
    window.app = new SalesApp();
});

// مزامنة البيانات عند الاتصال
window.addEventListener('online', () => {
    if (window.app) {
        window.app.syncData();
    }
});
