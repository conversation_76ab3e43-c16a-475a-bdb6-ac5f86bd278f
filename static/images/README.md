# أيقونات التطبيق

هذا المجلد يحتوي على أيقونات التطبيق المطلوبة للـ PWA.

## الأيقونات المطلوبة:

### أيقونات التطبيق الرئيسية:
- `icon-72x72.png` - أيقونة 72x72 بكسل
- `icon-96x96.png` - أيقونة 96x96 بكسل  
- `icon-128x128.png` - أيقونة 128x128 بكسل
- `icon-144x144.png` - أيقونة 144x144 بكسل
- `icon-152x152.png` - أيقونة 152x152 بكسل
- `icon-192x192.png` - أيقونة 192x192 بكسل
- `icon-384x384.png` - أيقونة 384x384 بكسل
- `icon-512x512.png` - أيقونة 512x512 بكسل

### أيقونات الاختصارات:
- `shortcut-invoice.png` - أيقونة اختصار الفواتير (96x96)
- `shortcut-product.png` - أيقونة اختصار المنتجات (96x96)
- `shortcut-reports.png` - أيقونة اختصار التقارير (96x96)

### أيقونات إضافية:
- `badge-72x72.png` - شارة الإشعارات
- `checkmark.png` - علامة صح للإشعارات
- `xmark.png` - علامة إغلاق للإشعارات

### لقطات الشاشة:
- `screenshot1.png` - لقطة شاشة عريضة (1280x720)
- `screenshot2.png` - لقطة شاشة ضيقة (750x1334)

## كيفية إنشاء الأيقونات:

1. **إنشاء التصميم الأساسي:**
   - استخدم أي برنامج تصميم (Photoshop, GIMP, Canva)
   - أنشئ تصميماً بحجم 512x512 بكسل
   - استخدم ألوان التطبيق (الأزرق #0d6efd)
   - أضف رمز متجر أو عربة تسوق

2. **تصدير الأحجام المختلفة:**
   - احفظ التصميم بصيغة PNG
   - قم بتغيير الحجم لكل مقاس مطلوب
   - تأكد من وضوح الأيقونة في الأحجام الصغيرة

3. **أدوات مساعدة:**
   - [PWA Icon Generator](https://www.pwabuilder.com/imageGenerator)
   - [Favicon Generator](https://favicon.io/)
   - [App Icon Generator](https://appicon.co/)

## ملاحظات:

- يجب أن تكون جميع الأيقونات بصيغة PNG
- يُفضل استخدام خلفية شفافة أو ملونة
- تأكد من أن الأيقونة واضحة في جميع الأحجام
- استخدم ألوان متناسقة مع تصميم التطبيق

## أيقونة مؤقتة:

يمكنك استخدام أيقونة Font Awesome كأيقونة مؤقتة:
```html
<i class="fas fa-store"></i>
```

أو إنشاء أيقونة بسيطة باستخدام CSS:
```css
.app-icon {
    width: 192px;
    height: 192px;
    background: linear-gradient(135deg, #0d6efd, #6610f2);
    border-radius: 20%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 80px;
}
```
