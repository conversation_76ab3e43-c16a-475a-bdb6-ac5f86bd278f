// Service Worker لتطبيق إدارة المبيعات والمخازن

const CACHE_NAME = 'sales-app-v1';
const urlsToCache = [
    '/',
    '/static/css/style.css',
    '/static/js/app.js',
    '/static/manifest.json',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css',
    'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js',
    'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
    'https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap'
];

// تثبيت Service Worker
self.addEventListener('install', (event) => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then((cache) => {
                console.log('Opened cache');
                return cache.addAll(urlsToCache);
            })
    );
});

// تفعيل Service Worker
self.addEventListener('activate', (event) => {
    event.waitUntil(
        caches.keys().then((cacheNames) => {
            return Promise.all(
                cacheNames.map((cacheName) => {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Deleting old cache:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});

// اعتراض الطلبات
self.addEventListener('fetch', (event) => {
    event.respondWith(
        caches.match(event.request)
            .then((response) => {
                // إرجاع الملف من الكاش إذا وُجد
                if (response) {
                    return response;
                }

                // إذا لم يوجد في الكاش، جلبه من الشبكة
                return fetch(event.request).then((response) => {
                    // التحقق من صحة الاستجابة
                    if (!response || response.status !== 200 || response.type !== 'basic') {
                        return response;
                    }

                    // نسخ الاستجابة
                    const responseToCache = response.clone();

                    caches.open(CACHE_NAME)
                        .then((cache) => {
                            cache.put(event.request, responseToCache);
                        });

                    return response;
                }).catch(() => {
                    // في حالة عدم وجود اتصال، إرجاع صفحة أوفلاين
                    if (event.request.destination === 'document') {
                        return caches.match('/offline.html');
                    }
                });
            })
    );
});

// مزامنة البيانات في الخلفية
self.addEventListener('sync', (event) => {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

async function doBackgroundSync() {
    try {
        // جلب البيانات المعلقة من IndexedDB
        const pendingData = await getPendingData();
        
        for (const item of pendingData) {
            await syncItem(item);
        }
        
        // حذف البيانات المعلقة بعد المزامنة
        await clearPendingData();
        
        // إرسال رسالة للتطبيق الرئيسي
        self.clients.matchAll().then(clients => {
            clients.forEach(client => {
                client.postMessage({
                    type: 'SYNC_COMPLETE',
                    message: 'تم مزامنة البيانات بنجاح'
                });
            });
        });
        
    } catch (error) {
        console.error('Background sync failed:', error);
    }
}

async function syncItem(item) {
    const response = await fetch(item.url, {
        method: item.method,
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(item.data)
    });

    if (!response.ok) {
        throw new Error(`Sync failed for ${item.type}`);
    }
}

// وظائف IndexedDB للتخزين المحلي
function openDB() {
    return new Promise((resolve, reject) => {
        const request = indexedDB.open('SalesAppDB', 1);
        
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
        
        request.onupgradeneeded = (event) => {
            const db = event.target.result;
            
            // إنشاء متجر للبيانات المعلقة
            if (!db.objectStoreNames.contains('pendingSync')) {
                const store = db.createObjectStore('pendingSync', { keyPath: 'id', autoIncrement: true });
                store.createIndex('timestamp', 'timestamp', { unique: false });
            }
            
            // إنشاء متجر للمنتجات
            if (!db.objectStoreNames.contains('products')) {
                const store = db.createObjectStore('products', { keyPath: 'id' });
                store.createIndex('name', 'name', { unique: false });
                store.createIndex('barcode', 'barcode', { unique: true });
            }
            
            // إنشاء متجر للعملاء
            if (!db.objectStoreNames.contains('customers')) {
                const store = db.createObjectStore('customers', { keyPath: 'id' });
                store.createIndex('name', 'name', { unique: false });
            }
            
            // إنشاء متجر للفواتير
            if (!db.objectStoreNames.contains('invoices')) {
                const store = db.createObjectStore('invoices', { keyPath: 'id' });
                store.createIndex('invoice_number', 'invoice_number', { unique: true });
                store.createIndex('created_at', 'created_at', { unique: false });
            }
        };
    });
}

async function getPendingData() {
    const db = await openDB();
    const transaction = db.transaction(['pendingSync'], 'readonly');
    const store = transaction.objectStore('pendingSync');
    
    return new Promise((resolve, reject) => {
        const request = store.getAll();
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
    });
}

async function clearPendingData() {
    const db = await openDB();
    const transaction = db.transaction(['pendingSync'], 'readwrite');
    const store = transaction.objectStore('pendingSync');
    
    return new Promise((resolve, reject) => {
        const request = store.clear();
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
    });
}

// حفظ البيانات للمزامنة لاحقاً
async function savePendingData(data) {
    const db = await openDB();
    const transaction = db.transaction(['pendingSync'], 'readwrite');
    const store = transaction.objectStore('pendingSync');
    
    const item = {
        ...data,
        timestamp: Date.now()
    };
    
    return new Promise((resolve, reject) => {
        const request = store.add(item);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result);
    });
}

// استقبال الرسائل من التطبيق الرئيسي
self.addEventListener('message', (event) => {
    if (event.data && event.data.type === 'SAVE_PENDING') {
        savePendingData(event.data.payload);
    }
});

// إشعارات Push (للمستقبل)
self.addEventListener('push', (event) => {
    const options = {
        body: event.data ? event.data.text() : 'إشعار جديد من نظام المبيعات',
        icon: '/static/images/icon-192x192.png',
        badge: '/static/images/badge-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: 1
        },
        actions: [
            {
                action: 'explore',
                title: 'عرض التفاصيل',
                icon: '/static/images/checkmark.png'
            },
            {
                action: 'close',
                title: 'إغلاق',
                icon: '/static/images/xmark.png'
            }
        ]
    };
    
    event.waitUntil(
        self.registration.showNotification('نظام إدارة المبيعات', options)
    );
});

// التعامل مع النقر على الإشعارات
self.addEventListener('notificationclick', (event) => {
    event.notification.close();
    
    if (event.action === 'explore') {
        event.waitUntil(
            clients.openWindow('/')
        );
    }
});
