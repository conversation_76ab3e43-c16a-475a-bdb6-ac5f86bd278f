/* خطوط عربية */
body {
    font-family: 'Cairo', sans-serif;
    background-color: #f8f9fa;
}

/* تحسينات عامة */
.card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.btn {
    border-radius: 8px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
}

/* تحسينات الجداول */
.table {
    border-radius: 10px;
    overflow: hidden;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

/* تحسينات النماذج */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* تحسينات التنقل */
.navbar-brand {
    font-weight: 700;
    font-size: 1.5rem;
}

.nav-link {
    font-weight: 500;
    transition: all 0.3s ease;
}

.nav-link:hover {
    transform: translateY(-1px);
}

/* مؤشر الاتصال */
#offline-indicator {
    border-radius: 10px;
    border: none;
    font-weight: 600;
}

/* تحسينات الإحصائيات */
.border-start {
    border-width: 4px !important;
}

/* تحسينات الأزرار */
.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-top-right-radius: 8px;
    border-bottom-right-radius: 8px;
}

.btn-group .btn:last-child {
    border-top-left-radius: 8px;
    border-bottom-left-radius: 8px;
}

/* تحسينات التنبيهات */
.alert {
    border-radius: 10px;
    border: none;
    font-weight: 500;
}

/* تحسينات الصفحات */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 2px solid #e9ecef;
    font-weight: 500;
}

.pagination .page-item.active .page-link {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

/* تحسينات الشاشات الصغيرة */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 15px;
        padding-right: 15px;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        font-size: 0.875rem;
    }
    
    .table-responsive {
        font-size: 0.875rem;
    }
}

/* تحسينات الطباعة */
@media print {
    .navbar, .btn, .pagination, #offline-indicator {
        display: none !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
    
    body {
        background-color: white !important;
    }
}

/* تحسينات PWA */
.pwa-install-prompt {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #0d6efd;
    color: white;
    padding: 15px 20px;
    border-radius: 10px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: none;
}

/* تحسينات الحالة */
.status-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 6px;
    font-weight: 600;
}

/* تحسينات البحث */
.search-box {
    position: relative;
}

.search-box .form-control {
    padding-right: 40px;
}

.search-box .search-icon {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    color: #6c757d;
}

/* تحسينات الأيقونات */
.icon-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

/* تحسينات الإحصائيات المتقدمة */
.stat-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    padding: 1.5rem;
    position: relative;
    overflow: hidden;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50%;
    transform: rotate(45deg);
}

/* تحسينات الجداول المتقدمة */
.table-hover tbody tr:hover {
    background-color: rgba(13, 110, 253, 0.05);
}

/* تحسينات النماذج المتقدمة */
.form-floating .form-control {
    border-radius: 10px;
}

.form-floating label {
    font-weight: 500;
    color: #6c757d;
}

/* تحسينات الشارات */
.badge {
    font-size: 0.75rem;
    padding: 0.35rem 0.65rem;
    border-radius: 6px;
}

/* تحسينات القوائم المنسدلة */
.dropdown-menu {
    border-radius: 10px;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.dropdown-item {
    font-weight: 500;
    transition: all 0.3s ease;
}

.dropdown-item:hover {
    background-color: rgba(13, 110, 253, 0.1);
    transform: translateX(5px);
}
