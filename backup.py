#!/usr/bin/env python3
"""
سكريبت إدارة النسخ الاحتياطية
"""

import os
import shutil
import sqlite3
import zipfile
from datetime import datetime
import argparse
import sys

def create_backup_folder():
    """إنشاء مجلد النسخ الاحتياطية"""
    backup_folder = 'backups'
    if not os.path.exists(backup_folder):
        os.makedirs(backup_folder)
    return backup_folder

def backup_database():
    """إنشاء نسخة احتياطية من قاعدة البيانات"""
    print("🔄 إنشاء نسخة احتياطية من قاعدة البيانات...")
    
    # مسار قاعدة البيانات
    db_path = 'sales_inventory.db'
    
    if not os.path.exists(db_path):
        print("❌ قاعدة البيانات غير موجودة")
        return None
    
    # إنشاء مجلد النسخ الاحتياطية
    backup_folder = create_backup_folder()
    
    # اسم النسخة الاحتياطية
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_name = f"database_backup_{timestamp}.db"
    backup_path = os.path.join(backup_folder, backup_name)
    
    try:
        # نسخ قاعدة البيانات
        shutil.copy2(db_path, backup_path)
        
        # التحقق من سلامة النسخة الاحتياطية
        conn = sqlite3.connect(backup_path)
        conn.execute("SELECT COUNT(*) FROM sqlite_master")
        conn.close()
        
        print(f"✅ تم إنشاء النسخة الاحتياطية: {backup_path}")
        return backup_path
        
    except Exception as e:
        print(f"❌ فشل في إنشاء النسخة الاحتياطية: {e}")
        return None

def backup_full_application():
    """إنشاء نسخة احتياطية كاملة من التطبيق"""
    print("🔄 إنشاء نسخة احتياطية كاملة...")
    
    # إنشاء مجلد النسخ الاحتياطية
    backup_folder = create_backup_folder()
    
    # اسم النسخة الاحتياطية
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    backup_name = f"full_backup_{timestamp}.zip"
    backup_path = os.path.join(backup_folder, backup_name)
    
    try:
        with zipfile.ZipFile(backup_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # الملفات المهمة للنسخ الاحتياطي
            important_files = [
                'app.py',
                'models.py',
                'config.py',
                'utils.py',
                'init_db.py',
                'run.py',
                'requirements.txt',
                'README.md',
                'QUICKSTART.md',
                'sales_inventory.db'
            ]
            
            # المجلدات المهمة
            important_folders = [
                'templates',
                'static'
            ]
            
            # إضافة الملفات
            for file in important_files:
                if os.path.exists(file):
                    zipf.write(file)
                    print(f"   ✓ {file}")
            
            # إضافة المجلدات
            for folder in important_folders:
                if os.path.exists(folder):
                    for root, dirs, files in os.walk(folder):
                        for file in files:
                            file_path = os.path.join(root, file)
                            zipf.write(file_path)
                    print(f"   ✓ {folder}/")
        
        print(f"✅ تم إنشاء النسخة الاحتياطية الكاملة: {backup_path}")
        return backup_path
        
    except Exception as e:
        print(f"❌ فشل في إنشاء النسخة الاحتياطية الكاملة: {e}")
        return None

def restore_database(backup_path):
    """استعادة قاعدة البيانات من نسخة احتياطية"""
    print(f"🔄 استعادة قاعدة البيانات من: {backup_path}")
    
    if not os.path.exists(backup_path):
        print("❌ ملف النسخة الاحتياطية غير موجود")
        return False
    
    # التحقق من سلامة النسخة الاحتياطية
    try:
        conn = sqlite3.connect(backup_path)
        conn.execute("SELECT COUNT(*) FROM sqlite_master")
        conn.close()
    except Exception as e:
        print(f"❌ النسخة الاحتياطية تالفة: {e}")
        return False
    
    # إنشاء نسخة احتياطية من قاعدة البيانات الحالية
    current_db = 'sales_inventory.db'
    if os.path.exists(current_db):
        backup_current = f"sales_inventory_before_restore_{datetime.now().strftime('%Y%m%d_%H%M%S')}.db"
        shutil.copy2(current_db, backup_current)
        print(f"📦 تم حفظ قاعدة البيانات الحالية: {backup_current}")
    
    try:
        # استعادة قاعدة البيانات
        shutil.copy2(backup_path, current_db)
        print("✅ تم استعادة قاعدة البيانات بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ فشل في استعادة قاعدة البيانات: {e}")
        return False

def list_backups():
    """عرض قائمة النسخ الاحتياطية"""
    print("📋 النسخ الاحتياطية المتوفرة:")
    
    backup_folder = 'backups'
    if not os.path.exists(backup_folder):
        print("   لا توجد نسخ احتياطية")
        return []
    
    backups = []
    for file in os.listdir(backup_folder):
        if file.endswith('.db') or file.endswith('.zip'):
            file_path = os.path.join(backup_folder, file)
            file_size = os.path.getsize(file_path)
            file_time = datetime.fromtimestamp(os.path.getmtime(file_path))
            
            backups.append({
                'name': file,
                'path': file_path,
                'size': file_size,
                'time': file_time
            })
    
    # ترتيب حسب التاريخ
    backups.sort(key=lambda x: x['time'], reverse=True)
    
    if not backups:
        print("   لا توجد نسخ احتياطية")
        return []
    
    for i, backup in enumerate(backups, 1):
        size_mb = backup['size'] / (1024 * 1024)
        print(f"   {i}. {backup['name']}")
        print(f"      التاريخ: {backup['time'].strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"      الحجم: {size_mb:.2f} MB")
        print()
    
    return backups

def cleanup_old_backups(keep_count=10):
    """حذف النسخ الاحتياطية القديمة"""
    print(f"🧹 تنظيف النسخ الاحتياطية القديمة (الاحتفاظ بـ {keep_count} نسخ)...")
    
    backup_folder = 'backups'
    if not os.path.exists(backup_folder):
        return
    
    # الحصول على قائمة النسخ الاحتياطية
    backups = []
    for file in os.listdir(backup_folder):
        if file.endswith('.db') or file.endswith('.zip'):
            file_path = os.path.join(backup_folder, file)
            file_time = os.path.getmtime(file_path)
            backups.append((file_path, file_time))
    
    # ترتيب حسب التاريخ (الأحدث أولاً)
    backups.sort(key=lambda x: x[1], reverse=True)
    
    # حذف النسخ الزائدة
    deleted_count = 0
    for backup_path, _ in backups[keep_count:]:
        try:
            os.remove(backup_path)
            print(f"   🗑️  تم حذف: {os.path.basename(backup_path)}")
            deleted_count += 1
        except Exception as e:
            print(f"   ❌ فشل في حذف {os.path.basename(backup_path)}: {e}")
    
    if deleted_count > 0:
        print(f"✅ تم حذف {deleted_count} نسخة احتياطية قديمة")
    else:
        print("✅ لا توجد نسخ احتياطية قديمة للحذف")

def main():
    """الوظيفة الرئيسية"""
    parser = argparse.ArgumentParser(description='إدارة النسخ الاحتياطية لنظام المبيعات')
    parser.add_argument('action', choices=['backup', 'full-backup', 'restore', 'list', 'cleanup'],
                       help='العملية المطلوبة')
    parser.add_argument('--file', help='مسار ملف النسخة الاحتياطية للاستعادة')
    parser.add_argument('--keep', type=int, default=10, help='عدد النسخ الاحتياطية للاحتفاظ بها')
    
    args = parser.parse_args()
    
    print("🗄️  إدارة النسخ الاحتياطية - نظام المبيعات")
    print("=" * 50)
    
    if args.action == 'backup':
        backup_database()
        
    elif args.action == 'full-backup':
        backup_full_application()
        
    elif args.action == 'restore':
        if not args.file:
            print("❌ يجب تحديد ملف النسخة الاحتياطية باستخدام --file")
            sys.exit(1)
        restore_database(args.file)
        
    elif args.action == 'list':
        list_backups()
        
    elif args.action == 'cleanup':
        cleanup_old_backups(args.keep)
    
    print("=" * 50)
    print("✅ تمت العملية")

if __name__ == '__main__':
    main()
