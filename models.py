from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

class User(UserMixin, db.Model):
    """نموذج المستخدمين"""
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    role = db.Column(db.String(20), nullable=False, default='seller')  # admin, seller, warehouse_manager
    is_active = db.Column(db.<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    # last_login = db.Column(db.DateTime)  # سيتم إضافته لاحقاً

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

class Category(db.Model):
    """تصنيفات المنتجات"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    products = db.relationship('Product', backref='category', lazy=True)

class Product(db.Model):
    """المنتجات"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    barcode = db.Column(db.String(50), unique=True)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'), nullable=False)
    purchase_price = db.Column(db.Float, nullable=False, default=0)
    selling_price = db.Column(db.Float, nullable=False, default=0)
    stock_quantity = db.Column(db.Integer, nullable=False, default=0)
    min_quantity = db.Column(db.Integer, nullable=False, default=5)
    unit = db.Column(db.String(20), default='قطعة')
    description = db.Column(db.Text)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class Customer(db.Model):
    """العملاء"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    credit_limit = db.Column(db.Float, default=0)
    current_balance = db.Column(db.Float, default=0)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Invoice(db.Model):
    """الفواتير"""
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    invoice_type = db.Column(db.String(20), nullable=False, default='sale')  # sale, return
    payment_method = db.Column(db.String(20), nullable=False, default='cash')  # cash, card, credit
    total_amount = db.Column(db.Float, nullable=False, default=0)
    discount = db.Column(db.Float, default=0)
    tax = db.Column(db.Float, default=0)
    final_amount = db.Column(db.Float, nullable=False, default=0)
    paid_amount = db.Column(db.Float, default=0)
    status = db.Column(db.String(20), default='completed')  # completed, pending, cancelled
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    customer = db.relationship('Customer', backref='invoices')
    user = db.relationship('User', backref='invoices')
    items = db.relationship('InvoiceItem', backref='invoice', lazy=True, cascade='all, delete-orphan')

class InvoiceItem(db.Model):
    """عناصر الفاتورة"""
    id = db.Column(db.Integer, primary_key=True)
    invoice_id = db.Column(db.Integer, db.ForeignKey('invoice.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_price = db.Column(db.Float, nullable=False)
    total_price = db.Column(db.Float, nullable=False)

    product = db.relationship('Product', backref='invoice_items')

class StockMovement(db.Model):
    """حركات المخزون المتقدمة"""
    id = db.Column(db.Integer, primary_key=True)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    movement_type = db.Column(db.String(20), nullable=False)  # in, out, adjustment, transfer
    quantity = db.Column(db.Integer, nullable=False)
    unit_cost = db.Column(db.Float, default=0)  # تكلفة الوحدة
    total_cost = db.Column(db.Float, default=0)  # إجمالي التكلفة
    reference_type = db.Column(db.String(20))  # sale, purchase, adjustment, transfer
    reference_id = db.Column(db.Integer)  # معرف المرجع (فاتورة، تسوية، إلخ)
    warehouse_from = db.Column(db.String(100))  # المخزن المصدر
    warehouse_to = db.Column(db.String(100))  # المخزن الوجهة
    balance_before = db.Column(db.Integer, default=0)  # الرصيد قبل الحركة
    balance_after = db.Column(db.Integer, default=0)  # الرصيد بعد الحركة
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    product = db.relationship('Product', backref='stock_movements')
    user = db.relationship('User', backref='stock_movements')

class Supplier(db.Model):
    """الموردين"""
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    company_name = db.Column(db.String(200))
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    address = db.Column(db.Text)
    tax_number = db.Column(db.String(50))
    credit_limit = db.Column(db.Float, default=0)
    current_balance = db.Column(db.Float, default=0)  # المبلغ المستحق للمورد
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    purchase_invoices = db.relationship('PurchaseInvoice', backref='supplier', lazy=True)

class PurchaseInvoice(db.Model):
    """فواتير الشراء"""
    id = db.Column(db.Integer, primary_key=True)
    invoice_number = db.Column(db.String(50), unique=True, nullable=False)
    supplier_id = db.Column(db.Integer, db.ForeignKey('supplier.id'), nullable=False)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    invoice_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date)
    due_date = db.Column(db.Date)
    subtotal = db.Column(db.Float, nullable=False, default=0)
    tax_amount = db.Column(db.Float, default=0)
    discount = db.Column(db.Float, default=0)
    total_amount = db.Column(db.Float, nullable=False, default=0)
    paid_amount = db.Column(db.Float, default=0)
    status = db.Column(db.String(20), default='pending')  # pending, received, cancelled
    payment_status = db.Column(db.String(20), default='unpaid')  # unpaid, partial, paid
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # العلاقات
    items = db.relationship('PurchaseInvoiceItem', backref='purchase_invoice', lazy=True, cascade='all, delete-orphan')

class PurchaseInvoiceItem(db.Model):
    """عناصر فاتورة الشراء"""
    id = db.Column(db.Integer, primary_key=True)
    purchase_invoice_id = db.Column(db.Integer, db.ForeignKey('purchase_invoice.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    unit_cost = db.Column(db.Float, nullable=False)  # سعر الشراء للوحدة
    total_cost = db.Column(db.Float, nullable=False)  # إجمالي التكلفة
    received_quantity = db.Column(db.Integer, default=0)  # الكمية المستلمة

    # العلاقات
    product = db.relationship('Product', backref='purchase_items')

class Payment(db.Model):
    """المدفوعات"""
    id = db.Column(db.Integer, primary_key=True)
    payment_type = db.Column(db.String(20), nullable=False)  # customer_payment, supplier_payment
    reference_type = db.Column(db.String(20), nullable=False)  # sale_invoice, purchase_invoice
    reference_id = db.Column(db.Integer, nullable=False)
    customer_id = db.Column(db.Integer, db.ForeignKey('customer.id'))
    supplier_id = db.Column(db.Integer, db.ForeignKey('supplier.id'))
    amount = db.Column(db.Float, nullable=False)
    payment_method = db.Column(db.String(20), default='cash')  # cash, bank, check
    payment_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date)
    notes = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    customer = db.relationship('Customer', backref='payments')
    supplier = db.relationship('Supplier', backref='payments')
    user = db.relationship('User', backref='payments')

class FinancialAccount(db.Model):
    """الحسابات المالية"""
    id = db.Column(db.Integer, primary_key=True)
    account_code = db.Column(db.String(20), unique=True, nullable=False)
    account_name = db.Column(db.String(200), nullable=False)
    account_type = db.Column(db.String(50), nullable=False)  # asset, liability, equity, revenue, expense
    parent_id = db.Column(db.Integer, db.ForeignKey('financial_account.id'))
    balance = db.Column(db.Float, default=0)
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

    # العلاقات
    children = db.relationship('FinancialAccount', backref=db.backref('parent', remote_side=[id]))

class JournalEntry(db.Model):
    """القيود المحاسبية"""
    id = db.Column(db.Integer, primary_key=True)
    entry_number = db.Column(db.String(50), unique=True, nullable=False)
    entry_date = db.Column(db.Date, nullable=False, default=datetime.utcnow().date)
    description = db.Column(db.Text, nullable=False)
    reference_type = db.Column(db.String(20))  # sale, purchase, payment, adjustment
    reference_id = db.Column(db.Integer)
    total_debit = db.Column(db.Float, default=0)
    total_credit = db.Column(db.Float, default=0)
    status = db.Column(db.String(20), default='draft')  # draft, posted
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)

    # العلاقات
    entries = db.relationship('JournalEntryLine', backref='journal_entry', lazy=True, cascade='all, delete-orphan')
    user = db.relationship('User', backref='journal_entries')

class JournalEntryLine(db.Model):
    """سطور القيد المحاسبي"""
    id = db.Column(db.Integer, primary_key=True)
    journal_entry_id = db.Column(db.Integer, db.ForeignKey('journal_entry.id'), nullable=False)
    account_id = db.Column(db.Integer, db.ForeignKey('financial_account.id'), nullable=False)
    description = db.Column(db.Text)
    debit_amount = db.Column(db.Float, default=0)
    credit_amount = db.Column(db.Float, default=0)

    # العلاقات
    account = db.relationship('FinancialAccount', backref='journal_lines')
