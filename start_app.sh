#!/bin/bash

# 🚀 تشغيل تطبيق إدارة المبيعات والمخازن
# تشغيل بنقرة واحدة

echo "🚀 بدء تشغيل تطبيق إدارة المبيعات والمخازن..."
echo "=================================================="

# الانتقال لمجلد التطبيق
cd "$(dirname "$0")"

# تفعيل البيئة الافتراضية
echo "🔧 تفعيل البيئة الافتراضية..."
source venv/bin/activate

# التحقق من وجود قاعدة البيانات
if [ ! -f "instance/sales_inventory.db" ]; then
    echo "📊 إنشاء قاعدة البيانات والبيانات التجريبية..."
    python init_db.py
    python add_sample_data.py
fi

# تشغيل التطبيق
echo "🌐 تشغيل التطبيق..."
echo "=================================================="
echo "✅ التطبيق يعمل الآن!"
echo "🌐 افتح المتصفح على: http://localhost:5000"
echo "👤 المستخدم: admin"
echo "🔑 كلمة المرور: admin123"
echo "=================================================="
echo "⏹️  لإيقاف التطبيق اضغط Ctrl+C"
echo ""

# تشغيل التطبيق
python run.py
