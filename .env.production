# متغيرات البيئة للإنتاج
# انسخ هذا الملف إلى .env وعدل القيم حسب بيئتك

# بيئة التشغيل
FLASK_ENV=production

# مفتاح الأمان - يجب تغييره لمفتاح عشوائي قوي
SECRET_KEY=your-super-secret-key-change-this-in-production

# قاعدة البيانات
DATABASE_URL=sqlite:///sales_inventory.db

# إعدادات الخادم
HOST=0.0.0.0
PORT=5000

# إعدادات الأمان
SESSION_COOKIE_SECURE=True
SESSION_COOKIE_HTTPONLY=True

# إعدادات التسجيل
LOG_LEVEL=INFO

# إعدادات البريد الإلكتروني (اختياري)
# MAIL_SERVER=smtp.gmail.com
# MAIL_PORT=587
# MAIL_USE_TLS=True
# MAIL_USERNAME=<EMAIL>
# MAIL_PASSWORD=your-app-password

# إعدادات التخزين السحابي (اختياري)
# AWS_ACCESS_KEY_ID=your-access-key
# AWS_SECRET_ACCESS_KEY=your-secret-key
# AWS_S3_BUCKET=your-bucket-name
