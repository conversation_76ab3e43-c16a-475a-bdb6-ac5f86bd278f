#!/usr/bin/env python3
"""
فحص شامل لخيار العملاء
"""

import requests
import json
from datetime import datetime

class CustomersChecker:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "http://127.0.0.1:5000"
        self.issues = []
        self.successes = []
        
    def login(self):
        """تسجيل الدخول"""
        try:
            login_data = {'username': 'admin', 'password': 'admin123'}
            response = self.session.post(f"{self.base_url}/login", data=login_data, allow_redirects=False)
            if response.status_code == 302:
                print("✅ تم تسجيل الدخول بنجاح")
                return True
            else:
                print(f"❌ فشل تسجيل الدخول: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ خطأ في تسجيل الدخول: {e}")
            return False
    
    def test_customers_page(self):
        """فحص صفحة العملاء الرئيسية"""
        print("\n📄 فحص صفحة العملاء الرئيسية...")
        
        try:
            response = self.session.get(f"{self.base_url}/customers")
            if response.status_code == 200:
                print("✅ صفحة العملاء تُحمّل بنجاح")
                
                # فحص محتوى الصفحة
                content = response.text
                
                # فحص العناصر المهمة
                checks = [
                    ("جدول العملاء", "table" in content.lower()),
                    ("زر إضافة عميل", "إضافة عميل" in content or "add" in content.lower()),
                    ("خانة البحث", "search" in content.lower() or "بحث" in content),
                    ("أعمدة الجدول", "الاسم" in content and "الهاتف" in content),
                ]
                
                for check_name, condition in checks:
                    if condition:
                        print(f"  ✅ {check_name} موجود")
                        self.successes.append(f"صفحة العملاء - {check_name}")
                    else:
                        print(f"  ❌ {check_name} مفقود")
                        self.issues.append(f"صفحة العملاء - {check_name} مفقود")
                
                return True
            else:
                print(f"❌ صفحة العملاء لا تعمل: {response.status_code}")
                self.issues.append(f"صفحة العملاء لا تعمل - كود {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في فحص صفحة العملاء: {e}")
            self.issues.append(f"خطأ في صفحة العملاء: {e}")
            return False
    
    def test_add_customer_page(self):
        """فحص صفحة إضافة عميل"""
        print("\n➕ فحص صفحة إضافة عميل...")
        
        try:
            response = self.session.get(f"{self.base_url}/customers/add")
            if response.status_code == 200:
                print("✅ صفحة إضافة عميل تُحمّل بنجاح")
                
                content = response.text
                
                # فحص حقول النموذج
                form_fields = [
                    ("حقل الاسم", "name" in content.lower()),
                    ("حقل الهاتف", "phone" in content.lower()),
                    ("حقل البريد الإلكتروني", "email" in content.lower()),
                    ("حقل العنوان", "address" in content.lower()),
                    ("حقل الحد الائتماني", "credit" in content.lower()),
                    ("زر الحفظ", "submit" in content.lower() or "حفظ" in content),
                ]
                
                for field_name, condition in form_fields:
                    if condition:
                        print(f"  ✅ {field_name} موجود")
                        self.successes.append(f"إضافة عميل - {field_name}")
                    else:
                        print(f"  ❌ {field_name} مفقود")
                        self.issues.append(f"إضافة عميل - {field_name} مفقود")
                
                return True
            else:
                print(f"❌ صفحة إضافة عميل لا تعمل: {response.status_code}")
                self.issues.append(f"صفحة إضافة عميل لا تعمل - كود {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في فحص صفحة إضافة عميل: {e}")
            self.issues.append(f"خطأ في صفحة إضافة عميل: {e}")
            return False
    
    def test_customer_search_api(self):
        """فحص API البحث في العملاء"""
        print("\n🔍 فحص API البحث في العملاء...")
        
        try:
            # البحث عن "أحمد"
            response = self.session.get(f"{self.base_url}/api/customers/search?q=أحمد")
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ API البحث يعمل - وجد {len(data)} نتيجة")
                    
                    if len(data) > 0:
                        print(f"  📋 أول نتيجة: {data[0].get('name', 'غير محدد')}")
                        self.successes.append("API البحث في العملاء يعمل ويعيد نتائج")
                    else:
                        print("  ⚠️  لا توجد نتائج للبحث")
                        self.issues.append("API البحث لا يعيد نتائج")
                    
                    return True
                except json.JSONDecodeError:
                    print("❌ API البحث لا يعيد JSON صحيح")
                    self.issues.append("API البحث لا يعيد JSON صحيح")
                    return False
            else:
                print(f"❌ API البحث لا يعمل: {response.status_code}")
                self.issues.append(f"API البحث لا يعمل - كود {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في فحص API البحث: {e}")
            self.issues.append(f"خطأ في API البحث: {e}")
            return False
    
    def test_add_customer_functionality(self):
        """فحص وظيفة إضافة عميل"""
        print("\n➕ فحص وظيفة إضافة عميل...")
        
        try:
            # بيانات عميل جديد
            customer_data = {
                'name': f'عميل اختبار {datetime.now().strftime("%H%M%S")}',
                'phone': '0501234567',
                'email': f'test_{datetime.now().strftime("%H%M%S")}@example.com',
                'address': 'عنوان اختبار',
                'credit_limit': '1000'
            }
            
            response = self.session.post(f"{self.base_url}/customers/add", data=customer_data)
            
            if response.status_code in [200, 302]:
                print("✅ إضافة عميل جديد تعمل")
                self.successes.append("إضافة عميل جديد")
                
                # التحقق من إضافة العميل بالبحث
                search_response = self.session.get(f"{self.base_url}/api/customers/search?q={customer_data['name']}")
                if search_response.status_code == 200:
                    search_data = search_response.json()
                    if len(search_data) > 0:
                        print("  ✅ العميل الجديد ظهر في نتائج البحث")
                        self.successes.append("العميل الجديد ظهر في البحث")
                    else:
                        print("  ⚠️  العميل الجديد لم يظهر في البحث")
                        self.issues.append("العميل الجديد لم يظهر في البحث")
                
                return True
            else:
                print(f"❌ فشل إضافة عميل جديد: {response.status_code}")
                self.issues.append(f"فشل إضافة عميل - كود {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في فحص إضافة عميل: {e}")
            self.issues.append(f"خطأ في إضافة عميل: {e}")
            return False
    
    def test_customer_data_display(self):
        """فحص عرض بيانات العملاء"""
        print("\n📊 فحص عرض بيانات العملاء...")
        
        try:
            # الحصول على قائمة العملاء
            response = self.session.get(f"{self.base_url}/api/customers/search?q=")
            if response.status_code == 200:
                customers = response.json()
                print(f"✅ تم العثور على {len(customers)} عميل")
                
                if len(customers) > 0:
                    # فحص بيانات أول عميل
                    first_customer = customers[0]
                    required_fields = ['id', 'name', 'phone']
                    
                    for field in required_fields:
                        if field in first_customer:
                            print(f"  ✅ حقل {field} موجود: {first_customer[field]}")
                            self.successes.append(f"حقل {field} في بيانات العميل")
                        else:
                            print(f"  ❌ حقل {field} مفقود")
                            self.issues.append(f"حقل {field} مفقود في بيانات العميل")
                    
                    return True
                else:
                    print("⚠️  لا توجد عملاء في النظام")
                    self.issues.append("لا توجد عملاء في النظام")
                    return False
            else:
                print(f"❌ فشل الحصول على بيانات العملاء: {response.status_code}")
                self.issues.append(f"فشل الحصول على بيانات العملاء - كود {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ خطأ في فحص بيانات العملاء: {e}")
            self.issues.append(f"خطأ في بيانات العملاء: {e}")
            return False
    
    def run_customers_check(self):
        """تشغيل فحص شامل لخيار العملاء"""
        print("🔍 فحص شامل لخيار العملاء")
        print("=" * 50)
        print(f"🕐 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        # تسجيل الدخول
        if not self.login():
            print("❌ فشل تسجيل الدخول - لا يمكن متابعة الفحص")
            return False
        
        # تشغيل جميع الفحوصات
        tests = [
            self.test_customers_page,
            self.test_add_customer_page,
            self.test_customer_search_api,
            self.test_customer_data_display,
            self.test_add_customer_functionality,
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test in tests:
            try:
                if test():
                    passed_tests += 1
            except Exception as e:
                print(f"💥 خطأ في تشغيل الفحص: {e}")
        
        # عرض النتائج
        self.show_results(passed_tests, total_tests)
    
    def show_results(self, passed_tests, total_tests):
        """عرض نتائج الفحص"""
        print("\n" + "=" * 50)
        print("📊 نتائج فحص خيار العملاء")
        print("=" * 50)
        
        # إحصائيات
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        print(f"\n📈 إحصائيات الفحص:")
        print(f"  ✅ اختبارات نجحت: {passed_tests}/{total_tests}")
        print(f"  📊 معدل النجاح: {success_rate:.1f}%")
        print(f"  🎯 نجاحات: {len(self.successes)}")
        print(f"  ❌ مشاكل: {len(self.issues)}")
        
        # عرض النجاحات
        if self.successes:
            print(f"\n✅ النجاحات ({len(self.successes)}):")
            for success in self.successes:
                print(f"  🟢 {success}")
        
        # عرض المشاكل
        if self.issues:
            print(f"\n❌ المشاكل المكتشفة ({len(self.issues)}):")
            for issue in self.issues:
                print(f"  🔴 {issue}")
        else:
            print("\n✅ لم يتم اكتشاف أي مشاكل!")
        
        # التقييم النهائي
        print(f"\n🎯 التقييم النهائي لخيار العملاء:")
        if success_rate >= 90:
            print("🎉 ممتاز - خيار العملاء يعمل بشكل مثالي!")
        elif success_rate >= 70:
            print("✅ جيد - خيار العملاء يعمل مع بعض التحسينات المطلوبة")
        else:
            print("⚠️  يحتاج إصلاح - خيار العملاء به مشاكل تحتاج حل")
        
        return success_rate >= 70

def main():
    """الوظيفة الرئيسية"""
    checker = CustomersChecker()
    success = checker.run_customers_check()
    return success

if __name__ == "__main__":
    main()
