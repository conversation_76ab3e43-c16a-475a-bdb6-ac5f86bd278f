#!/usr/bin/env python3
"""
اختبار شامل لوظائف إدارة المستخدمين
"""

import requests
import json
from datetime import datetime

class UsersManagementTester:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "http://127.0.0.1:5000"
        
    def login(self):
        """تسجيل الدخول كمدير"""
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = self.session.post(f"{self.base_url}/login", data=login_data, allow_redirects=False)
        return response.status_code == 302
    
    def test_users_page_access(self):
        """اختبار الوصول لصفحة المستخدمين"""
        print("\n👥 اختبار الوصول لصفحة المستخدمين:")
        
        try:
            response = self.session.get(f"{self.base_url}/users")
            if response.status_code == 200:
                print("  ✅ صفحة المستخدمين - يمكن الوصول إليها")
                
                # التحقق من وجود المحتوى المطلوب
                content = response.text
                if "إدارة المستخدمين" in content:
                    print("  ✅ عنوان الصفحة موجود")
                if "إضافة مستخدم جديد" in content:
                    print("  ✅ زر إضافة مستخدم موجود")
                if "admin" in content:
                    print("  ✅ مستخدم المدير معروض")
                
                return True
            else:
                print(f"  ❌ خطأ في الوصول للصفحة: {response.status_code}")
                return False
        except Exception as e:
            print(f"  ❌ خطأ: {e}")
            return False
    
    def test_add_user_page(self):
        """اختبار صفحة إضافة مستخدم"""
        print("\n➕ اختبار صفحة إضافة مستخدم:")
        
        try:
            response = self.session.get(f"{self.base_url}/users/add")
            if response.status_code == 200:
                print("  ✅ صفحة إضافة مستخدم - يمكن الوصول إليها")
                
                content = response.text
                if "إضافة مستخدم جديد" in content:
                    print("  ✅ عنوان الصفحة صحيح")
                if 'name="username"' in content:
                    print("  ✅ حقل اسم المستخدم موجود")
                if 'name="email"' in content:
                    print("  ✅ حقل البريد الإلكتروني موجود")
                if 'name="password"' in content:
                    print("  ✅ حقل كلمة المرور موجود")
                if 'name="role"' in content:
                    print("  ✅ حقل الدور موجود")
                
                return True
            else:
                print(f"  ❌ خطأ في الوصول للصفحة: {response.status_code}")
                return False
        except Exception as e:
            print(f"  ❌ خطأ: {e}")
            return False
    
    def test_add_new_user(self):
        """اختبار إضافة مستخدم جديد"""
        print("\n👤 اختبار إضافة مستخدم جديد:")
        
        try:
            # بيانات المستخدم الجديد
            user_data = {
                'username': f'testuser_{datetime.now().strftime("%H%M%S")}',
                'email': f'testuser_{datetime.now().strftime("%H%M%S")}@example.com',
                'password': 'testpass123',
                'role': 'seller'
            }
            
            response = self.session.post(f"{self.base_url}/users/add", data=user_data)
            
            if response.status_code in [200, 302]:  # نجح أو إعادة توجيه
                print(f"  ✅ تم إضافة المستخدم {user_data['username']} بنجاح")
                
                # التحقق من وجود المستخدم في قائمة المستخدمين
                users_page = self.session.get(f"{self.base_url}/users")
                if user_data['username'] in users_page.text:
                    print("  ✅ المستخدم الجديد ظهر في القائمة")
                    return True, user_data['username']
                else:
                    print("  ⚠️  المستخدم لم يظهر في القائمة")
                    return False, None
            else:
                print(f"  ❌ فشل في إضافة المستخدم: {response.status_code}")
                return False, None
        except Exception as e:
            print(f"  ❌ خطأ: {e}")
            return False, None
    
    def test_users_search_filter(self):
        """اختبار البحث والفلترة في المستخدمين"""
        print("\n🔍 اختبار البحث والفلترة:")
        
        try:
            # اختبار البحث بالاسم
            response = self.session.get(f"{self.base_url}/users?search=admin")
            if response.status_code == 200:
                if "admin" in response.text:
                    print("  ✅ البحث بالاسم يعمل")
                else:
                    print("  ❌ البحث بالاسم لا يعمل")
                    return False
            
            # اختبار الفلترة بالدور
            response = self.session.get(f"{self.base_url}/users?role=admin")
            if response.status_code == 200:
                print("  ✅ الفلترة بالدور تعمل")
            else:
                print("  ❌ الفلترة بالدور لا تعمل")
                return False
            
            return True
        except Exception as e:
            print(f"  ❌ خطأ: {e}")
            return False
    
    def test_edit_user_page(self):
        """اختبار صفحة تعديل المستخدم"""
        print("\n✏️ اختبار صفحة تعديل المستخدم:")
        
        try:
            # محاولة تعديل مستخدم المدير (ID = 1 عادة)
            response = self.session.get(f"{self.base_url}/users/1/edit")
            if response.status_code == 200:
                print("  ✅ صفحة تعديل المستخدم - يمكن الوصول إليها")
                
                content = response.text
                if "تعديل المستخدم" in content:
                    print("  ✅ عنوان الصفحة صحيح")
                if 'value="admin"' in content:
                    print("  ✅ بيانات المستخدم محملة")
                
                return True
            else:
                print(f"  ❌ خطأ في الوصول للصفحة: {response.status_code}")
                return False
        except Exception as e:
            print(f"  ❌ خطأ: {e}")
            return False
    
    def test_user_roles_display(self):
        """اختبار عرض الأدوار المختلفة"""
        print("\n🎭 اختبار عرض الأدوار:")
        
        try:
            response = self.session.get(f"{self.base_url}/users")
            if response.status_code == 200:
                content = response.text
                
                roles_found = []
                if "مدير" in content:
                    roles_found.append("مدير")
                if "بائع" in content:
                    roles_found.append("بائع")
                if "مشرف مخزن" in content:
                    roles_found.append("مشرف مخزن")
                
                print(f"  ✅ الأدوار المعروضة: {', '.join(roles_found)}")
                return len(roles_found) > 0
            else:
                print(f"  ❌ خطأ في الوصول للصفحة: {response.status_code}")
                return False
        except Exception as e:
            print(f"  ❌ خطأ: {e}")
            return False
    
    def test_user_statistics(self):
        """اختبار إحصائيات المستخدمين"""
        print("\n📊 اختبار إحصائيات المستخدمين:")
        
        try:
            response = self.session.get(f"{self.base_url}/users")
            if response.status_code == 200:
                content = response.text
                
                # البحث عن الإحصائيات
                if "إجمالي المستخدمين" in content:
                    print("  ✅ إحصائية إجمالي المستخدمين موجودة")
                if "المديرين" in content:
                    print("  ✅ إحصائية المديرين موجودة")
                if "البائعين" in content:
                    print("  ✅ إحصائية البائعين موجودة")
                if "مشرفي المخزن" in content:
                    print("  ✅ إحصائية مشرفي المخزن موجودة")
                
                return True
            else:
                print(f"  ❌ خطأ في الوصول للصفحة: {response.status_code}")
                return False
        except Exception as e:
            print(f"  ❌ خطأ: {e}")
            return False
    
    def run_all_tests(self):
        """تشغيل جميع اختبارات إدارة المستخدمين"""
        print("🧪 اختبار شامل لوظائف إدارة المستخدمين")
        print("=" * 60)
        
        # تسجيل الدخول
        if not self.login():
            print("❌ فشل تسجيل الدخول")
            return False
        
        print("✅ تم تسجيل الدخول كمدير")
        
        # قائمة الاختبارات
        tests = [
            ("الوصول لصفحة المستخدمين", self.test_users_page_access),
            ("صفحة إضافة مستخدم", self.test_add_user_page),
            ("إضافة مستخدم جديد", self.test_add_new_user),
            ("البحث والفلترة", self.test_users_search_filter),
            ("صفحة تعديل المستخدم", self.test_edit_user_page),
            ("عرض الأدوار", self.test_user_roles_display),
            ("إحصائيات المستخدمين", self.test_user_statistics)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🔍 اختبار: {test_name}")
            try:
                result = test_func()
                if isinstance(result, tuple):
                    # للاختبارات التي ترجع نتائج إضافية
                    success = result[0]
                else:
                    success = result
                
                if success:
                    passed += 1
                    print(f"✅ {test_name} - نجح")
                else:
                    print(f"❌ {test_name} - فشل")
            except Exception as e:
                print(f"❌ {test_name} - خطأ: {e}")
            
            print("-" * 40)
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print(f"📊 نتائج اختبار إدارة المستخدمين:")
        print(f"✅ نجح: {passed}/{total}")
        print(f"❌ فشل: {total - passed}/{total}")
        print(f"📈 معدل النجاح: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 جميع وظائف إدارة المستخدمين تعمل بشكل مثالي!")
            return True
        elif passed >= total * 0.8:
            print(f"\n✅ معظم وظائف إدارة المستخدمين تعمل بشكل جيد ({passed}/{total})")
            return True
        else:
            print(f"\n⚠️  بعض وظائف إدارة المستخدمين تحتاج لمراجعة ({total - passed} وظائف)")
            return False

def main():
    """الوظيفة الرئيسية"""
    print(f"🕐 وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 عنوان التطبيق: http://127.0.0.1:5000")
    print()
    
    tester = UsersManagementTester()
    success = tester.run_all_tests()
    
    print("\n" + "=" * 60)
    if success:
        print("🏆 وظائف إدارة المستخدمين تعمل بشكل ممتاز!")
    else:
        print("🔧 بعض وظائف إدارة المستخدمين تحتاج لمراجعة")
    
    return success

if __name__ == "__main__":
    main()
