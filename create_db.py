#!/usr/bin/env python3
"""
سكريبت إنشاء قاعدة البيانات الجديدة مع جميع النماذج المحدثة
"""

import os
import sys

# إضافة المجلد الحالي إلى مسار Python
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import app, db
from models import *
from werkzeug.security import generate_password_hash

def create_database():
    """إنشاء قاعدة البيانات والبيانات الأولية"""
    try:
        with app.app_context():
            # حذف جميع الجداول الموجودة
            db.drop_all()
            print('🗑️ تم حذف الجداول القديمة')

            # إنشاء قاعدة البيانات الجديدة
            db.create_all()
            print('✅ تم إنشاء قاعدة البيانات الجديدة مع جميع النماذج')

            # إنشاء مستخدم إداري
            admin = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                role='admin'
            )
            db.session.add(admin)
            print('✅ تم إنشاء المستخدم الإداري')

            # إنشاء تصنيف افتراضي
            category = Category(name='عام', description='تصنيف عام للمنتجات')
            db.session.add(category)
            print('✅ تم إنشاء التصنيف الافتراضي')

            # إنشاء منتج تجريبي
            product = Product(
                name='منتج تجريبي',
                barcode='123456789',
                category_id=1,
                purchase_price=10.000,
                selling_price=15.000,
                stock_quantity=100,
                min_quantity=10,
                unit='قطعة',
                description='منتج تجريبي للاختبار'
            )
            db.session.add(product)
            print('✅ تم إنشاء منتج تجريبي')

            # إنشاء عميل تجريبي
            customer = Customer(
                name='عميل تجريبي',
                phone='+218912345678',
                email='<EMAIL>',
                address='طرابلس، ليبيا',
                credit_limit=1000.000
            )
            db.session.add(customer)
            print('✅ تم إنشاء عميل تجريبي')

            # إنشاء مورد تجريبي
            supplier = Supplier(
                name='مورد تجريبي',
                company_name='شركة الموردين المحدودة',
                phone='+218923456789',
                email='<EMAIL>',
                address='بنغازي، ليبيا',
                credit_limit=5000.000
            )
            db.session.add(supplier)
            print('✅ تم إنشاء مورد تجريبي')

            db.session.commit()
            print('✅ تم حفظ جميع البيانات الأولية')
            print('👤 المستخدم: admin')
            print('🔑 كلمة المرور: admin123')
            print('🎉 قاعدة البيانات جاهزة للاستخدام!')

    except Exception as e:
        print(f'❌ خطأ في إنشاء قاعدة البيانات: {str(e)}')
        return False

    return True

if __name__ == '__main__':
    success = create_database()
    if success:
        print('\n🎊 تم إنشاء قاعدة البيانات بنجاح!')
    else:
        print('\n💥 فشل في إنشاء قاعدة البيانات!')
