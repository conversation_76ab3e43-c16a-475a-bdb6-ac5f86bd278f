#!/usr/bin/env python3
"""
سكريبت إنشاء قاعدة البيانات الجديدة مع جميع النماذج المحدثة
"""

from app import app, db
from models import User, Category, Product, Customer
from werkzeug.security import generate_password_hash

def create_database():
    """إنشاء قاعدة البيانات والبيانات الأولية"""
    with app.app_context():
        # إنشاء قاعدة البيانات
        db.create_all()
        print('✅ تم إنشاء قاعدة البيانات الجديدة')

        # إنشاء مستخدم إداري إذا لم يكن موجوداً
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                password_hash=generate_password_hash('admin123'),
                role='admin'
            )
            db.session.add(admin)
            print('✅ تم إنشاء المستخدم الإداري')
        else:
            print('ℹ️ المستخدم الإداري موجود مسبقاً')

        # إنشاء تصنيف افتراضي إذا لم يكن موجوداً
        category = Category.query.filter_by(name='عام').first()
        if not category:
            category = Category(name='عام', description='تصنيف عام للمنتجات')
            db.session.add(category)
            print('✅ تم إنشاء التصنيف الافتراضي')
        else:
            print('ℹ️ التصنيف الافتراضي موجود مسبقاً')

        db.session.commit()
        print('✅ تم إنشاء البيانات الأولية')
        print('👤 المستخدم: admin')
        print('🔑 كلمة المرور: admin123')

if __name__ == '__main__':
    create_database()
