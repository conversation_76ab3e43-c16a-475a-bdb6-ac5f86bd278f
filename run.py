#!/usr/bin/env python3
"""
ملف تشغيل التطبيق
"""

from app import app
import os

if __name__ == '__main__':
    # تحديد المنفذ من متغيرات البيئة أو استخدام 5000 كافتراضي
    port = int(os.environ.get('PORT', 5000))
    
    # تحديد وضع التطوير
    debug = os.environ.get('FLASK_ENV') == 'development'
    
    print("🚀 بدء تشغيل نظام إدارة المبيعات والمخازن")
    print("=" * 50)
    print(f"🌐 الرابط: http://localhost:{port}")
    print("👤 المستخدم: admin")
    print("🔑 كلمة المرور: admin123")
    print("=" * 50)
    
    # تشغيل التطبيق
    app.run(
        host='0.0.0.0',
        port=port,
        debug=debug
    )
