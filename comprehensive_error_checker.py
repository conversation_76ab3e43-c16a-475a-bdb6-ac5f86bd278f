#!/usr/bin/env python3
"""
فحص شامل ومتقدم للتطبيق للكشف عن جميع الأخطاء ومعالجتها
"""

import requests
import json
import sqlite3
import os
import sys
import traceback
from datetime import datetime
import time

class ComprehensiveErrorChecker:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "http://127.0.0.1:5000"
        self.errors = []
        self.warnings = []
        self.fixes_applied = []
        self.test_results = {}
        
    def log_error(self, category, description, severity="medium", fix_suggestion=None):
        """تسجيل خطأ"""
        error = {
            'category': category,
            'description': description,
            'severity': severity,
            'fix_suggestion': fix_suggestion,
            'timestamp': datetime.now()
        }
        self.errors.append(error)
        print(f"❌ [{category}] {description}")
        if fix_suggestion:
            print(f"   💡 اقتراح الإصلاح: {fix_suggestion}")
    
    def log_warning(self, category, description):
        """تسجيل تحذير"""
        warning = {
            'category': category,
            'description': description,
            'timestamp': datetime.now()
        }
        self.warnings.append(warning)
        print(f"⚠️  [{category}] {description}")
    
    def log_fix(self, description):
        """تسجيل إصلاح تم تطبيقه"""
        self.fixes_applied.append({
            'description': description,
            'timestamp': datetime.now()
        })
        print(f"🔧 تم الإصلاح: {description}")
    
    def test_basic_connectivity(self):
        """فحص الاتصال الأساسي"""
        print("\n🌐 فحص الاتصال الأساسي...")
        try:
            response = self.session.get(f"{self.base_url}/", timeout=10)
            if response.status_code == 200:
                print("✅ الاتصال بالتطبيق يعمل")
                self.test_results['connectivity'] = True
                return True
            else:
                self.log_error("Connectivity", f"فشل الاتصال - كود {response.status_code}", "high")
                self.test_results['connectivity'] = False
                return False
        except requests.exceptions.ConnectionError:
            self.log_error("Connectivity", "لا يمكن الاتصال بالتطبيق - تأكد من تشغيله", "high")
            self.test_results['connectivity'] = False
            return False
        except Exception as e:
            self.log_error("Connectivity", f"خطأ في الاتصال: {e}", "high")
            self.test_results['connectivity'] = False
            return False
    
    def test_authentication(self):
        """فحص نظام المصادقة"""
        print("\n🔐 فحص نظام المصادقة...")
        
        # اختبار صفحة تسجيل الدخول
        try:
            response = self.session.get(f"{self.base_url}/login")
            if response.status_code != 200:
                self.log_error("Authentication", "صفحة تسجيل الدخول لا تعمل", "high")
                return False
            
            # اختبار تسجيل الدخول
            login_data = {'username': 'admin', 'password': 'admin123'}
            response = self.session.post(f"{self.base_url}/login", data=login_data, allow_redirects=False)
            
            if response.status_code == 302:
                print("✅ تسجيل الدخول يعمل")
                self.test_results['authentication'] = True
                
                # اختبار الوصول للوحة التحكم
                dashboard_response = self.session.get(f"{self.base_url}/")
                if dashboard_response.status_code == 200:
                    print("✅ الوصول للوحة التحكم يعمل")
                    return True
                else:
                    self.log_error("Authentication", "فشل الوصول للوحة التحكم بعد تسجيل الدخول", "medium")
                    return False
            else:
                self.log_error("Authentication", "فشل تسجيل الدخول", "high", 
                             "تحقق من بيانات المستخدم الافتراضي في قاعدة البيانات")
                self.test_results['authentication'] = False
                return False
                
        except Exception as e:
            self.log_error("Authentication", f"خطأ في نظام المصادقة: {e}", "high")
            self.test_results['authentication'] = False
            return False
    
    def test_database_integrity(self):
        """فحص سلامة قاعدة البيانات"""
        print("\n🗄️ فحص سلامة قاعدة البيانات...")
        
        db_paths = ['instance/sales_inventory.db', 'sales_inventory.db']
        db_found = False
        
        for db_path in db_paths:
            if os.path.exists(db_path):
                db_found = True
                print(f"✅ قاعدة البيانات موجودة: {db_path}")
                
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    
                    # فحص الجداول
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [table[0] for table in cursor.fetchall()]
                    
                    required_tables = ['user', 'category', 'product', 'customer', 'invoice', 'invoice_item', 'stock_movement']
                    missing_tables = []
                    
                    for table in required_tables:
                        if table not in tables:
                            missing_tables.append(table)
                        else:
                            # فحص بنية الجدول
                            try:
                                cursor.execute(f"PRAGMA table_info({table})")
                                columns = cursor.fetchall()
                                if not columns:
                                    self.log_error("Database", f"جدول {table} فارغ من الأعمدة", "high")
                                else:
                                    print(f"  ✅ جدول {table} ({len(columns)} عمود)")
                                    
                                    # فحص عدد السجلات
                                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                                    count = cursor.fetchone()[0]
                                    print(f"    📊 {count} سجل")
                                    
                                    if table == 'user' and count == 0:
                                        self.log_error("Database", "لا يوجد مستخدمين في النظام", "high",
                                                     "تشغيل python init_db.py لإنشاء المستخدم الافتراضي")
                                        
                            except Exception as e:
                                self.log_error("Database", f"خطأ في فحص جدول {table}: {e}", "medium")
                    
                    if missing_tables:
                        self.log_error("Database", f"جداول مفقودة: {', '.join(missing_tables)}", "high",
                                     "تشغيل python init_db.py لإنشاء الجداول")
                    else:
                        print("✅ جميع الجداول المطلوبة موجودة")
                        self.test_results['database'] = True
                    
                    conn.close()
                    
                except Exception as e:
                    self.log_error("Database", f"خطأ في الاتصال بقاعدة البيانات: {e}", "high")
                    self.test_results['database'] = False
                
                break
        
        if not db_found:
            self.log_error("Database", "لم يتم العثور على قاعدة البيانات", "high",
                         "تشغيل python init_db.py لإنشاء قاعدة البيانات")
            self.test_results['database'] = False
    
    def test_all_pages(self):
        """فحص جميع الصفحات"""
        print("\n📄 فحص جميع الصفحات...")
        
        pages = [
            ('/', 'لوحة التحكم'),
            ('/products', 'المنتجات'),
            ('/products/add', 'إضافة منتج'),
            ('/customers', 'العملاء'),
            ('/sales', 'المبيعات'),
            ('/sales/new', 'فاتورة جديدة'),
            ('/users', 'المستخدمين'),
            ('/users/add', 'إضافة مستخدم'),
            ('/categories', 'التصنيفات'),
            ('/reports', 'التقارير')
        ]
        
        failed_pages = []
        
        for url, name in pages:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                if response.status_code == 200:
                    print(f"  ✅ {name}")
                    
                    # فحص وجود أخطاء JavaScript في الصفحة
                    if 'error' in response.text.lower() or 'exception' in response.text.lower():
                        self.log_warning("Pages", f"قد توجد أخطاء في صفحة {name}")
                        
                elif response.status_code == 403:
                    print(f"  ⚠️  {name} - ممنوع (قد يكون طبيعي حسب الصلاحيات)")
                else:
                    self.log_error("Pages", f"صفحة {name} لا تعمل - كود {response.status_code}", "medium")
                    failed_pages.append(name)
                    
            except Exception as e:
                self.log_error("Pages", f"خطأ في صفحة {name}: {e}", "medium")
                failed_pages.append(name)
        
        if not failed_pages:
            print("✅ جميع الصفحات تعمل بشكل صحيح")
            self.test_results['pages'] = True
        else:
            self.test_results['pages'] = False
    
    def test_api_endpoints(self):
        """فحص نقاط API"""
        print("\n🔌 فحص نقاط API...")
        
        apis = [
            ('/api/products/search?q=test', 'البحث في المنتجات'),
            ('/api/customers/search?q=test', 'البحث في العملاء'),
            ('/api/reports/sales', 'تقرير المبيعات'),
        ]
        
        failed_apis = []
        
        for url, name in apis:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                if response.status_code == 200:
                    try:
                        data = response.json()
                        print(f"  ✅ {name} - يعيد JSON صحيح")
                    except json.JSONDecodeError:
                        self.log_error("API", f"API {name} لا يعيد JSON صحيح", "medium")
                        failed_apis.append(name)
                else:
                    self.log_error("API", f"API {name} لا يعمل - كود {response.status_code}", "medium")
                    failed_apis.append(name)
                    
            except Exception as e:
                self.log_error("API", f"خطأ في API {name}: {e}", "medium")
                failed_apis.append(name)
        
        if not failed_apis:
            print("✅ جميع نقاط API تعمل بشكل صحيح")
            self.test_results['api'] = True
        else:
            self.test_results['api'] = False
    
    def test_static_files(self):
        """فحص الملفات الثابتة"""
        print("\n📁 فحص الملفات الثابتة...")
        
        static_files = [
            ('/static/css/style.css', 'ملف CSS الرئيسي'),
            ('/static/js/app.js', 'ملف JavaScript الرئيسي'),
            ('/static/manifest.json', 'PWA Manifest'),
            ('/static/sw.js', 'Service Worker'),
            ('/static/images/icon-144x144.png', 'أيقونة PWA'),
            ('/static/images/icon-192x192.png', 'أيقونة PWA كبيرة'),
            ('/static/images/icon-512x512.png', 'أيقونة PWA كبيرة جداً')
        ]
        
        missing_files = []
        
        for url, name in static_files:
            try:
                response = self.session.get(f"{self.base_url}{url}")
                if response.status_code in [200, 304]:
                    print(f"  ✅ {name}")
                else:
                    self.log_warning("Static Files", f"{name} غير متوفر")
                    missing_files.append(name)
                    
            except Exception as e:
                self.log_warning("Static Files", f"خطأ في تحميل {name}: {e}")
                missing_files.append(name)
        
        if not missing_files:
            print("✅ جميع الملفات الثابتة متوفرة")
            self.test_results['static_files'] = True
        else:
            self.test_results['static_files'] = False
    
    def test_security(self):
        """فحص الأمان"""
        print("\n🔒 فحص الأمان...")
        
        # إنشاء جلسة جديدة بدون تسجيل دخول
        test_session = requests.Session()
        
        protected_routes = [
            '/products', '/customers', '/sales', '/users', '/reports'
        ]
        
        security_issues = []
        
        for route in protected_routes:
            try:
                response = test_session.get(f"{self.base_url}{route}", allow_redirects=False)
                if response.status_code == 302:
                    location = response.headers.get('Location', '')
                    if 'login' in location.lower():
                        print(f"  ✅ {route} محمي بشكل صحيح")
                    else:
                        self.log_warning("Security", f"مسار {route} محمي لكن إعادة التوجيه غير واضحة")
                elif response.status_code in [401, 403]:
                    print(f"  ✅ {route} محمي (كود {response.status_code})")
                else:
                    self.log_error("Security", f"مسار {route} قد يكون غير محمي", "high")
                    security_issues.append(route)
                    
            except Exception as e:
                self.log_warning("Security", f"خطأ في فحص الحماية لـ {route}: {e}")
        
        if not security_issues:
            print("✅ جميع المسارات محمية بشكل صحيح")
            self.test_results['security'] = True
        else:
            self.test_results['security'] = False
    
    def test_functionality(self):
        """فحص الوظائف الأساسية"""
        print("\n⚙️ فحص الوظائف الأساسية...")
        
        try:
            # اختبار إضافة عميل جديد
            customer_data = {
                'name': f'عميل اختبار {datetime.now().strftime("%H%M%S")}',
                'phone': '0501234567',
                'email': f'test_{datetime.now().strftime("%H%M%S")}@example.com'
            }
            
            response = self.session.post(f"{self.base_url}/api/customers", 
                                       json=customer_data,
                                       headers={'Content-Type': 'application/json'})
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("  ✅ إضافة عميل جديد يعمل")
                    self.test_results['add_customer'] = True
                else:
                    self.log_error("Functionality", "فشل في إضافة عميل جديد", "medium")
                    self.test_results['add_customer'] = False
            else:
                self.log_error("Functionality", f"خطأ في إضافة عميل - كود {response.status_code}", "medium")
                self.test_results['add_customer'] = False
                
        except Exception as e:
            self.log_error("Functionality", f"خطأ في اختبار إضافة عميل: {e}", "medium")
            self.test_results['add_customer'] = False
    
    def run_comprehensive_check(self):
        """تشغيل الفحص الشامل"""
        print("🔍 بدء الفحص الشامل للتطبيق")
        print("=" * 60)
        print(f"🕐 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # تشغيل جميع الفحوصات
        checks = [
            self.test_basic_connectivity,
            self.test_authentication,
            self.test_database_integrity,
            self.test_all_pages,
            self.test_api_endpoints,
            self.test_static_files,
            self.test_security,
            self.test_functionality
        ]
        
        for check in checks:
            try:
                check()
            except Exception as e:
                self.log_error("Test Error", f"خطأ في تشغيل الفحص: {e}", "high")
                print(f"💥 خطأ في الفحص: {e}")
                traceback.print_exc()
        
        # عرض النتائج النهائية
        self.show_results()
        
        # تطبيق الإصلاحات إذا أمكن
        if self.errors:
            self.apply_fixes()
    
    def apply_fixes(self):
        """تطبيق الإصلاحات التلقائية"""
        print("\n🔧 تطبيق الإصلاحات التلقائية...")
        
        for error in self.errors:
            if error['fix_suggestion']:
                print(f"\n💡 اقتراح إصلاح لـ: {error['description']}")
                print(f"   {error['fix_suggestion']}")
                
                # تطبيق بعض الإصلاحات التلقائية
                if "python init_db.py" in error['fix_suggestion']:
                    print("   🔄 تشغيل init_db.py...")
                    try:
                        import subprocess
                        result = subprocess.run(['python', 'init_db.py'], 
                                              capture_output=True, text=True, cwd='.')
                        if result.returncode == 0:
                            self.log_fix("تم إنشاء/تحديث قاعدة البيانات")
                        else:
                            print(f"   ❌ فشل في تشغيل init_db.py: {result.stderr}")
                    except Exception as e:
                        print(f"   ❌ خطأ في تشغيل init_db.py: {e}")
    
    def show_results(self):
        """عرض نتائج الفحص"""
        print("\n" + "=" * 60)
        print("📊 نتائج الفحص الشامل")
        print("=" * 60)
        
        # إحصائيات الاختبارات
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result)
        
        print(f"\n📈 إحصائيات الاختبارات:")
        print(f"  ✅ نجح: {passed_tests}/{total_tests}")
        print(f"  ❌ فشل: {total_tests - passed_tests}/{total_tests}")
        if total_tests > 0:
            print(f"  📊 معدل النجاح: {(passed_tests/total_tests)*100:.1f}%")
        
        # عرض الأخطاء
        if self.errors:
            print(f"\n❌ الأخطاء المكتشفة ({len(self.errors)}):")
            for error in self.errors:
                severity_icon = {
                    'high': '🔴',
                    'medium': '🟡',
                    'low': '🟢'
                }.get(error['severity'], '⚪')
                
                print(f"  {severity_icon} [{error['category']}] {error['description']}")
        else:
            print("\n✅ لم يتم اكتشاف أي أخطاء!")
        
        # عرض التحذيرات
        if self.warnings:
            print(f"\n⚠️  التحذيرات ({len(self.warnings)}):")
            for warning in self.warnings:
                print(f"  🟡 [{warning['category']}] {warning['description']}")
        else:
            print("\n✅ لا توجد تحذيرات!")
        
        # عرض الإصلاحات المطبقة
        if self.fixes_applied:
            print(f"\n🔧 الإصلاحات المطبقة ({len(self.fixes_applied)}):")
            for fix in self.fixes_applied:
                print(f"  ✅ {fix['description']}")
        
        # التقييم النهائي
        high_errors = len([e for e in self.errors if e['severity'] == 'high'])
        medium_errors = len([e for e in self.errors if e['severity'] == 'medium'])
        
        print(f"\n🎯 التقييم النهائي:")
        if high_errors == 0 and medium_errors == 0:
            print("🎉 التطبيق في حالة ممتازة! لا توجد أخطاء.")
            return True
        elif high_errors == 0 and medium_errors <= 3:
            print("✅ التطبيق في حالة جيدة مع بعض التحسينات المطلوبة.")
            return True
        else:
            print("⚠️  التطبيق يحتاج لإصلاحات قبل الاستخدام.")
            return False

def main():
    """الوظيفة الرئيسية"""
    checker = ComprehensiveErrorChecker()
    success = checker.run_comprehensive_check()
    return success

if __name__ == "__main__":
    main()
