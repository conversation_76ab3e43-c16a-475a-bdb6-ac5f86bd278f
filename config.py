"""
إعدادات التطبيق
"""
import os
from datetime import timedelta

class Config:
    """الإعدادات الأساسية للتطبيق"""
    
    # إعدادات Flask الأساسية
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-in-production'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///sales_inventory.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_RECORD_QUERIES = True
    
    # إعدادات الجلسة
    PERMANENT_SESSION_LIFETIME = timedelta(hours=24)
    SESSION_COOKIE_SECURE = False  # True في الإنتاج مع HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    
    # إعدادات التطبيق
    COMPANY_NAME = "شركة المبيعات المتقدمة"
    COMPANY_ADDRESS = "الرياض، المملكة العربية السعودية"
    COMPANY_PHONE = "+966 11 123 4567"
    COMPANY_EMAIL = "<EMAIL>"
    COMPANY_TAX_NUMBER = "123456789012345"
    
    # إعدادات الضرائب
    TAX_RATE = 0.15  # 15% ضريبة القيمة المضافة
    
    # إعدادات الفواتير
    INVOICE_PREFIX = "INV"
    INVOICE_NUMBER_LENGTH = 4
    
    # إعدادات التصدير
    EXPORT_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'exports')
    MAX_EXPORT_RECORDS = 10000
    
    # إعدادات الرفع
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'pdf', 'xlsx', 'csv'}
    
    # إعدادات البريد الإلكتروني (للمستقبل)
    MAIL_SERVER = os.environ.get('MAIL_SERVER')
    MAIL_PORT = int(os.environ.get('MAIL_PORT') or 587)
    MAIL_USE_TLS = os.environ.get('MAIL_USE_TLS', 'true').lower() in ['true', 'on', '1']
    MAIL_USERNAME = os.environ.get('MAIL_USERNAME')
    MAIL_PASSWORD = os.environ.get('MAIL_PASSWORD')
    
    # إعدادات التخزين السحابي (للمستقبل)
    CLOUD_STORAGE_ENABLED = False
    AWS_ACCESS_KEY_ID = os.environ.get('AWS_ACCESS_KEY_ID')
    AWS_SECRET_ACCESS_KEY = os.environ.get('AWS_SECRET_ACCESS_KEY')
    AWS_S3_BUCKET = os.environ.get('AWS_S3_BUCKET')
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600  # ساعة واحدة
    
    # إعدادات التسجيل
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = 'app.log'
    
    @staticmethod
    def init_app(app):
        """تهيئة التطبيق مع الإعدادات"""
        # إنشاء المجلدات المطلوبة
        os.makedirs(Config.EXPORT_FOLDER, exist_ok=True)
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)

class DevelopmentConfig(Config):
    """إعدادات بيئة التطوير"""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('DEV_DATABASE_URL') or 'sqlite:///sales_inventory_dev.db'

class TestingConfig(Config):
    """إعدادات بيئة الاختبار"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = os.environ.get('TEST_DATABASE_URL') or 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

class ProductionConfig(Config):
    """إعدادات بيئة الإنتاج"""
    DEBUG = False
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///sales_inventory.db'
    SESSION_COOKIE_SECURE = True
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # تسجيل الأخطاء في الإنتاج
        import logging
        from logging.handlers import RotatingFileHandler
        
        if not app.debug:
            file_handler = RotatingFileHandler(
                'logs/sales_app.log', 
                maxBytes=10240000, 
                backupCount=10
            )
            file_handler.setFormatter(logging.Formatter(
                '%(asctime)s %(levelname)s: %(message)s [in %(pathname)s:%(lineno)d]'
            ))
            file_handler.setLevel(logging.INFO)
            app.logger.addHandler(file_handler)
            
            app.logger.setLevel(logging.INFO)
            app.logger.info('Sales App startup')

# خريطة الإعدادات
config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}

# إعدادات الأدوار والصلاحيات
ROLES = {
    'admin': {
        'name': 'مدير',
        'permissions': [
            'view_dashboard',
            'manage_products',
            'manage_categories',
            'manage_customers',
            'create_invoices',
            'view_invoices',
            'cancel_invoices',
            'return_invoices',
            'manage_users',
            'view_reports',
            'export_data',
            'manage_settings'
        ]
    },
    'seller': {
        'name': 'بائع',
        'permissions': [
            'view_dashboard',
            'view_products',
            'view_customers',
            'create_invoices',
            'view_invoices',
            'print_invoices'
        ]
    },
    'warehouse_manager': {
        'name': 'مشرف مخزن',
        'permissions': [
            'view_dashboard',
            'manage_products',
            'manage_categories',
            'view_customers',
            'view_stock_movements',
            'adjust_inventory',
            'view_reports'
        ]
    }
}

# إعدادات طرق الدفع
PAYMENT_METHODS = {
    'cash': {
        'name': 'نقدي',
        'icon': 'fas fa-money-bill',
        'color': 'success'
    },
    'card': {
        'name': 'بطاقة',
        'icon': 'fas fa-credit-card',
        'color': 'primary'
    },
    'credit': {
        'name': 'آجل',
        'icon': 'fas fa-clock',
        'color': 'warning'
    }
}

# إعدادات حالات الفواتير
INVOICE_STATUSES = {
    'completed': {
        'name': 'مكتملة',
        'icon': 'fas fa-check-circle',
        'color': 'success'
    },
    'pending': {
        'name': 'معلقة',
        'icon': 'fas fa-clock',
        'color': 'warning'
    },
    'cancelled': {
        'name': 'ملغية',
        'icon': 'fas fa-times-circle',
        'color': 'danger'
    }
}

# إعدادات أنواع حركات المخزون
STOCK_MOVEMENT_TYPES = {
    'in': {
        'name': 'وارد',
        'icon': 'fas fa-arrow-down',
        'color': 'success'
    },
    'out': {
        'name': 'صادر',
        'icon': 'fas fa-arrow-up',
        'color': 'danger'
    },
    'adjustment': {
        'name': 'تعديل',
        'icon': 'fas fa-edit',
        'color': 'warning'
    }
}

# إعدادات التقارير
REPORT_TYPES = {
    'sales': {
        'name': 'تقرير المبيعات',
        'icon': 'fas fa-chart-line',
        'description': 'تقرير شامل عن المبيعات والفواتير'
    },
    'inventory': {
        'name': 'تقرير المخزون',
        'icon': 'fas fa-boxes',
        'description': 'تقرير عن حالة المخزون والكميات'
    },
    'customers': {
        'name': 'تقرير العملاء',
        'icon': 'fas fa-users',
        'description': 'تقرير عن العملاء ومعاملاتهم'
    },
    'products': {
        'name': 'تقرير المنتجات',
        'icon': 'fas fa-box',
        'description': 'تقرير عن المنتجات والمبيعات'
    }
}

# إعدادات الوحدات
PRODUCT_UNITS = [
    'قطعة',
    'كيلو',
    'لتر',
    'متر',
    'علبة',
    'كرتون',
    'حبة',
    'زجاجة',
    'كيس',
    'عبوة'
]

# إعدادات التصدير
EXPORT_FORMATS = {
    'excel': {
        'name': 'Excel',
        'extension': 'xlsx',
        'mime_type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    },
    'pdf': {
        'name': 'PDF',
        'extension': 'pdf',
        'mime_type': 'application/pdf'
    },
    'csv': {
        'name': 'CSV',
        'extension': 'csv',
        'mime_type': 'text/csv'
    }
}
