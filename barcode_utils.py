#!/usr/bin/env python3
"""
وحدة الباركود - إنشاء وقراءة الباركود
Barcode Module - Generate and Read Barcodes
"""

import os
import io
import base64
import random
import string
from datetime import datetime
from PIL import Image, ImageDraw, ImageFont
import barcode
from barcode import Code128, EAN13, Code39
from barcode.writer import ImageWriter
import qrcode

class BarcodeGenerator:
    """مولد الباركود"""
    
    def __init__(self):
        self.barcode_dir = 'static/barcodes'
        self.ensure_barcode_directory()
    
    def ensure_barcode_directory(self):
        """التأكد من وجود مجلد الباركود"""
        if not os.path.exists(self.barcode_dir):
            os.makedirs(self.barcode_dir)
    
    def generate_barcode_number(self, product_id=None):
        """توليد رقم باركود فريد"""
        if product_id:
            # استخدام معرف المنتج مع timestamp
            timestamp = str(int(datetime.now().timestamp()))[-6:]
            return f"2{product_id:06d}{timestamp}"
        else:
            # توليد رقم عشوائي
            return ''.join([str(random.randint(0, 9)) for _ in range(13)])
    
    def create_code128_barcode(self, code, product_name="", save_file=True):
        """إنشاء باركود Code128"""
        try:
            # إنشاء الباركود
            barcode_class = barcode.get_barcode_class('code128')
            barcode_instance = barcode_class(code, writer=ImageWriter())
            
            # إعدادات الكتابة
            options = {
                'module_width': 0.2,
                'module_height': 15.0,
                'quiet_zone': 6.5,
                'font_size': 10,
                'text_distance': 5.0,
                'background': 'white',
                'foreground': 'black',
            }
            
            # إنشاء الصورة
            buffer = io.BytesIO()
            barcode_instance.write(buffer, options)
            buffer.seek(0)
            
            # إضافة اسم المنتج
            if product_name:
                img = Image.open(buffer)
                img = self.add_product_name_to_barcode(img, product_name)
                
                # حفظ الصورة المحدثة
                if save_file:
                    filename = f"{code}_code128.png"
                    filepath = os.path.join(self.barcode_dir, filename)
                    img.save(filepath)
                    return filepath, self.image_to_base64(img)
                else:
                    return None, self.image_to_base64(img)
            else:
                if save_file:
                    filename = f"{code}_code128.png"
                    filepath = os.path.join(self.barcode_dir, filename)
                    with open(filepath, 'wb') as f:
                        f.write(buffer.getvalue())
                    return filepath, self.buffer_to_base64(buffer)
                else:
                    return None, self.buffer_to_base64(buffer)
                    
        except Exception as e:
            print(f"خطأ في إنشاء باركود Code128: {e}")
            return None, None
    
    def create_ean13_barcode(self, code, product_name="", save_file=True):
        """إنشاء باركود EAN13"""
        try:
            # التأكد من أن الكود 12 رقم (EAN13 يضيف رقم التحقق تلقائياً)
            if len(code) == 13:
                code = code[:12]
            elif len(code) != 12:
                # إضافة أصفار في البداية أو قطع الزائد
                code = code.zfill(12)[:12]
            
            # إنشاء الباركود
            barcode_class = barcode.get_barcode_class('ean13')
            barcode_instance = barcode_class(code, writer=ImageWriter())
            
            # إعدادات الكتابة
            options = {
                'module_width': 0.2,
                'module_height': 15.0,
                'quiet_zone': 6.5,
                'font_size': 10,
                'text_distance': 5.0,
                'background': 'white',
                'foreground': 'black',
            }
            
            # إنشاء الصورة
            buffer = io.BytesIO()
            barcode_instance.write(buffer, options)
            buffer.seek(0)
            
            # إضافة اسم المنتج
            if product_name:
                img = Image.open(buffer)
                img = self.add_product_name_to_barcode(img, product_name)
                
                if save_file:
                    filename = f"{barcode_instance.get_fullcode()}_ean13.png"
                    filepath = os.path.join(self.barcode_dir, filename)
                    img.save(filepath)
                    return filepath, self.image_to_base64(img)
                else:
                    return None, self.image_to_base64(img)
            else:
                if save_file:
                    filename = f"{barcode_instance.get_fullcode()}_ean13.png"
                    filepath = os.path.join(self.barcode_dir, filename)
                    with open(filepath, 'wb') as f:
                        f.write(buffer.getvalue())
                    return filepath, self.buffer_to_base64(buffer)
                else:
                    return None, self.buffer_to_base64(buffer)
                    
        except Exception as e:
            print(f"خطأ في إنشاء باركود EAN13: {e}")
            return None, None
    
    def create_qr_code(self, data, product_name="", save_file=True):
        """إنشاء رمز QR"""
        try:
            # إنشاء رمز QR
            qr = qrcode.QRCode(
                version=1,
                error_correction=qrcode.constants.ERROR_CORRECT_L,
                box_size=10,
                border=4,
            )
            qr.add_data(data)
            qr.make(fit=True)
            
            # إنشاء الصورة
            img = qr.make_image(fill_color="black", back_color="white")
            
            # إضافة اسم المنتج
            if product_name:
                img = self.add_product_name_to_qr(img, product_name)
            
            if save_file:
                filename = f"qr_{data[:20]}.png"
                # إزالة الأحرف غير المسموحة من اسم الملف
                filename = "".join(c for c in filename if c.isalnum() or c in "._-")
                filepath = os.path.join(self.barcode_dir, filename)
                img.save(filepath)
                return filepath, self.image_to_base64(img)
            else:
                return None, self.image_to_base64(img)
                
        except Exception as e:
            print(f"خطأ في إنشاء رمز QR: {e}")
            return None, None
    
    def add_product_name_to_barcode(self, img, product_name):
        """إضافة اسم المنتج أسفل الباركود"""
        try:
            # إنشاء صورة جديدة أكبر
            new_height = img.height + 40
            new_img = Image.new('RGB', (img.width, new_height), 'white')
            new_img.paste(img, (0, 0))
            
            # إضافة النص
            draw = ImageDraw.Draw(new_img)
            
            # محاولة استخدام خط عربي
            try:
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 12)
            except:
                font = ImageFont.load_default()
            
            # حساب موضع النص
            text_bbox = draw.textbbox((0, 0), product_name, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_x = (img.width - text_width) // 2
            text_y = img.height + 10
            
            # رسم النص
            draw.text((text_x, text_y), product_name, fill='black', font=font)
            
            return new_img
        except Exception as e:
            print(f"خطأ في إضافة اسم المنتج: {e}")
            return img
    
    def add_product_name_to_qr(self, img, product_name):
        """إضافة اسم المنتج أسفل رمز QR"""
        try:
            # إنشاء صورة جديدة أكبر
            new_height = img.height + 40
            new_img = Image.new('RGB', (img.width, new_height), 'white')
            new_img.paste(img, (0, 0))
            
            # إضافة النص
            draw = ImageDraw.Draw(new_img)
            
            try:
                font = ImageFont.truetype("/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf", 14)
            except:
                font = ImageFont.load_default()
            
            # حساب موضع النص
            text_bbox = draw.textbbox((0, 0), product_name, font=font)
            text_width = text_bbox[2] - text_bbox[0]
            text_x = (img.width - text_width) // 2
            text_y = img.height + 10
            
            # رسم النص
            draw.text((text_x, text_y), product_name, fill='black', font=font)
            
            return new_img
        except Exception as e:
            print(f"خطأ في إضافة اسم المنتج لرمز QR: {e}")
            return img
    
    def image_to_base64(self, img):
        """تحويل الصورة إلى base64"""
        try:
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            buffer.seek(0)
            return base64.b64encode(buffer.getvalue()).decode()
        except Exception as e:
            print(f"خطأ في تحويل الصورة إلى base64: {e}")
            return None
    
    def buffer_to_base64(self, buffer):
        """تحويل buffer إلى base64"""
        try:
            buffer.seek(0)
            return base64.b64encode(buffer.getvalue()).decode()
        except Exception as e:
            print(f"خطأ في تحويل buffer إلى base64: {e}")
            return None
    
    def generate_product_barcode(self, product_id, product_name, barcode_type='code128'):
        """إنشاء باركود للمنتج"""
        try:
            # توليد رقم الباركود
            barcode_number = self.generate_barcode_number(product_id)
            
            if barcode_type == 'code128':
                filepath, base64_data = self.create_code128_barcode(barcode_number, product_name)
            elif barcode_type == 'ean13':
                filepath, base64_data = self.create_ean13_barcode(barcode_number, product_name)
            elif barcode_type == 'qr':
                # بيانات رمز QR تتضمن معلومات المنتج
                qr_data = f"PRODUCT:{product_id}|NAME:{product_name}|BARCODE:{barcode_number}"
                filepath, base64_data = self.create_qr_code(qr_data, product_name)
            else:
                return None, None, None
            
            return barcode_number, filepath, base64_data
            
        except Exception as e:
            print(f"خطأ في إنشاء باركود المنتج: {e}")
            return None, None, None
    
    def create_barcode_labels(self, products, labels_per_row=3):
        """إنشاء ملصقات باركود متعددة للطباعة"""
        try:
            if not products:
                return None, None
            
            # إعدادات الملصق
            label_width = 200
            label_height = 100
            margin = 10
            
            # حساب أبعاد الصورة النهائية
            rows = (len(products) + labels_per_row - 1) // labels_per_row
            total_width = (label_width + margin) * labels_per_row - margin
            total_height = (label_height + margin) * rows - margin
            
            # إنشاء صورة كبيرة
            final_img = Image.new('RGB', (total_width, total_height), 'white')
            
            for i, product in enumerate(products):
                # إنشاء باركود للمنتج
                if product.get('barcode'):
                    _, base64_data = self.create_code128_barcode(
                        product['barcode'], 
                        product['name'], 
                        save_file=False
                    )
                    
                    if base64_data:
                        # تحويل base64 إلى صورة
                        barcode_data = base64.b64decode(base64_data)
                        barcode_img = Image.open(io.BytesIO(barcode_data))
                        
                        # تغيير حجم الباركود ليناسب الملصق
                        barcode_img = barcode_img.resize((label_width - 20, label_height - 20))
                        
                        # حساب موضع الملصق
                        row = i // labels_per_row
                        col = i % labels_per_row
                        x = col * (label_width + margin) + 10
                        y = row * (label_height + margin) + 10
                        
                        # لصق الباركود
                        final_img.paste(barcode_img, (x, y))
            
            # حفظ الصورة النهائية
            filename = f"barcode_labels_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            filepath = os.path.join(self.barcode_dir, filename)
            final_img.save(filepath)
            
            return filepath, self.image_to_base64(final_img)
            
        except Exception as e:
            print(f"خطأ في إنشاء ملصقات الباركود: {e}")
            return None, None

# إنشاء مثيل عام للاستخدام
barcode_generator = BarcodeGenerator()
