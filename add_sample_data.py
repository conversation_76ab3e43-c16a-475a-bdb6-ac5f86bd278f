#!/usr/bin/env python3
"""
إضافة البيانات التجريبية للتطبيق
"""

import sqlite3
from datetime import datetime

def add_sample_data():
    """إضافة البيانات التجريبية"""
    try:
        conn = sqlite3.connect('instance/sales_inventory.db')
        cursor = conn.cursor()

        # فحص وإضافة المنتجات
        cursor.execute("SELECT COUNT(*) FROM product")
        products_count = cursor.fetchone()[0]

        if products_count == 0:
            print("إضافة منتجات تجريبية...")
            products = [
                ('هاتف ذكي سامسونج', 1, '123456789', 800.0, 1200.0, 50, 10, 'قطعة', 'هاتف ذكي متطور'),
                ('لابتوب ديل', 1, '987654321', 2000.0, 3000.0, 20, 5, 'قطعة', 'لابتوب للأعمال'),
                ('قميص قطني رجالي', 2, '111222333', 30.0, 60.0, 100, 20, 'قطعة', 'قميص قطني عالي الجودة'),
                ('فستان نسائي', 2, '444555666', 80.0, 150.0, 50, 10, 'قطعة', 'فستان أنيق'),
                ('أرز بسمتي', 3, '777888999', 15.0, 25.0, 200, 50, 'كيس', 'أرز بسمتي فاخر'),
                ('زيت زيتون', 3, '111333555', 25.0, 40.0, 100, 20, 'زجاجة', 'زيت زيتون بكر'),
                ('مكنسة كهربائية', 4, '222444666', 150.0, 250.0, 30, 5, 'قطعة', 'مكنسة كهربائية قوية'),
                ('طقم أواني طبخ', 4, '333555777', 200.0, 350.0, 25, 5, 'طقم', 'طقم أواني من الستانلس ستيل')
            ]

            for product in products:
                cursor.execute("""
                    INSERT INTO product (name, category_id, barcode, purchase_price, selling_price,
                                       quantity, min_quantity, unit, description, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, product + (datetime.now(),))

            print(f"تم إضافة {len(products)} منتج")

        # فحص وإضافة العملاء
        cursor.execute("SELECT COUNT(*) FROM customer")
        customers_count = cursor.fetchone()[0]

        if customers_count == 0:
            print("إضافة عملاء تجريبيين...")
            customers = [
                ('أحمد محمد', '0501234567', '<EMAIL>', 'الرياض، حي النخيل', 5000.0),
                ('فاطمة علي', '0509876543', '<EMAIL>', 'جدة، حي الصفا', 3000.0),
                ('محمد السعد', '0507654321', None, 'الدمام، حي الشاطئ', 2000.0),
                ('نورا الأحمد', '0503456789', '<EMAIL>', 'مكة، حي العزيزية', 4000.0)
            ]

            for customer in customers:
                cursor.execute("""
                    INSERT INTO customer (name, phone, email, address, credit_limit, created_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, customer + (datetime.now(),))

            print(f"تم إضافة {len(customers)} عميل")

        # حفظ التغييرات
        conn.commit()

        # عرض الإحصائيات النهائية
        cursor.execute("SELECT COUNT(*) FROM product")
        products_final = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM customer")
        customers_final = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM category")
        categories_final = cursor.fetchone()[0]

        cursor.execute("SELECT COUNT(*) FROM user")
        users_final = cursor.fetchone()[0]

        conn.close()

        print("\n📊 إحصائيات البيانات النهائية:")
        print(f"  👥 المستخدمين: {users_final}")
        print(f"  📂 التصنيفات: {categories_final}")
        print(f"  📦 المنتجات: {products_final}")
        print(f"  🤝 العملاء: {customers_final}")

        print("\n✅ تم إضافة جميع البيانات التجريبية بنجاح!")
        return True

    except Exception as e:
        print(f"❌ خطأ في إضافة البيانات: {e}")
        return False

if __name__ == "__main__":
    add_sample_data()
