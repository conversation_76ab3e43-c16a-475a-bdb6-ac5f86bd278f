#!/usr/bin/env python3
"""
تحديث قاعدة البيانات لإضافة حقل last_login للمستخدمين
"""

import sqlite3
from datetime import datetime

def update_database():
    """تحديث قاعدة البيانات"""
    try:
        # الاتصال بقاعدة البيانات
        conn = sqlite3.connect('sales_inventory.db')
        cursor = conn.cursor()

        # عرض جميع الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print("الجداول الموجودة:", [table[0] for table in tables])

        # البحث عن جدول المستخدمين
        user_table = None
        for table in tables:
            if 'user' in table[0].lower():
                user_table = table[0]
                break

        if not user_table:
            print("❌ لم يتم العثور على جدول المستخدمين")
            return False

        print(f"جدول المستخدمين: {user_table}")

        # التحقق من وجود العمود
        cursor.execute(f"PRAGMA table_info({user_table})")
        columns = [column[1] for column in cursor.fetchall()]
        print("الأعمدة الموجودة:", columns)

        if 'last_login' not in columns:
            print("إضافة عمود last_login...")
            cursor.execute(f"ALTER TABLE {user_table} ADD COLUMN last_login DATETIME")
            print("✅ تم إضافة عمود last_login بنجاح")
        else:
            print("✅ عمود last_login موجود بالفعل")

        # حفظ التغييرات
        conn.commit()
        conn.close()

        print("✅ تم تحديث قاعدة البيانات بنجاح")
        return True

    except Exception as e:
        print(f"❌ خطأ في تحديث قاعدة البيانات: {e}")
        return False

if __name__ == "__main__":
    print("🔄 بدء تحديث قاعدة البيانات...")
    success = update_database()

    if success:
        print("🎉 تم تحديث قاعدة البيانات بنجاح!")
    else:
        print("💥 فشل في تحديث قاعدة البيانات")
