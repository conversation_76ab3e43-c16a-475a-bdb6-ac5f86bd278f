#!/usr/bin/env python3
"""
وحدة حساب الأرباح - Profit Calculator
حساب الأرباح اليومية والشهرية والسنوية
"""

from datetime import datetime, timedelta, date
from sqlalchemy import func, and_, extract
from models import db, Invoice, InvoiceItem, Product
from collections import defaultdict
import calendar

class ProfitCalculator:
    """حاسبة الأرباح"""
    
    def __init__(self):
        self.currency_symbol = "د.ل"  # الدينار الليبي
    
    def calculate_item_profit(self, invoice_item):
        """حساب ربح عنصر فاتورة واحد"""
        try:
            product = invoice_item.product
            if not product:
                return 0
            
            # ربح الوحدة = سعر البيع - سعر الشراء
            unit_profit = invoice_item.unit_price - product.purchase_price
            
            # إجمالي الربح = ربح الوحدة × الكمية
            total_profit = unit_profit * invoice_item.quantity
            
            return total_profit
        except Exception as e:
            print(f"خطأ في حساب ربح العنصر: {e}")
            return 0
    
    def get_daily_profit(self, target_date=None):
        """حساب الأرباح اليومية"""
        if target_date is None:
            target_date = date.today()
        
        # تحويل التاريخ إلى datetime للمقارنة
        start_date = datetime.combine(target_date, datetime.min.time())
        end_date = datetime.combine(target_date, datetime.max.time())
        
        # جلب الفواتير لهذا اليوم
        invoices = Invoice.query.filter(
            and_(
                Invoice.created_at >= start_date,
                Invoice.created_at <= end_date,
                Invoice.status == 'completed'
            )
        ).all()
        
        total_profit = 0
        total_sales = 0
        total_cost = 0
        items_sold = 0
        profit_details = []
        
        for invoice in invoices:
            invoice_profit = 0
            invoice_cost = 0
            
            for item in invoice.items:
                item_profit = self.calculate_item_profit(item)
                item_cost = item.product.purchase_price * item.quantity if item.product else 0
                
                invoice_profit += item_profit
                invoice_cost += item_cost
                items_sold += item.quantity
                
                profit_details.append({
                    'invoice_id': invoice.id,
                    'invoice_number': invoice.invoice_number,
                    'product_name': item.product.name if item.product else 'غير معروف',
                    'quantity': item.quantity,
                    'unit_price': item.unit_price,
                    'purchase_price': item.product.purchase_price if item.product else 0,
                    'unit_profit': item.unit_price - (item.product.purchase_price if item.product else 0),
                    'total_profit': item_profit,
                    'created_at': invoice.created_at
                })
            
            total_profit += invoice_profit
            total_cost += invoice_cost
            total_sales += invoice.final_amount
        
        # حساب هامش الربح
        profit_margin = (total_profit / total_sales * 100) if total_sales > 0 else 0
        
        return {
            'date': target_date,
            'total_profit': total_profit,
            'total_sales': total_sales,
            'total_cost': total_cost,
            'profit_margin': profit_margin,
            'items_sold': items_sold,
            'invoices_count': len(invoices),
            'details': profit_details,
            'currency': self.currency_symbol
        }
    
    def get_monthly_profit(self, year=None, month=None):
        """حساب الأرباح الشهرية"""
        if year is None:
            year = datetime.now().year
        if month is None:
            month = datetime.now().month
        
        # أول وآخر يوم في الشهر
        first_day = date(year, month, 1)
        last_day = date(year, month, calendar.monthrange(year, month)[1])
        
        start_date = datetime.combine(first_day, datetime.min.time())
        end_date = datetime.combine(last_day, datetime.max.time())
        
        # جلب الفواتير للشهر
        invoices = Invoice.query.filter(
            and_(
                Invoice.created_at >= start_date,
                Invoice.created_at <= end_date,
                Invoice.status == 'completed'
            )
        ).all()
        
        # حساب الأرباح اليومية للشهر
        daily_profits = {}
        current_date = first_day
        
        while current_date <= last_day:
            daily_data = self.get_daily_profit(current_date)
            daily_profits[current_date.strftime('%Y-%m-%d')] = daily_data
            current_date += timedelta(days=1)
        
        # حساب الإجماليات
        total_profit = sum(day['total_profit'] for day in daily_profits.values())
        total_sales = sum(day['total_sales'] for day in daily_profits.values())
        total_cost = sum(day['total_cost'] for day in daily_profits.values())
        total_items = sum(day['items_sold'] for day in daily_profits.values())
        total_invoices = sum(day['invoices_count'] for day in daily_profits.values())
        
        profit_margin = (total_profit / total_sales * 100) if total_sales > 0 else 0
        
        # أفضل وأسوأ الأيام
        best_day = max(daily_profits.values(), key=lambda x: x['total_profit']) if daily_profits else None
        worst_day = min(daily_profits.values(), key=lambda x: x['total_profit']) if daily_profits else None
        
        return {
            'year': year,
            'month': month,
            'month_name': calendar.month_name[month],
            'total_profit': total_profit,
            'total_sales': total_sales,
            'total_cost': total_cost,
            'profit_margin': profit_margin,
            'items_sold': total_items,
            'invoices_count': total_invoices,
            'daily_profits': daily_profits,
            'best_day': best_day,
            'worst_day': worst_day,
            'average_daily_profit': total_profit / len(daily_profits) if daily_profits else 0,
            'currency': self.currency_symbol
        }
    
    def get_yearly_profit(self, year=None):
        """حساب الأرباح السنوية"""
        if year is None:
            year = datetime.now().year
        
        # بداية ونهاية السنة
        start_date = datetime(year, 1, 1)
        end_date = datetime(year, 12, 31, 23, 59, 59)
        
        # حساب الأرباح الشهرية للسنة
        monthly_profits = {}
        
        for month in range(1, 13):
            monthly_data = self.get_monthly_profit(year, month)
            monthly_profits[month] = monthly_data
        
        # حساب الإجماليات
        total_profit = sum(month['total_profit'] for month in monthly_profits.values())
        total_sales = sum(month['total_sales'] for month in monthly_profits.values())
        total_cost = sum(month['total_cost'] for month in monthly_profits.values())
        total_items = sum(month['items_sold'] for month in monthly_profits.values())
        total_invoices = sum(month['invoices_count'] for month in monthly_profits.values())
        
        profit_margin = (total_profit / total_sales * 100) if total_sales > 0 else 0
        
        # أفضل وأسوأ الشهور
        best_month = max(monthly_profits.values(), key=lambda x: x['total_profit']) if monthly_profits else None
        worst_month = min(monthly_profits.values(), key=lambda x: x['total_profit']) if monthly_profits else None
        
        # نمو الأرباح (مقارنة مع السنة السابقة)
        previous_year_data = self.get_yearly_profit(year - 1) if year > 2020 else None
        growth_rate = 0
        if previous_year_data and previous_year_data['total_profit'] > 0:
            growth_rate = ((total_profit - previous_year_data['total_profit']) / 
                          previous_year_data['total_profit'] * 100)
        
        return {
            'year': year,
            'total_profit': total_profit,
            'total_sales': total_sales,
            'total_cost': total_cost,
            'profit_margin': profit_margin,
            'items_sold': total_items,
            'invoices_count': total_invoices,
            'monthly_profits': monthly_profits,
            'best_month': best_month,
            'worst_month': worst_month,
            'average_monthly_profit': total_profit / 12,
            'growth_rate': growth_rate,
            'previous_year_profit': previous_year_data['total_profit'] if previous_year_data else 0,
            'currency': self.currency_symbol
        }
    
    def get_profit_trends(self, days=30):
        """تحليل اتجاهات الأرباح"""
        end_date = date.today()
        start_date = end_date - timedelta(days=days-1)
        
        trends = []
        current_date = start_date
        
        while current_date <= end_date:
            daily_data = self.get_daily_profit(current_date)
            trends.append({
                'date': current_date.strftime('%Y-%m-%d'),
                'profit': daily_data['total_profit'],
                'sales': daily_data['total_sales'],
                'margin': daily_data['profit_margin']
            })
            current_date += timedelta(days=1)
        
        # حساب المتوسطات المتحركة
        if len(trends) >= 7:
            for i in range(6, len(trends)):
                week_profits = [trends[j]['profit'] for j in range(i-6, i+1)]
                trends[i]['moving_average_7'] = sum(week_profits) / 7
        
        return trends
    
    def get_top_profitable_products(self, limit=10, period='month'):
        """أكثر المنتجات ربحية"""
        # تحديد الفترة
        if period == 'day':
            start_date = datetime.combine(date.today(), datetime.min.time())
            end_date = datetime.combine(date.today(), datetime.max.time())
        elif period == 'month':
            today = date.today()
            start_date = datetime.combine(date(today.year, today.month, 1), datetime.min.time())
            end_date = datetime.combine(today, datetime.max.time())
        elif period == 'year':
            today = date.today()
            start_date = datetime.combine(date(today.year, 1, 1), datetime.min.time())
            end_date = datetime.combine(today, datetime.max.time())
        else:
            # آخر 30 يوم
            end_date = datetime.combine(date.today(), datetime.max.time())
            start_date = end_date - timedelta(days=30)
        
        # جلب عناصر الفواتير في الفترة المحددة
        items = db.session.query(InvoiceItem, Product, Invoice).join(
            Product, InvoiceItem.product_id == Product.id
        ).join(
            Invoice, InvoiceItem.invoice_id == Invoice.id
        ).filter(
            and_(
                Invoice.created_at >= start_date,
                Invoice.created_at <= end_date,
                Invoice.status == 'completed'
            )
        ).all()
        
        # حساب الأرباح لكل منتج
        product_profits = defaultdict(lambda: {
            'total_profit': 0,
            'total_quantity': 0,
            'total_sales': 0,
            'product_name': '',
            'product_id': 0
        })
        
        for item, product, invoice in items:
            profit = self.calculate_item_profit(item)
            product_profits[product.id]['total_profit'] += profit
            product_profits[product.id]['total_quantity'] += item.quantity
            product_profits[product.id]['total_sales'] += item.total_price
            product_profits[product.id]['product_name'] = product.name
            product_profits[product.id]['product_id'] = product.id
        
        # ترتيب حسب الربح
        sorted_products = sorted(
            product_profits.values(),
            key=lambda x: x['total_profit'],
            reverse=True
        )[:limit]
        
        return sorted_products

# إنشاء مثيل عام للاستخدام
profit_calculator = ProfitCalculator()
