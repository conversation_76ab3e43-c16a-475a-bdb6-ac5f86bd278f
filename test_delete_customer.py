#!/usr/bin/env python3
"""
اختبار وظيفة حذف العميل
"""

import requests
import json

def test_delete_customer():
    """اختبار حذف العميل"""
    session = requests.Session()
    base_url = "http://127.0.0.1:5000"
    
    # تسجيل الدخول
    print("🔐 تسجيل الدخول...")
    login_data = {'username': 'admin', 'password': 'admin123'}
    response = session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
    
    if response.status_code != 302:
        print("❌ فشل تسجيل الدخول")
        return False
    
    print("✅ تم تسجيل الدخول بنجاح")
    
    # الحصول على قائمة العملاء
    print("\n📋 الحصول على قائمة العملاء...")
    response = session.get(f"{base_url}/api/customers/search?q=")
    
    if response.status_code != 200:
        print(f"❌ فشل الحصول على العملاء: {response.status_code}")
        return False
    
    customers = response.json()
    print(f"✅ تم العثور على {len(customers)} عميل")
    
    if len(customers) == 0:
        print("⚠️  لا توجد عملاء للحذف")
        return False
    
    # عرض العملاء
    print("\n👥 العملاء المتاحين:")
    for i, customer in enumerate(customers):
        print(f"  {i+1}. {customer['name']} (ID: {customer['id']})")
    
    # اختيار آخر عميل للحذف (عادة عميل الاختبار)
    customer_to_delete = customers[-1]
    customer_id = customer_to_delete['id']
    customer_name = customer_to_delete['name']
    
    print(f"\n🗑️ محاولة حذف العميل: {customer_name} (ID: {customer_id})")
    
    # محاولة حذف العميل
    response = session.post(f"{base_url}/customers/{customer_id}/delete")
    
    print(f"📡 استجابة الخادم: {response.status_code}")
    
    try:
        result = response.json()
        print(f"📄 محتوى الاستجابة: {json.dumps(result, ensure_ascii=False, indent=2)}")
        
        if response.status_code == 200 and result.get('success'):
            print(f"✅ تم حذف العميل {customer_name} بنجاح!")
            
            # التحقق من الحذف
            print("\n🔍 التحقق من الحذف...")
            response = session.get(f"{base_url}/api/customers/search?q=")
            new_customers = response.json()
            
            if len(new_customers) == len(customers) - 1:
                print("✅ تم تأكيد الحذف - انخفض عدد العملاء")
                return True
            else:
                print("❌ لم يتم الحذف فعلياً")
                return False
                
        else:
            print(f"❌ فشل حذف العميل: {result.get('error', 'خطأ غير معروف')}")
            return False
            
    except json.JSONDecodeError:
        print(f"❌ استجابة غير صحيحة: {response.text}")
        return False

def test_delete_protection():
    """اختبار حماية حذف العميل الذي له فواتير"""
    session = requests.Session()
    base_url = "http://127.0.0.1:5000"
    
    # تسجيل الدخول
    login_data = {'username': 'admin', 'password': 'admin123'}
    session.post(f"{base_url}/login", data=login_data, allow_redirects=False)
    
    print("\n🛡️ اختبار حماية حذف العميل الذي له فواتير...")
    
    # محاولة حذف العميل الأول (قد يكون له فواتير)
    response = session.post(f"{base_url}/customers/1/delete")
    
    if response.status_code == 400:
        result = response.json()
        print(f"✅ الحماية تعمل: {result.get('error')}")
        return True
    elif response.status_code == 200:
        print("⚠️  تم حذف العميل (قد لا يكون له فواتير)")
        return True
    else:
        print(f"❌ خطأ غير متوقع: {response.status_code}")
        return False

if __name__ == "__main__":
    print("🧪 اختبار وظيفة حذف العميل")
    print("=" * 50)
    
    # اختبار الحذف العادي
    success1 = test_delete_customer()
    
    # اختبار الحماية
    success2 = test_delete_protection()
    
    print("\n" + "=" * 50)
    print("📊 نتائج الاختبار:")
    print(f"  حذف العميل: {'✅ نجح' if success1 else '❌ فشل'}")
    print(f"  حماية الحذف: {'✅ تعمل' if success2 else '❌ لا تعمل'}")
    
    if success1 and success2:
        print("\n🎉 جميع اختبارات حذف العميل نجحت!")
    else:
        print("\n⚠️  بعض الاختبارات فشلت")
