#!/usr/bin/env python3
"""
فحص نهائي شامل للتأكد من أن التطبيق يعمل بشكل مثالي
"""

import requests
import json
import sqlite3
from datetime import datetime

class FinalVerification:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "http://127.0.0.1:5000"
        self.tests_passed = 0
        self.tests_total = 0
        
    def test(self, description, test_func):
        """تشغيل اختبار وتسجيل النتيجة"""
        self.tests_total += 1
        try:
            result = test_func()
            if result:
                print(f"✅ {description}")
                self.tests_passed += 1
                return True
            else:
                print(f"❌ {description}")
                return False
        except Exception as e:
            print(f"❌ {description} - خطأ: {e}")
            return False
    
    def login(self):
        """تسجيل الدخول"""
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = self.session.post(f"{self.base_url}/login", data=login_data, allow_redirects=False)
        return response.status_code == 302
    
    def test_data_exists(self):
        """فحص وجود البيانات التجريبية"""
        try:
            conn = sqlite3.connect('instance/sales_inventory.db')
            cursor = conn.cursor()
            
            # فحص المنتجات
            cursor.execute("SELECT COUNT(*) FROM product")
            products_count = cursor.fetchone()[0]
            
            # فحص العملاء
            cursor.execute("SELECT COUNT(*) FROM customer")
            customers_count = cursor.fetchone()[0]
            
            # فحص التصنيفات
            cursor.execute("SELECT COUNT(*) FROM category")
            categories_count = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"  📊 المنتجات: {products_count}")
            print(f"  📊 العملاء: {customers_count}")
            print(f"  📊 التصنيفات: {categories_count}")
            
            return products_count > 0 and customers_count > 0 and categories_count > 0
            
        except Exception as e:
            print(f"  ❌ خطأ في فحص البيانات: {e}")
            return False
    
    def test_create_invoice(self):
        """اختبار إنشاء فاتورة كاملة"""
        try:
            # بيانات الفاتورة
            invoice_data = {
                "customer_id": 1,  # أول عميل
                "payment_method": "cash",
                "discount": 0,
                "tax": 0,
                "notes": "فاتورة اختبار",
                "items": [
                    {
                        "product_id": 1,  # أول منتج
                        "quantity": 2,
                        "unit_price": 100.00
                    }
                ]
            }
            
            response = self.session.post(
                f"{self.base_url}/api/invoices",
                json=invoice_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    invoice_id = result.get('invoice_id')
                    print(f"  📄 تم إنشاء فاتورة رقم: {result.get('invoice_number')}")
                    return True
            
            return False
            
        except Exception as e:
            print(f"  ❌ خطأ في إنشاء الفاتورة: {e}")
            return False
    
    def test_search_functionality(self):
        """اختبار وظائف البحث"""
        try:
            # البحث في المنتجات
            response = self.session.get(f"{self.base_url}/api/products/search?q=هاتف")
            if response.status_code == 200:
                products = response.json()
                print(f"  🔍 البحث في المنتجات: وجد {len(products)} نتيجة")
                
                # البحث في العملاء
                response = self.session.get(f"{self.base_url}/api/customers/search?q=أحمد")
                if response.status_code == 200:
                    customers = response.json()
                    print(f"  🔍 البحث في العملاء: وجد {len(customers)} نتيجة")
                    return True
            
            return False
            
        except Exception as e:
            print(f"  ❌ خطأ في البحث: {e}")
            return False
    
    def test_user_management(self):
        """اختبار إدارة المستخدمين"""
        try:
            # إضافة مستخدم جديد
            user_data = {
                'username': f'testuser_{datetime.now().strftime("%H%M%S")}',
                'email': f'test_{datetime.now().strftime("%H%M%S")}@example.com',
                'password': 'testpass123',
                'role': 'seller'
            }
            
            response = self.session.post(f"{self.base_url}/users/add", data=user_data)
            
            if response.status_code in [200, 302]:
                print(f"  👤 تم إضافة مستخدم: {user_data['username']}")
                return True
            
            return False
            
        except Exception as e:
            print(f"  ❌ خطأ في إدارة المستخدمين: {e}")
            return False
    
    def test_reports(self):
        """اختبار التقارير"""
        try:
            response = self.session.get(f"{self.base_url}/api/reports/sales")
            if response.status_code == 200:
                report = response.json()
                print(f"  📊 تقرير المبيعات: إجمالي {report.get('total_sales', 0)} ر.س")
                return True
            
            return False
            
        except Exception as e:
            print(f"  ❌ خطأ في التقارير: {e}")
            return False
    
    def test_pwa_features(self):
        """اختبار ميزات PWA"""
        try:
            # فحص Service Worker
            response = self.session.get(f"{self.base_url}/static/sw.js")
            sw_works = response.status_code == 200
            
            # فحص Manifest
            response = self.session.get(f"{self.base_url}/static/manifest.json")
            manifest_works = response.status_code == 200
            
            if sw_works and manifest_works:
                print("  📱 Service Worker و Manifest متوفران")
                return True
            
            return False
            
        except Exception as e:
            print(f"  ❌ خطأ في PWA: {e}")
            return False
    
    def run_final_verification(self):
        """تشغيل الفحص النهائي الشامل"""
        print("🔍 بدء الفحص النهائي الشامل")
        print("=" * 60)
        print(f"🕐 الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 60)
        
        # تسجيل الدخول
        if not self.login():
            print("❌ فشل تسجيل الدخول")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح\n")
        
        # تشغيل جميع الاختبارات
        print("📋 تشغيل الاختبارات الشاملة:")
        
        self.test("وجود البيانات التجريبية", self.test_data_exists)
        self.test("إنشاء فاتورة جديدة", self.test_create_invoice)
        self.test("وظائف البحث", self.test_search_functionality)
        self.test("إدارة المستخدمين", self.test_user_management)
        self.test("التقارير", self.test_reports)
        self.test("ميزات PWA", self.test_pwa_features)
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print("📊 نتائج الفحص النهائي")
        print("=" * 60)
        
        success_rate = (self.tests_passed / self.tests_total) * 100 if self.tests_total > 0 else 0
        
        print(f"✅ اختبارات نجحت: {self.tests_passed}/{self.tests_total}")
        print(f"📈 معدل النجاح: {success_rate:.1f}%")
        
        if success_rate == 100:
            print("\n🎉 التطبيق يعمل بشكل مثالي!")
            print("🏆 جاهز للاستخدام الإنتاجي!")
            return True
        elif success_rate >= 80:
            print("\n✅ التطبيق يعمل بشكل جيد مع بعض التحسينات")
            return True
        else:
            print("\n⚠️  التطبيق يحتاج لمزيد من الإصلاحات")
            return False

def main():
    """الوظيفة الرئيسية"""
    verifier = FinalVerification()
    success = verifier.run_final_verification()
    return success

if __name__ == "__main__":
    main()
