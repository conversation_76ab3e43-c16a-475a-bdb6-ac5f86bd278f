# 🚀 كيفية تشغيل التطبيق - أسهل الطرق

## 🎯 **الطريقة الأسهل - نقرة واحدة!**

### 🖱️ **الطريقة الأولى: نقر مزدوج**
1. **انقر نقراً مزدوجاً** على ملف `start_app.sh`
2. **انتظر** حتى يظهر "التطبيق يعمل الآن!"
3. **افتح المتصفح** على: http://localhost:5000
4. **سجل الدخول** بـ: admin / admin123

### 🖥️ **الطريقة الثانية: اختصار سطح المكتب**
1. **انقر نقراً مزدوجاً** على `تطبيق_المبيعات.desktop`
2. **اختر "تشغيل"** إذا ظهر تحذير
3. **انتظر** حتى يفتح التطبيق
4. **افتح المتصفح** على: http://localhost:5000

### ⌨️ **الطريقة الثالثة: سطر الأوامر**
```bash
cd /home/<USER>/سطح\ المكتب/lly
./start_app.sh
```

---

## 📱 **معلومات الدخول**

### 🔑 **بيانات تسجيل الدخول:**
- **الرابط:** http://localhost:5000
- **المستخدم:** admin
- **كلمة المرور:** admin123

### 👥 **المستخدمين المتاحين:**
- **admin** - مدير النظام (جميع الصلاحيات)

---

## 🛠️ **إذا واجهت مشاكل**

### ❌ **المشكلة: "Permission denied"**
**الحل:**
```bash
chmod +x start_app.sh
```

### ❌ **المشكلة: "venv not found"**
**الحل:**
```bash
python3 -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### ❌ **المشكلة: "Port already in use"**
**الحل:**
```bash
# إيقاف العملية المستخدمة للمنفذ
sudo lsof -ti:5000 | xargs kill -9
```

### ❌ **المشكلة: "Database not found"**
**الحل:**
```bash
python init_db.py
python add_sample_data.py
```

---

## 🌐 **الوصول من أجهزة أخرى**

### 📱 **من الهاتف أو جهاز آخر في نفس الشبكة:**
1. **اعرف عنوان IP** للجهاز:
   ```bash
   ip addr show | grep inet
   ```
2. **افتح المتصفح** على: http://[عنوان-IP]:5000
   مثال: http://*************:5000

---

## ⏹️ **إيقاف التطبيق**

### 🛑 **طرق الإيقاف:**
1. **اضغط Ctrl+C** في نافذة Terminal
2. **أغلق نافذة Terminal**
3. **أعد تشغيل الجهاز**

---

## 🎉 **نصائح للاستخدام**

### 💡 **نصائح مهمة:**
1. **احتفظ بنافذة Terminal مفتوحة** أثناء الاستخدام
2. **لا تغلق المتصفح** - يمكنك فتح علامات تبويب جديدة
3. **احفظ الرابط في المفضلة:** http://localhost:5000
4. **يمكن تثبيت التطبيق كـ PWA** من المتصفح

### 📊 **البيانات التجريبية المتوفرة:**
- **8 منتجات** جاهزة للاستخدام
- **4 عملاء** تجريبيين
- **4 تصنيفات** للمنتجات
- **فواتير تجريبية** للاختبار

### 🔄 **إعادة تعيين البيانات:**
```bash
rm instance/sales_inventory.db
python init_db.py
python add_sample_data.py
```

---

## 📞 **الدعم**

### 🆘 **إذا احتجت مساعدة:**
1. **تحقق من ملف السجل:** logs/error.log
2. **أعد تشغيل التطبيق**
3. **تأكد من تثبيت Python 3.8+**

---

## 🎯 **الخلاصة**

### ✅ **للتشغيل السريع:**
**انقر نقراً مزدوجاً على `start_app.sh` وافتح http://localhost:5000**

**🎉 استمتع باستخدام التطبيق!**
