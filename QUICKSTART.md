# 🚀 دليل البدء السريع

## التشغيل السريع

### 1. تفعيل البيئة الافتراضية
```bash
source venv/bin/activate
```

### 2. تشغيل التطبيق
```bash
python run.py
```

### 3. فتح المتصفح
افتح المتصفح واذهب إلى: `http://localhost:5000`

### 4. تسجيل الدخول
- **اسم المستخدم:** admin
- **كلمة المرور:** admin123

---

## 🎯 الميزات الجاهزة للاستخدام

### ✅ ما يعمل الآن:
- ✅ تسجيل الدخول والخروج
- ✅ لوحة التحكم مع الإحصائيات
- ✅ إدارة المنتجات (عرض، إضافة)
- ✅ إدارة العملاء (عرض، إضافة)
- ✅ إدارة التصنيفات
- ✅ إنشاء فواتير المبيعات
- ✅ طباعة الفواتير
- ✅ عرض قائمة المبيعات
- ✅ التقارير الأساسية
- ✅ البحث في المنتجات والعملاء
- ✅ PWA - يعمل أوفلاين
- ✅ تصميم متجاوب للهواتف

### 🔄 قيد التطوير:
- 🔄 تعديل وحذف المنتجات
- 🔄 تعديل وحذف العملاء
- 🔄 إدارة المستخدمين
- 🔄 تقارير متقدمة
- 🔄 تصدير البيانات
- 🔄 إدارة المرتجعات
- 🔄 إدارة المخزون المتقدمة

---

## 📱 استخدام التطبيق

### إضافة منتج جديد:
1. اذهب إلى "المنتجات"
2. اضغط "إضافة منتج جديد"
3. املأ البيانات واحفظ

### إنشاء فاتورة:
1. اذهب إلى "المبيعات"
2. اضغط "فاتورة جديدة"
3. اختر العميل (اختياري)
4. ابحث عن المنتجات وأضفها
5. احفظ الفاتورة

### طباعة فاتورة:
1. من قائمة المبيعات
2. اضغط على أيقونة الطباعة
3. ستفتح نافذة الطباعة

---

## 🔧 إعدادات مهمة

### تغيير معلومات الشركة:
عدّل الملف `config.py`:
```python
COMPANY_NAME = "اسم شركتك"
COMPANY_ADDRESS = "عنوان الشركة"
COMPANY_PHONE = "رقم الهاتف"
COMPANY_EMAIL = "البريد الإلكتروني"
COMPANY_TAX_NUMBER = "الرقم الضريبي"
```

### إضافة مستخدم جديد:
```bash
python -c "
from app import app, db
from models import User
with app.app_context():
    user = User(username='اسم_المستخدم', email='<EMAIL>', role='seller')
    user.set_password('كلمة_المرور')
    db.session.add(user)
    db.session.commit()
    print('تم إنشاء المستخدم')
"
```

---

## 🛠️ استكشاف الأخطاء

### المشكلة: التطبيق لا يعمل
**الحل:**
```bash
# تأكد من تفعيل البيئة الافتراضية
source venv/bin/activate

# تأكد من تثبيت المتطلبات
pip install -r requirements.txt

# تشغيل التطبيق
python run.py
```

### المشكلة: خطأ في قاعدة البيانات
**الحل:**
```bash
# إعادة تهيئة قاعدة البيانات
python init_db.py
```

### المشكلة: لا يمكن تسجيل الدخول
**الحل:**
- تأكد من البيانات: admin / admin123
- أو أعد تهيئة قاعدة البيانات

---

## 📞 الدعم

### للمساعدة:
1. راجع ملف `README.md` للتفاصيل الكاملة
2. تحقق من ملفات السجل في حالة الأخطاء
3. تأكد من تثبيت جميع المتطلبات

### ملفات مهمة:
- `app.py` - التطبيق الرئيسي
- `models.py` - نماذج قاعدة البيانات
- `config.py` - الإعدادات
- `utils.py` - الوظائف المساعدة
- `init_db.py` - تهيئة قاعدة البيانات

---

## 🎉 استمتع بالاستخدام!

التطبيق جاهز للاستخدام مع البيانات التجريبية. يمكنك البدء فوراً في:
- إضافة منتجاتك الحقيقية
- إضافة عملائك
- إنشاء فواتير المبيعات
- متابعة التقارير

**نصيحة:** ابدأ بإضافة منتجاتك وعملائك الحقيقيين لتحصل على أقصى استفادة من التطبيق.
