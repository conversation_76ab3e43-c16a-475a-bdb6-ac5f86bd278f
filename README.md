# 🏪 نظام إدارة المبيعات والمخازن

تطبيق ويب شامل لإدارة المبيعات والمخازن يعمل أوفلاين باستخدام تقنيات PWA (Progressive Web App) مع Python و Flask.

## ✨ الميزات الرئيسية

### 📦 إدارة المخازن
- ✅ إضافة وتعديل وحذف المنتجات
- ✅ تصنيفات للمنتجات
- ✅ تتبع الكميات بالمخزون
- ✅ إشعارات عند انخفاض الكمية تحت الحد الأدنى
- ✅ سجل الحركات (الواردات والصادرات)
- ✅ باركود للمنتجات

### 💰 إدارة المبيعات
- ✅ تسجيل الفواتير (بيع – مرتجع)
- ✅ طباعة الفواتير
- ✅ إدارة العملاء
- ✅ طرق دفع متعددة (نقدي، بطاقة، آجل)
- ✅ تقارير المبيعات اليومية، الأسبوعية، الشهرية
- ✅ حساب الضرائب والخصومات

### 👤 إدارة المستخدمين والصلاحيات
- ✅ تسجيل دخول آمن
- ✅ صلاحيات متعددة (مدير – بائع – مشرف مخزن)
- ✅ تتبع العمليات حسب المستخدم

### 📊 تقارير وتحليلات
- ✅ مخزون حالي
- ✅ مبيعات حسب المنتج
- ✅ أرباح وخسائر
- ✅ إحصائيات تفاعلية

### 🌐 PWA - العمل أوفلاين
- ✅ يعمل بدون اتصال إنترنت
- ✅ تثبيت التطبيق على الجهاز
- ✅ مزامنة البيانات عند الاتصال
- ✅ تخزين محلي للبيانات

## 🛠️ التقنيات المستخدمة

### Backend
- **Python 3.x**
- **Flask** - إطار العمل الرئيسي
- **SQLAlchemy** - ORM لقاعدة البيانات
- **Flask-Login** - إدارة المصادقة
- **SQLite** - قاعدة البيانات المحلية

### Frontend
- **HTML5/CSS3/JavaScript**
- **Bootstrap 5 RTL** - التصميم المتجاوب
- **Font Awesome** - الأيقونات
- **Google Fonts (Cairo)** - الخطوط العربية

### PWA
- **Service Worker** - العمل أوفلاين
- **Web App Manifest** - تثبيت التطبيق
- **IndexedDB** - التخزين المحلي

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.8 أو أحدث
- pip (مدير حزم Python)

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone <repository-url>
cd sales-inventory-app
```

2. **إنشاء بيئة افتراضية**
```bash
python3 -m venv venv
source venv/bin/activate  # على Linux/Mac
# أو
venv\Scripts\activate  # على Windows
```

3. **تثبيت المتطلبات**
```bash
pip install -r requirements.txt
```

4. **تشغيل التطبيق**
```bash
python app.py
```

5. **فتح المتصفح**
```
http://localhost:5000
```

## 🔐 بيانات الدخول الافتراضية

- **اسم المستخدم:** admin
- **كلمة المرور:** admin123
- **الصلاحية:** مدير

## 📱 استخدام التطبيق

### تسجيل الدخول
1. افتح التطبيق في المتصفح
2. أدخل بيانات الدخول الافتراضية
3. ستنتقل إلى لوحة التحكم الرئيسية

### إضافة منتج جديد
1. اذهب إلى "المنتجات" من القائمة
2. اضغط على "إضافة منتج جديد"
3. املأ البيانات المطلوبة
4. احفظ المنتج

### إنشاء فاتورة مبيعات
1. اذهب إلى "المبيعات" من القائمة
2. اضغط على "فاتورة جديدة"
3. اختر العميل (أو اتركه فارغاً للعميل النقدي)
4. ابحث عن المنتجات وأضفها
5. احفظ الفاتورة

### طباعة الفاتورة
1. من قائمة المبيعات، اضغط على أيقونة الطباعة
2. ستفتح نافذة جديدة بتصميم الفاتورة
3. اضغط على "طباعة" أو Ctrl+P

## 🔧 الإعدادات المتقدمة

### تخصيص معلومات الشركة
عدّل الملف `templates/print_invoice.html` لتغيير:
- اسم الشركة
- العنوان
- أرقام الهواتف
- الرقم الضريبي

### إضافة مستخدمين جدد
```python
# في Python shell أو ملف منفصل
from app import app, db
from models import User

with app.app_context():
    user = User(
        username='اسم_المستخدم',
        email='<EMAIL>',
        role='seller'  # أو 'admin' أو 'warehouse_manager'
    )
    user.set_password('كلمة_المرور')
    db.session.add(user)
    db.session.commit()
```

## 📂 هيكل المشروع

```
sales-inventory-app/
├── app.py                 # التطبيق الرئيسي
├── models.py              # نماذج قاعدة البيانات
├── requirements.txt       # المتطلبات
├── README.md             # هذا الملف
├── templates/            # قوالب HTML
│   ├── base.html
│   ├── login.html
│   ├── dashboard.html
│   ├── products.html
│   ├── add_product.html
│   ├── customers.html
│   ├── sales.html
│   ├── new_sale.html
│   └── print_invoice.html
├── static/               # الملفات الثابتة
│   ├── css/
│   │   └── style.css
│   ├── js/
│   │   └── app.js
│   ├── sw.js            # Service Worker
│   └── manifest.json    # Web App Manifest
└── venv/                # البيئة الافتراضية
```

## 🔒 الأمان

- كلمات المرور مشفرة باستخدام Werkzeug
- جلسات آمنة مع Flask-Login
- حماية من CSRF
- التحقق من الصلاحيات لكل عملية

## 🌟 الميزات المستقبلية

- [ ] تكامل مع ماسح الباركود
- [ ] تصدير التقارير إلى Excel/PDF
- [ ] إشعارات Push
- [ ] تطبيق موبايل
- [ ] تكامل مع أنظمة الدفع الإلكتروني
- [ ] نسخ احتياطي تلقائي
- [ ] تقارير متقدمة مع الرسوم البيانية

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إجراء التغييرات
4. إرسال Pull Request

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف LICENSE للتفاصيل.

## 📞 الدعم

للدعم والاستفسارات:
- إنشاء Issue في GitHub
- التواصل عبر البريد الإلكتروني

---

**تم تطوير هذا المشروع بواسطة Augment Agent** 🤖

*نظام شامل لإدارة المبيعات والمخازن يجمع بين البساطة والقوة*
