# 🚀 دليل النشر الإنتاجي

## 📋 **متطلبات النشر**

### 🖥️ **متطلبات الخادم:**
- **نظام التشغيل:** Linux (Ubuntu 20.04+ مفضل)
- **Python:** 3.8 أو أحدث
- **الذاكرة:** 1GB RAM على الأقل
- **التخزين:** 5GB مساحة فارغة على الأقل
- **الشبكة:** اتصال إنترنت مستقر

### 🔧 **البرامج المطلوبة:**
```bash
# تحديث النظام
sudo apt update && sudo apt upgrade -y

# تثبيت Python و pip
sudo apt install python3 python3-pip python3-venv -y

# تثبيت Nginx (خادم ويب عكسي)
sudo apt install nginx -y

# تثبيت Supervisor (إدارة العمليات)
sudo apt install supervisor -y
```

---

## 🚀 **خطوات النشر**

### 1️⃣ **تحضير الخادم**

```bash
# إنشاء مستخدم للتطبيق
sudo adduser salesapp
sudo usermod -aG sudo salesapp

# التبديل للمستخدم الجديد
su - salesapp

# إنشاء مجلد التطبيق
mkdir -p /home/<USER>/sales-inventory-app
cd /home/<USER>/sales-inventory-app
```

### 2️⃣ **رفع ملفات التطبيق**

```bash
# نسخ ملفات التطبيق إلى الخادم
# يمكن استخدام scp أو git clone

# إنشاء البيئة الافتراضية
python3 -m venv venv
source venv/bin/activate

# تثبيت التبعيات
pip install -r requirements.txt

# تثبيت خادم إنتاجي
pip install gunicorn
```

### 3️⃣ **إعداد متغيرات البيئة**

```bash
# نسخ ملف البيئة
cp .env.production .env

# تعديل المتغيرات
nano .env

# تعيين مفتاح أمان قوي
export SECRET_KEY=$(python3 -c 'import secrets; print(secrets.token_hex(32))')
echo "SECRET_KEY=$SECRET_KEY" >> .env
```

### 4️⃣ **إعداد قاعدة البيانات**

```bash
# تشغيل التطبيق لإنشاء قاعدة البيانات
python3 wsgi.py &
sleep 5
pkill -f wsgi.py

# أو تشغيل سكريبت التهيئة
python3 init_db.py
```

### 5️⃣ **إعداد Gunicorn**

```bash
# إنشاء ملف إعداد Gunicorn
cat > gunicorn.conf.py << 'EOF'
# إعدادات Gunicorn للإنتاج

# عدد العمليات
workers = 4

# نوع العامل
worker_class = "sync"

# عدد الاتصالات لكل عامل
worker_connections = 1000

# المهلة الزمنية
timeout = 30

# الاستماع
bind = "127.0.0.1:8000"

# ملف PID
pidfile = "/tmp/gunicorn.pid"

# ملفات السجل
accesslog = "/home/<USER>/sales-inventory-app/logs/access.log"
errorlog = "/home/<USER>/sales-inventory-app/logs/error.log"
loglevel = "info"

# إعادة تشغيل العمليات
max_requests = 1000
max_requests_jitter = 100

# الأمان
user = "salesapp"
group = "salesapp"
EOF

# إنشاء مجلد السجلات
mkdir -p logs
```

### 6️⃣ **إعداد Supervisor**

```bash
# إنشاء ملف إعداد Supervisor
sudo tee /etc/supervisor/conf.d/salesapp.conf << 'EOF'
[program:salesapp]
command=/home/<USER>/sales-inventory-app/venv/bin/gunicorn -c gunicorn.conf.py wsgi:app
directory=/home/<USER>/sales-inventory-app
user=salesapp
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/home/<USER>/sales-inventory-app/logs/supervisor.log
environment=FLASK_ENV=production
EOF

# إعادة تحميل Supervisor
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start salesapp
```

### 7️⃣ **إعداد Nginx**

```bash
# إنشاء ملف إعداد Nginx
sudo tee /etc/nginx/sites-available/salesapp << 'EOF'
server {
    listen 80;
    server_name your-domain.com;  # غير هذا لنطاقك

    # إعادة توجيه HTTP إلى HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;  # غير هذا لنطاقك

    # شهادات SSL (استخدم Let's Encrypt)
    ssl_certificate /etc/ssl/certs/salesapp.crt;
    ssl_certificate_key /etc/ssl/private/salesapp.key;

    # إعدادات SSL
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    ssl_prefer_server_ciphers off;

    # الملفات الثابتة
    location /static {
        alias /home/<USER>/sales-inventory-app/static;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # التطبيق الرئيسي
    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # مهلة زمنية
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # ضغط الملفات
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types text/plain text/css text/xml text/javascript application/javascript application/xml+rss application/json;

    # الأمان
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
}
EOF

# تفعيل الموقع
sudo ln -s /etc/nginx/sites-available/salesapp /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 8️⃣ **إعداد شهادة SSL (Let's Encrypt)**

```bash
# تثبيت Certbot
sudo apt install certbot python3-certbot-nginx -y

# الحصول على شهادة SSL
sudo certbot --nginx -d your-domain.com

# تجديد تلقائي
sudo crontab -e
# إضافة السطر التالي:
# 0 12 * * * /usr/bin/certbot renew --quiet
```

### 9️⃣ **إعداد النسخ الاحتياطية**

```bash
# إنشاء سكريبت النسخ الاحتياطية
cat > backup_script.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/home/<USER>/backups"
DATE=$(date +%Y%m%d_%H%M%S)
APP_DIR="/home/<USER>/sales-inventory-app"

# إنشاء مجلد النسخ الاحتياطية
mkdir -p $BACKUP_DIR

# نسخ احتياطية لقاعدة البيانات
cp $APP_DIR/instance/sales_inventory.db $BACKUP_DIR/db_backup_$DATE.db

# نسخ احتياطية للملفات المرفوعة
tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz -C $APP_DIR uploads/

# حذف النسخ القديمة (أكثر من 30 يوم)
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete

echo "Backup completed: $DATE"
EOF

chmod +x backup_script.sh

# إضافة مهمة cron للنسخ الاحتياطية اليومية
crontab -e
# إضافة السطر التالي:
# 0 2 * * * /home/<USER>/sales-inventory-app/backup_script.sh
```

---

## 🔒 **إعدادات الأمان الإضافية**

### 🛡️ **جدار الحماية**

```bash
# تفعيل UFW
sudo ufw enable

# السماح بـ SSH
sudo ufw allow ssh

# السماح بـ HTTP و HTTPS
sudo ufw allow 'Nginx Full'

# حالة الجدار
sudo ufw status
```

### 🔐 **تحديث كلمات المرور**

```bash
# تغيير كلمة مرور المدير الافتراضية
python3 -c "
from app import app
from models import db, User
with app.app_context():
    admin = User.query.filter_by(username='admin').first()
    if admin:
        admin.set_password('new-secure-password-here')
        db.session.commit()
        print('تم تحديث كلمة مرور المدير')
"
```

---

## 📊 **المراقبة والصيانة**

### 📈 **مراقبة الأداء**

```bash
# مراقبة العمليات
sudo supervisorctl status

# مراقبة السجلات
tail -f logs/error.log
tail -f logs/access.log

# مراقبة استخدام الموارد
htop
df -h
```

### 🔄 **التحديثات**

```bash
# إيقاف التطبيق
sudo supervisorctl stop salesapp

# تحديث الكود
git pull origin main

# تحديث التبعيات
source venv/bin/activate
pip install -r requirements.txt

# إعادة تشغيل التطبيق
sudo supervisorctl start salesapp
```

---

## 🆘 **استكشاف الأخطاء**

### ❌ **مشاكل شائعة:**

1. **التطبيق لا يبدأ:**
   ```bash
   sudo supervisorctl tail salesapp stderr
   ```

2. **خطأ في قاعدة البيانات:**
   ```bash
   python3 init_db.py
   ```

3. **مشاكل الصلاحيات:**
   ```bash
   sudo chown -R salesapp:salesapp /home/<USER>/sales-inventory-app
   ```

4. **مشاكل Nginx:**
   ```bash
   sudo nginx -t
   sudo systemctl status nginx
   ```

---

## 📞 **الدعم**

للحصول على المساعدة:
1. راجع ملفات السجلات
2. تحقق من حالة الخدمات
3. راجع التوثيق
4. اتصل بفريق الدعم

---

**🎉 تهانينا! تطبيقك الآن جاهز للإنتاج!**
