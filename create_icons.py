#!/usr/bin/env python3
"""
إنشاء أيقونات PWA للتطبيق
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    PIL_AVAILABLE = True
except ImportError:
    PIL_AVAILABLE = False

import os

def create_simple_icon(size, filename):
    """إنشاء أيقونة بسيطة"""
    if not PIL_AVAILABLE:
        print("مكتبة PIL غير متوفرة - سيتم إنشاء ملف نصي بدلاً من الصورة")
        # إنشاء ملف نصي كبديل
        with open(filename, 'w') as f:
            f.write(f"Icon placeholder {size}x{size}")
        return
    
    # إنشاء صورة بخلفية زرقاء
    img = Image.new('RGB', (size, size), color='#007bff')
    draw = ImageDraw.Draw(img)
    
    # رسم دائرة بيضاء في المنتصف
    margin = size // 4
    draw.ellipse([margin, margin, size-margin, size-margin], fill='white')
    
    # إضافة نص
    try:
        # محاولة استخدام خط افتراضي
        font_size = size // 8
        font = ImageFont.load_default()
        text = "POS"
        
        # حساب موضع النص
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2
        
        draw.text((x, y), text, fill='#007bff', font=font)
    except:
        # إذا فشل النص، ارسم مربع صغير
        center = size // 2
        rect_size = size // 6
        draw.rectangle([
            center - rect_size, center - rect_size,
            center + rect_size, center + rect_size
        ], fill='#007bff')
    
    # حفظ الصورة
    img.save(filename, 'PNG')
    print(f"✅ تم إنشاء {filename}")

def create_all_icons():
    """إنشاء جميع الأيقونات المطلوبة"""
    print("🎨 إنشاء أيقونات PWA...")
    
    # إنشاء مجلد الصور إذا لم يكن موجوداً
    os.makedirs('static/images', exist_ok=True)
    
    # قائمة الأيقونات المطلوبة
    icons = [
        (72, 'static/images/icon-72x72.png'),
        (96, 'static/images/icon-96x96.png'),
        (128, 'static/images/icon-128x128.png'),
        (144, 'static/images/icon-144x144.png'),
        (152, 'static/images/icon-152x152.png'),
        (192, 'static/images/icon-192x192.png'),
        (384, 'static/images/icon-384x384.png'),
        (512, 'static/images/icon-512x512.png'),
    ]
    
    for size, filename in icons:
        create_simple_icon(size, filename)
    
    # إنشاء favicon
    create_simple_icon(32, 'static/images/favicon-32x32.png')
    create_simple_icon(16, 'static/images/favicon-16x16.png')
    
    print("🎉 تم إنشاء جميع الأيقونات بنجاح!")

if __name__ == "__main__":
    create_all_icons()
