#!/usr/bin/env python3
"""
اختبار الأزرار التفاعلية JavaScript
"""

import requests
import json
from datetime import datetime

class JavaScriptButtonTester:
    def __init__(self):
        self.session = requests.Session()
        self.base_url = "http://127.0.0.1:5000"
        
    def login(self):
        """تسجيل الدخول"""
        login_data = {'username': 'admin', 'password': 'admin123'}
        response = self.session.post(f"{self.base_url}/login", data=login_data, allow_redirects=False)
        return response.status_code == 302
    
    def test_invoice_creation_flow(self):
        """اختبار تدفق إنشاء فاتورة كاملة"""
        print("\n💰 اختبار تدفق إنشاء فاتورة:")
        
        try:
            # 1. الوصول لصفحة فاتورة جديدة
            response = self.session.get(f"{self.base_url}/sales/new")
            if response.status_code != 200:
                print("  ❌ لا يمكن الوصول لصفحة الفاتورة الجديدة")
                return False
            print("  ✅ صفحة فاتورة جديدة - تعمل")
            
            # 2. اختبار إنشاء فاتورة عبر API
            invoice_data = {
                "customer_id": None,  # عميل نقدي
                "payment_method": "cash",
                "discount": 0,
                "tax": 0,
                "notes": "فاتورة تجريبية",
                "items": [
                    {
                        "product_id": 1,  # هاتف ذكي
                        "quantity": 1,
                        "unit_price": 1200.00
                    }
                ]
            }
            
            response = self.session.post(
                f"{self.base_url}/api/invoices",
                json=invoice_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    invoice_id = result.get('invoice_id')
                    invoice_number = result.get('invoice_number')
                    print(f"  ✅ إنشاء فاتورة - نجح (رقم: {invoice_number})")
                    
                    # 3. اختبار طباعة الفاتورة
                    print_response = self.session.get(f"{self.base_url}/invoices/{invoice_id}/print")
                    if print_response.status_code == 200:
                        print("  ✅ طباعة الفاتورة - تعمل")
                        return True
                    else:
                        print(f"  ❌ طباعة الفاتورة - خطأ {print_response.status_code}")
                        return False
                else:
                    print("  ❌ إنشاء فاتورة - فشل في الحفظ")
                    return False
            else:
                print(f"  ❌ إنشاء فاتورة - خطأ {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ خطأ في تدفق الفاتورة: {e}")
            return False
    
    def test_product_addition_flow(self):
        """اختبار تدفق إضافة منتج"""
        print("\n📦 اختبار تدفق إضافة منتج:")
        
        try:
            # الوصول لصفحة إضافة منتج
            response = self.session.get(f"{self.base_url}/products/add")
            if response.status_code != 200:
                print("  ❌ لا يمكن الوصول لصفحة إضافة منتج")
                return False
            print("  ✅ صفحة إضافة منتج - تعمل")
            
            # محاولة إضافة منتج جديد
            product_data = {
                'name': 'منتج تجريبي',
                'category_id': 1,  # إلكترونيات
                'barcode': f'TEST{datetime.now().strftime("%Y%m%d%H%M%S")}',
                'purchase_price': 100.00,
                'selling_price': 150.00,
                'quantity': 50,
                'min_quantity': 10,
                'unit': 'قطعة',
                'description': 'منتج تجريبي للاختبار'
            }
            
            response = self.session.post(f"{self.base_url}/products/add", data=product_data)
            if response.status_code in [200, 302]:  # نجح أو إعادة توجيه
                print("  ✅ إضافة منتج جديد - يعمل")
                return True
            else:
                print(f"  ❌ إضافة منتج جديد - خطأ {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ خطأ في إضافة منتج: {e}")
            return False
    
    def test_search_functionality(self):
        """اختبار وظائف البحث"""
        print("\n🔍 اختبار وظائف البحث:")
        
        search_tests = [
            ("البحث في المنتجات", "/api/products/search", "هاتف"),
            ("البحث في العملاء", "/api/customers/search", "أحمد"),
        ]
        
        results = []
        for name, endpoint, query in search_tests:
            try:
                response = self.session.get(f"{self.base_url}{endpoint}?q={query}")
                if response.status_code == 200:
                    data = response.json()
                    print(f"  ✅ {name} - يعمل (وجد {len(data)} نتيجة)")
                    results.append(True)
                else:
                    print(f"  ❌ {name} - خطأ {response.status_code}")
                    results.append(False)
            except Exception as e:
                print(f"  ❌ {name} - خطأ: {e}")
                results.append(False)
        
        return all(results)
    
    def test_modal_forms(self):
        """اختبار النماذج المنبثقة"""
        print("\n📝 اختبار النماذج المنبثقة:")
        
        # اختبار إضافة عميل سريع
        try:
            customer_data = {
                'name': f'عميل تجريبي {datetime.now().strftime("%H%M%S")}',
                'phone': '0501234567'
            }
            
            response = self.session.post(
                f"{self.base_url}/api/customers",
                json=customer_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("  ✅ نموذج إضافة عميل سريع - يعمل")
                    modal_customer_works = True
                else:
                    print("  ❌ نموذج إضافة عميل سريع - فشل")
                    modal_customer_works = False
            else:
                print(f"  ❌ نموذج إضافة عميل سريع - خطأ {response.status_code}")
                modal_customer_works = False
        except Exception as e:
            print(f"  ❌ نموذج إضافة عميل سريع - خطأ: {e}")
            modal_customer_works = False
        
        # اختبار إضافة تصنيف
        try:
            category_data = {
                'name': f'تصنيف تجريبي {datetime.now().strftime("%H%M%S")}',
                'description': 'وصف تجريبي'
            }
            
            response = self.session.post(
                f"{self.base_url}/categories/add",
                json=category_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("  ✅ نموذج إضافة تصنيف - يعمل")
                    modal_category_works = True
                else:
                    print("  ❌ نموذج إضافة تصنيف - فشل")
                    modal_category_works = False
            else:
                print(f"  ❌ نموذج إضافة تصنيف - خطأ {response.status_code}")
                modal_category_works = False
        except Exception as e:
            print(f"  ❌ نموذج إضافة تصنيف - خطأ: {e}")
            modal_category_works = False
        
        return modal_customer_works and modal_category_works
    
    def test_reports_functionality(self):
        """اختبار وظائف التقارير"""
        print("\n📊 اختبار وظائف التقارير:")
        
        try:
            # اختبار تقرير المبيعات
            response = self.session.get(f"{self.base_url}/api/reports/sales")
            if response.status_code == 200:
                data = response.json()
                print(f"  ✅ تقرير المبيعات - يعمل (إجمالي: {data.get('total_sales', 0)} ر.س)")
                return True
            else:
                print(f"  ❌ تقرير المبيعات - خطأ {response.status_code}")
                return False
        except Exception as e:
            print(f"  ❌ تقرير المبيعات - خطأ: {e}")
            return False
    
    def test_static_files_loading(self):
        """اختبار تحميل الملفات الثابتة"""
        print("\n📁 اختبار تحميل الملفات الثابتة:")
        
        static_files = [
            ("CSS الرئيسي", "/static/css/style.css"),
            ("JavaScript الرئيسي", "/static/js/app.js"),
            ("Service Worker", "/static/sw.js"),
            ("Web App Manifest", "/static/manifest.json")
        ]
        
        results = []
        for name, path in static_files:
            try:
                response = self.session.get(f"{self.base_url}{path}")
                if response.status_code == 200:
                    print(f"  ✅ {name} - يُحمّل بنجاح")
                    results.append(True)
                else:
                    print(f"  ❌ {name} - خطأ {response.status_code}")
                    results.append(False)
            except Exception as e:
                print(f"  ❌ {name} - خطأ: {e}")
                results.append(False)
        
        return all(results)
    
    def run_comprehensive_test(self):
        """تشغيل اختبار شامل للوظائف التفاعلية"""
        print("🧪 اختبار شامل للوظائف التفاعلية والأزرار")
        print("=" * 60)
        
        # تسجيل الدخول
        if not self.login():
            print("❌ فشل تسجيل الدخول")
            return False
        
        print("✅ تم تسجيل الدخول بنجاح")
        
        # قائمة الاختبارات
        tests = [
            ("تدفق إنشاء فاتورة", self.test_invoice_creation_flow),
            ("تدفق إضافة منتج", self.test_product_addition_flow),
            ("وظائف البحث", self.test_search_functionality),
            ("النماذج المنبثقة", self.test_modal_forms),
            ("وظائف التقارير", self.test_reports_functionality),
            ("تحميل الملفات الثابتة", self.test_static_files_loading)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n🔍 اختبار: {test_name}")
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} - نجح")
                else:
                    print(f"❌ {test_name} - فشل")
            except Exception as e:
                print(f"❌ {test_name} - خطأ: {e}")
            
            print("-" * 40)
        
        # النتائج النهائية
        print("\n" + "=" * 60)
        print(f"📊 نتائج الاختبار الشامل:")
        print(f"✅ نجح: {passed}/{total}")
        print(f"❌ فشل: {total - passed}/{total}")
        print(f"📈 معدل النجاح: {(passed/total)*100:.1f}%")
        
        if passed == total:
            print("\n🎉 جميع الوظائف التفاعلية تعمل بشكل مثالي!")
            return True
        elif passed >= total * 0.8:
            print(f"\n✅ معظم الوظائف تعمل بشكل جيد ({passed}/{total})")
            return True
        else:
            print(f"\n⚠️  بعض الوظائف تحتاج لمراجعة ({total - passed} وظائف)")
            return False

def main():
    """الوظيفة الرئيسية"""
    print(f"🕐 وقت الاختبار: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🌐 عنوان التطبيق: http://127.0.0.1:5000")
    print()
    
    tester = JavaScriptButtonTester()
    success = tester.run_comprehensive_test()
    
    print("\n" + "=" * 60)
    if success:
        print("🏆 جميع الوظائف التفاعلية والأزرار تعمل بشكل ممتاز!")
    else:
        print("🔧 بعض الوظائف تحتاج لمراجعة")
    
    return success

if __name__ == "__main__":
    main()
